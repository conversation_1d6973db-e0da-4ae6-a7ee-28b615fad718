/**
 * 数据分析性能优化工具
 */

export interface DataStats {
  rowCount: number;
  columnCount: number;
  missingValues: Record<string, number>;
  totalMissingCount: number;
  dataHash: string;
}

export interface ColumnStats {
  mean: number;
  median: number;
  std: number;
  count: number;
}

/**
 * 数据分析缓存管理器
 */
export class DataAnalysisCache {
  private cache = new Map<string, DataStats>();
  private columnStatsCache = new Map<string, ColumnStats>();
  private maxCacheSize = 10; // 最大缓存数量

  /**
   * 生成数据哈希值 - 包含数据内容的简单哈希
   */
  generateDataHash(data: any[][], headers: string[]): string {
    // 基础信息
    const basicInfo = `${data.length}-${headers.length}-${JSON.stringify(headers)}`;

    // 如果数据量不大，包含部分数据内容的哈希
    if (data.length <= 1000) {
      const sampleData = data.slice(0, Math.min(100, data.length));
      const dataContent = JSON.stringify(sampleData);
      return `${basicInfo}-${this.simpleHash(dataContent)}`;
    }

    // 对于大数据集，只使用基础信息和时间戳
    return `${basicInfo}-${Date.now()}`;
  }

  /**
   * 简单哈希函数
   */
  private simpleHash(str: string): number {
    let hash = 0;
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32bit integer
    }
    return Math.abs(hash);
  }

  /**
   * 获取缓存的统计信息
   */
  getStats(dataHash: string): DataStats | null {
    return this.cache.get(dataHash) || null;
  }

  /**
   * 设置缓存的统计信息
   */
  setStats(dataHash: string, stats: DataStats): void {
    // 如果缓存已满，删除最旧的条目
    if (this.cache.size >= this.maxCacheSize) {
      const firstKey = this.cache.keys().next().value;
      this.cache.delete(firstKey);
    }
    this.cache.set(dataHash, stats);
  }

  /**
   * 获取列统计缓存
   */
  getColumnStats(key: string): ColumnStats | null {
    return this.columnStatsCache.get(key) || null;
  }

  /**
   * 设置列统计缓存
   */
  setColumnStats(key: string, stats: ColumnStats): void {
    if (this.columnStatsCache.size >= this.maxCacheSize * 5) {
      const firstKey = this.columnStatsCache.keys().next().value;
      this.columnStatsCache.delete(firstKey);
    }
    this.columnStatsCache.set(key, stats);
  }

  /**
   * 清空缓存
   */
  clear(): void {
    this.cache.clear();
    this.columnStatsCache.clear();
  }

  /**
   * 强制刷新 - 清空所有缓存
   */
  forceRefresh(): void {
    this.clear();
    console.log("🔄 数据分析缓存已强制刷新");
  }

  /**
   * 检查缓存是否存在
   */
  hasCache(dataHash: string): boolean {
    return this.cache.has(dataHash);
  }
}

/**
 * 批量数据处理器
 */
export class BatchDataProcessor {
  private batchSize: number;

  constructor(batchSize = 1000) {
    this.batchSize = batchSize;
  }

  /**
   * 批量计算缺失值
   */
  async calculateMissingValues(
    data: any[][],
    headers: string[],
    onProgress?: (progress: number) => void
  ): Promise<Record<string, number>> {
    const missingCounts: Record<string, number> = {};
    const totalCols = headers.length;
    
    for (let colIndex = 0; colIndex < totalCols; colIndex++) {
      const colName = headers[colIndex];
      let count = 0;
      
      // 分批处理数据
      for (let startRow = 0; startRow < data.length; startRow += this.batchSize) {
        const endRow = Math.min(startRow + this.batchSize, data.length);
        
        for (let rowIndex = startRow; rowIndex < endRow; rowIndex++) {
          const value = data[rowIndex][colIndex];
          if (this.isMissingValue(value)) {
            count++;
          }
        }
        
        // 每批处理后让出控制权
        if (startRow + this.batchSize < data.length) {
          await new Promise((resolve) => setTimeout(resolve, 0));
        }
      }
      
      missingCounts[colName] = count;
      
      // 报告进度
      if (onProgress) {
        onProgress((colIndex + 1) / totalCols);
      }
    }

    return missingCounts;
  }

  /**
   * 批量提取数值数据
   */
  async extractNumericValues(
    data: any[][],
    columnIndex: number,
    onProgress?: (progress: number) => void
  ): Promise<number[]> {
    const values: number[] = [];
    const totalRows = data.length;
    
    for (let startRow = 0; startRow < totalRows; startRow += this.batchSize) {
      const endRow = Math.min(startRow + this.batchSize, totalRows);
      
      for (let rowIndex = startRow; rowIndex < endRow; rowIndex++) {
        const value = data[rowIndex][columnIndex];
        
        if (!this.isMissingValue(value)) {
          const numValue = parseFloat(value);
          if (!isNaN(numValue)) {
            values.push(numValue);
          }
        }
      }
      
      // 每批处理后让出控制权
      if (startRow + this.batchSize < totalRows) {
        await new Promise((resolve) => setTimeout(resolve, 0));
      }
      
      // 报告进度
      if (onProgress) {
        onProgress((endRow) / totalRows);
      }
    }

    return values;
  }

  /**
   * 检查是否为缺失值
   */
  private isMissingValue(value: any): boolean {
    return (
      value === null ||
      value === undefined ||
      value === "" ||
      value === "null" ||
      value === "undefined" ||
      (typeof value === "string" && value.trim() === "")
    );
  }
}

/**
 * 统计计算优化器
 */
export class StatsCalculator {
  /**
   * 快速计算基础统计信息
   */
  static calculateBasicStats(values: number[]): ColumnStats {
    if (values.length === 0) {
      return { mean: 0, median: 0, std: 0, count: 0 };
    }

    // 排序用于计算中位数
    const sorted = [...values].sort((a, b) => a - b);
    const count = values.length;
    
    // 计算均值
    const sum = values.reduce((acc, val) => acc + val, 0);
    const mean = sum / count;
    
    // 计算中位数
    const median = count % 2 === 0
      ? (sorted[count / 2 - 1] + sorted[count / 2]) / 2
      : sorted[Math.floor(count / 2)];
    
    // 计算标准差
    const variance = values.reduce((acc, val) => acc + Math.pow(val - mean, 2), 0) / (count - 1);
    const std = Math.sqrt(variance);

    return {
      mean: parseFloat(mean.toFixed(2)),
      median: parseFloat(median.toFixed(2)),
      std: parseFloat(std.toFixed(2)),
      count
    };
  }
}

// 创建全局实例
export const dataAnalysisCache = new DataAnalysisCache();
export const batchDataProcessor = new BatchDataProcessor();

/**
 * 性能监控装饰器
 */
export function measurePerformance<T extends (...args: any[]) => any>(
  target: T,
  name: string
): T {
  return ((...args: any[]) => {
    const start = performance.now();
    const result = target(...args);
    const end = performance.now();
    console.log(`📊 ${name}: ${(end - start).toFixed(2)}ms`);
    return result;
  }) as T;
}

/**
 * 异步性能监控装饰器
 */
export function measureAsyncPerformance<T extends (...args: any[]) => Promise<any>>(
  target: T,
  name: string
): T {
  return (async (...args: any[]) => {
    const start = performance.now();
    const result = await target(...args);
    const end = performance.now();
    console.log(`📊 ${name}: ${(end - start).toFixed(2)}ms`);
    return result;
  }) as T;
}
