from concurrent.futures import <PERSON><PERSON>ool<PERSON>xecutor
from flask import Flask
from pathlib import Path
from logging import Logger

class FlaskWithExecutor(Flask):
    executor: ProcessPoolExecutor
    connected_sids: dict
    url_prefix: str
    temp_folder: Path
    statics_folder: Path
    upload_folder: Path
    projects_folder: Path
    model_path: Path
    logger: Logger
    db_path: Path
    log_dir: Path
    is_rabbitmq_ready: bool