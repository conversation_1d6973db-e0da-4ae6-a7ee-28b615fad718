// composables/useTable.ts
import { ref, computed, watch } from "vue";
import * as XLSX from "xlsx";
import type { DataTableProps, TableColumn } from "../types";

export function useTable(props: DataTableProps) {
  const internalData = ref<any[][]>([]);
  const internalColumns = ref<TableColumn[]>([]);

  // 合并设置 - 添加性能优化配置
  const mergedSettings = computed(() => ({
    data: internalData.value,
    colHeaders: internalColumns.value.map((col) => col.title || col.data),
    columns: internalColumns.value.map((col) => ({
      data: col.data,
      type: col.type || "text",
      readOnly: col.readOnly || false,
      width: col.width || 120, // 设置默认列宽，确保有横向滚动条
      ...col.settings,
    })),
    columnSorting: true,
    rowHeights: 30,
    manualRowMove: true,
    manualColumnMove: true,
    stretchH: "none", // 不拉伸列宽，保持原始宽度以显示横向滚动条
    contextMenu: true,
    language: "zh-CN",
    licenseKey: "non-commercial-and-evaluation",
    // 性能优化配置 - 进一步优化
    renderAllRows: false, // 启用虚拟滚动
    viewportRowRenderingOffset: 30, // 进一步减少视口外渲染行数
    viewportColumnRenderingOffset: 10, // 进一步减少视口外渲染列数
    preventOverflow: false, // 允许溢出以显示滚动条
    preventWheel: false, // 允许鼠标滚轮
    outsideClickDeselects: false, // 点击外部不取消选择
    fillHandle: false, // 禁用填充手柄以提升性能
    autoColumnSize: false, // 禁用自动列宽计算
    autoRowSize: false, // 禁用自动行高计算
    manualColumnResize: true, // 手动列宽调整
    manualRowResize: false, // 禁用手动行高调整
    // 额外的性能优化
    disableVisualSelection: false, // 保持选择功能
    currentRowClassName: null, // 禁用当前行高亮以提升性能
    currentColClassName: null, // 禁用当前列高亮以提升性能
    invalidCellClassName: null, // 禁用无效单元格样式
    readOnlyCellClassName: null, // 禁用只读单元格样式
    commentedCellClassName: null, // 禁用注释单元格样式
    noWordWrapClassName: null, // 禁用文本换行样式
    tableClassName: null, // 禁用表格样式类
    className: null, // 禁用额外样式类
    // 滚动条配置
    scrollH: 'auto', // 水平滚动条自动显示
    scrollV: 'auto', // 垂直滚动条自动显示
    // 渲染优化
    renderer: 'text', // 使用文本渲染器
    trimWhitespace: false, // 不修剪空白字符
    // 禁用一些可能影响性能的功能
    observeChanges: true, // 禁用DOM变化观察
    observeDOMVisibility: false, // 禁用DOM可见性观察
    skipColumnOnPaste: false, // 粘贴时不跳过列
    // 优化滚动性能
    fixedRowsTop: 0, // 固定顶部行数
    fixedRowsBottom: 0, // 固定底部行数
    fixedColumnsLeft: 0, // 固定左侧列数
    fixedColumnsRight: 0, // 固定右侧列数
    // 内存优化
    maxRows: 10000, // 限制最大行数
    maxCols: 100, // 限制最大列数
    ...props.settings,
  }));

  // 是否有数据
  const hasData = computed(() => {
    return internalData.value.length > 0;
  });

  // 更新数据 - 确保创建新数组
  const updateData = (data: any[][]) => {
    internalData.value = data ? [...data] : [];
  };

  // 更新列配置 - 确保创建新数组
  const updateColumns = (columns: TableColumn[]) => {
    internalColumns.value = columns ? [...columns] : [];
  };

  // 获取数据 - 优化版本，支持浅拷贝选项
  const getData = (shallow = false) => {
    return shallow ? internalData.value : [...internalData.value];
  };

  // 获取列配置
  const getColumns = () => {
    return [...internalColumns.value];
  };

  // 导出数据
  const exportData = (filename = `export_${Date.now()}.xlsx`) => {
    if (!hasData.value) return;

    const headers = internalColumns.value.map((col) => col.title || col.data);
    const exportData = [headers, ...internalData.value];

    const ws = XLSX.utils.aoa_to_sheet(exportData);
    const wb = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(wb, ws, "Sheet1");
    XLSX.writeFile(wb, filename);
  };

  // 导入数据
  const importData = async (file: File): Promise<void> => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();

      reader.onload = (e) => {
        try {
          const data = new Uint8Array(e.target?.result as ArrayBuffer);
          const workbook = XLSX.read(data, { type: "array" });
          const firstSheet = workbook.Sheets[workbook.SheetNames[0]];
          const jsonData = XLSX.utils.sheet_to_json(firstSheet, { header: 1 });

          if (jsonData.length > 0) {
            const headers = jsonData[0] as string[];
            const content = jsonData.slice(1) as any[][];

            // 更新列配置
            const columns = headers.map((header, index) => ({
              data: index.toString(),
              title: header,
              type: "text" as const,
            }));
            updateColumns(columns);

            // 更新数据
            updateData(content);
            resolve();
          } else {
            reject(new Error("文件为空"));
          }
        } catch (error) {
          reject(error);
        }
      };

      reader.onerror = () => reject(new Error("文件读取失败"));
      reader.readAsArrayBuffer(file);
    });
  };

  // 监听 props 变化
  watch(
    () => props.data,
    (newData) => {
      if (newData) {
        updateData(newData);
      }
    },
    { immediate: true, deep: true },
  );

  watch(
    () => props.columns,
    (newColumns) => {
      if (newColumns) {
        updateColumns(newColumns);
      }
    },
    { immediate: true, deep: true },
  );

  return {
    mergedSettings,
    hasData,
    updateData,
    updateColumns,
    getData,
    getColumns,
    exportData,
    importData,
  };
}
