import pika, json, logging
from contextlib import contextmanager
from app.types.services import <PERSON>mq<PERSON><PERSON>, RabbitmqPort 
from app.configs import BasicConfig
from flask_socketio import emit

host: RabbitmqHost = BasicConfig.RABBITMQ_HOST
port: RabbitmqPort = BasicConfig.RABBITMQ_PORT

logger = logging.getLogger("rabbitmq")

def check_rabbitmq_service():
    """
    检查RabbitMQ服务是否可用
    
    Returns:
        bool: True表示服务可用，False表示服务不可用
    """
    try:
        connection = pika.BlockingConnection(
            pika.ConnectionParameters(
                host=host, 
                port=port,
                connection_attempts=3,
                retry_delay=1
            )
        )
        connection.close()
        logger.info(f"check_rabbitmq_service: RabbitMQ server check success - host: {host}, port: {port}")
        return True
    except Exception as e:
        logger.error(f"check_rabbitmq_service: RabbitMQ service check failed - host: {host}, port: {port}, error: {str(e)}")
        return False

def setup_rabbitmq():
    # 首先检查RabbitMQ服务是否可用
    if not check_rabbitmq_service():
        logger.warning("setup_rabbitmq: RabbitMQ service is not available, skip RabbitMQ initialization")
        return False
    
    try:
        connection = pika.BlockingConnection(pika.ConnectionParameters(host=host, port=port))
        channel = connection.channel()
        
        channel.exchange_declare(
            exchange='model_build_task',
            exchange_type='direct',
            durable=True
        )

        channel.exchange_declare(
            exchange='search_task',
            exchange_type='direct',
            durable=True
        )

        connection.close()
        logger.info("flask setup_rabbitmq: RabbitMQ initialization success")
        return True
    except Exception as e:
        logger.error(f"flask setup_rabbitmq: RabbitMQ initialization failed: {str(e)}")
        return False

class ProgressReporter(object):
 
    def __init__(self, task_uid: str):
        self.task_uid = task_uid
        self.connection = None
        self.channel = None
        
        try:
            self.connection = pika.BlockingConnection(pika.ConnectionParameters(host=host, port=port))
            self.channel = self.connection.channel()
            logger.info(f"ProgressReporter connect RabbitMQ success - task: {task_uid}")
        except Exception as e:
            logger.error(f"ProgressReporter connect RabbitMQ failed - task: {task_uid}, error: {str(e)}")
    
    def report_model_build_task(self, body: dict, progress: str, status: str, message: str=None):
        report_data = {
            "task_uid": self.task_uid,
            "model_info": body,
            "progress": progress,
            "status": status,
            "message": message
        }
        if self.channel:
            try:
                self.channel.basic_publish(
                    exchange="model_build_task", 
                    routing_key=f"{str(self.task_uid)}", 
                    body=json.dumps(report_data),
                    properties=pika.BasicProperties(
                            delivery_mode=2
                        )
                    )
            except Exception as e:
                logger.error(f"report_model_build_task: send report progress message failed - task: {self.task_uid}, error: {str(e)}")

    def report_search_task(self, body: dict, progress: str, status: str, message: str=None):
        report_data = {
            "task_uid": self.task_uid,
            "search_info": body,
            "progress": progress,
            "status": status,
            "message": message
        }
        if self.channel:
            try:
                self.channel.basic_publish(
                    exchange="search_task",
                    routing_key=f"{str(self.task_uid)}",
                    body=json.dumps(report_data),
                    properties=pika.BasicProperties(
                        delivery_mode=2
                    )
                )
            except Exception as e:
                logger.error(f"report_search_task: send report progress message failed - task: {self.task_uid}, error: {str(e)}")

    def close(self):
        if self.connection:
            try:
                self.connection.close()
            except Exception as e:
                logger.error(f"close: close RabbitMQ connection failed: {str(e)}")


class ProgressConsumer(object):
    """
    消费模型构建任务进度消息的消费者类
    """
    
    def __init__(self, task_uid: str=None, task_id: str=None, callback=None):
        """
        初始化消费者
        
        Args:
            task_uid: 任务UID，用于绑定到特定的routing_key
            callback: 消息处理回调函数，接收message参数
        """
        self.task_uid = task_uid
        self.task_id = task_id
        self.callback = callback
        self.connection = None
        self.channel = None
        self.queue_name = None
        
        self._setup_connection()
    
    def _setup_connection(self):
        """设置RabbitMQ连接和通道"""
        try:
            self.connection = pika.BlockingConnection(pika.ConnectionParameters(host=host, port=port))
            self.channel = self.connection.channel()
            
            # 声明队列
            result = self.channel.queue_declare(queue='', exclusive=True)
            self.queue_name = result.method.queue
            
            # 绑定队列到exchange和routing_key
            if self.task_uid:
                self.channel.queue_bind(
                    exchange='model_build_task',
                    queue=self.queue_name,
                    routing_key=f"{str(self.task_uid)}"
                )
            elif self.task_id:
                self.channel.queue_bind(
                    exchange='search_task',
                    queue=self.queue_name,
                    routing_key=f"{str(self.task_id)}"
                )
            
            # 设置消息处理函数
            self.channel.basic_consume(
                queue=self.queue_name,
                on_message_callback=self._process_message,
                auto_ack=True
            )
            logger.info(f"_setup_connection: ProgressConsumer connect RabbitMQ success - task: {self.task_uid or self.task_id}")
        except Exception as e:
            logger.error(f"_setup_connection: ProgressConsumer connect RabbitMQ failed - task: {self.task_uid or self.task_id}, error: {str(e)}")
            self.connection = None
            self.channel = None
    
    def _process_message(self, ch, method, properties, body):
        """
        处理接收到的消息
        
        Args:
            ch: 通道对象
            method: 方法帧
            properties: 属性
            body: 消息体
        """
        try:
            message = json.loads(body.decode('utf-8'))
            if self.callback:
                self.callback(ch, method, properties, message)
            else:
                logger.info(f"_process_message: receive progress message of task {self.task_uid or self.task_id}: {message}")
        except json.JSONDecodeError as e:
            logger.error(f"_process_message: message parsing failed: {e}")
        except Exception as e:
            logger.error(f"_process_message: error occurred while processing message: {e}")
    
    def start_consuming(self):
        """开始消费消息"""
        if self.channel:
            logger.info(f"start_consuming: start listening to the progress message of task {self.task_uid or self.task_id}...")
            try:
                self.channel.start_consuming()
            except KeyboardInterrupt:
                logger.info("start_consuming: consumer interrupted")
                self.stop_consuming()
        else:
            logger.info("start_consuming: RabbitMQ is not enabled or the connection is not established")
    
    def stop_consuming(self):
        """停止消费消息"""
        if self.channel:
            self.channel.stop_consuming()
    
    def close(self):
        """关闭连接"""
        try:
            if self.channel:
                self.channel.close()
            if self.connection:
                self.connection.close()
        except Exception as e:
            logger.error(f"close: close ProgressConsumer connection failed: {str(e)}")


@contextmanager
def create_progress_consumer(task_uid: str, task_id: str, callback=None):
    """
    创建进度消费者的上下文管理器
    
    Args:
        task_uid: 任务UID
        callback: 消息处理回调函数
    
    Yields:
        ProgressConsumer: 消费者实例
    """
    consumer = ProgressConsumer(task_uid, task_id, callback)
    try:
        yield consumer
    finally:
        consumer.close()
