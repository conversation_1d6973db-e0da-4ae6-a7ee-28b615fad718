from flask import current_app, Blueprint, send_file, make_response, jsonify, request
from app.extensions.socketio import socketio
from app.utils.params import camel_to_snake, snake_to_camel
from app.services.ProgressService import ProgressService
from app.types.flaskapp import FlaskWithExecutor
from app.services.DBService import DBService
from app.services.FileService import FileService
import logging
from typing import cast
from app.routers.models.utils import read_data
import pandas as pd
from app.services.RegressionService import RegressionService
from app.types.model_request import ModelConfig

logger = logging.getLogger("socketio")
common_bp = Blueprint("common", __name__)

@socketio.on('get_model_info')
def get_model_info(data: dict):
    data = camel_to_snake(data) # type: ignore
    task_uid: str = data["task_uid"]
    is_rabbitmq_ready = data.get("is_rabbitmq_ready")
    if is_rabbitmq_ready is None:
        is_rabbitmq_ready = cast(FlaskWithExecutor, current_app).is_rabbitmq_ready
    try:
        if task_uid is not None:
            ProgressService.report(task_uid)
            return_data = {
                "task_uid": task_uid,
                "is_rabbitmq_ready": is_rabbitmq_ready
            }
            return {"code": 200, "msg": "success", "data": snake_to_camel(return_data)}
        else:
            return {"code": 400, "msg": "task_uid is None", "data": None}
    except Exception as e:
        logger.error(f"handle_model_info: 模型信息获取失败: {e}")
        return {"code": 500, "msg": f"模型信息获取失败: {e}", "data": None}
    

@common_bp.route("/download_model", methods=["POST"])
def download_model():
    post_data: dict = camel_to_snake(request.json) # type: ignore
    task_uid = post_data.get("task_uid")
    try:
        if task_uid is not None:
            return send_file(current_app.config["MODEL_PATH"] / f"{task_uid}", as_attachment=True)
        else:
            return make_response(jsonify({"code": 400, "msg": "invalid task_uid", "data": None}), 400)
    except Exception as e:
        logger.error(f"download_model: 模型下载失败: {e}")
        return make_response(jsonify({"code": 500, "msg": f"模型下载失败: {e}", "data": None}), 500)

@common_bp.route("/get_model_list", methods=["GET"])
def get_model_list():
    model_list = DBService.get_model_list()
    compact_model_list = []
    try:
        for model in model_list:
            if model.status != 'completed':
                continue
            tmp_dict = model.to_dict()
            tmp_dict.pop("result")
            params = tmp_dict.pop("params")
            y_train = params.get("y_train")
            y_test = params.get("y_test")
            target_name = list(y_train.keys())[0] if y_train else None
            target_values = []
            if y_train and target_name:
                target_values = list(y_train.get(target_name, {}).values())
                if y_test is not None:
                    target_values.extend(list(y_test.get(target_name, {}).values()))
            tmp_dict["target_values"] = target_values
            tmp_dict["target_name"] = target_name
            compact_model_list.append(tmp_dict)
        return make_response(jsonify({"code": 200, "msg": "success", "data": snake_to_camel(compact_model_list)}), 200)
    except Exception as e:
        logger.error(f"get_model_list: 模型列表获取失败: {e}")
        return make_response(jsonify({"code": 500, "msg": f"模型列表获取失败: {e}", "data": None}), 500)

@common_bp.route("/predict", methods=["POST"])
def predict():
    post_data: dict = request.json # type: ignore
    model_uid = post_data.get("taskUid")
    test_data = post_data.get("data")
    try:
        test_data = pd.DataFrame.from_dict(test_data)
        if model_uid is not None:
            model_dict = FileService.load_model(model_uid)['model']
            model = model_dict['model']
            scaler = model_dict['scaler']
            feature_names = model_dict['feature_names']
            test_data = test_data[feature_names]
            test_data = scaler.transform(test_data)
            predictions = model.predict(test_data)
        return make_response(jsonify({"code": 200, "msg": "success", "data": {
            "predictions": predictions.tolist(),
            "label": model_dict['y_train_name']
        }}), 200)
    except Exception as e:
        logger.error(f"predict: 预测失败: {e}")
        return make_response(jsonify({"code": 500, "msg": f"预测失败: {e}", "data": None}), 500)
    



