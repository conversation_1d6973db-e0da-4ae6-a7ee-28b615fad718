from flask import Blueprint, request, make_response, current_app, jsonify
import logging
from app.utils.params import camel_to_snake, snake_to_camel
from app.types.model_request import ModelRequest, ModelConfig
from app.routers.models.utils import check_model_request
from app.services.DBService import DBService
from app.types.services import ModelTask
from app.services.TaskService import TaskService
from typing import cast
from app.types.flaskapp import FlaskWithExecutor

other_bp = Blueprint("other", __name__)
logger = logging.getLogger()

@other_bp.route("/build", methods=["POST"])
def other_build(): 
    post_data: ModelRequest = camel_to_snake(request.json) # type: ignore
    try:
        model_config: ModelConfig = check_model_request(post_data)
        model_task: ModelTask = DBService.create_model_task(
            task_uid=model_config.task_uid, 
            name=other_bp.name, 
            params=model_config
        )
        DBService.add_model_task(model_task=model_task)
        logger.info(f"other_build: 添加模型任务: {model_task.task_uid}")

        TaskService.submit_model_task(model_config)
        return_data = {
            "task_uid": model_task.task_uid,
            "asynchronous": model_config.asynchronous,
            "is_rabbitmq_ready": cast(FlaskWithExecutor, current_app).is_rabbitmq_ready
        }
        return make_response(jsonify({"code": 200, "msg": "success", "data": snake_to_camel(return_data)}), 200)
    except Exception as e:
        logger.error(f"other_build: 添加模型任务失败: {e}")
        return make_response(jsonify({"code": 500, "msg": f"添加模型任务失败: {e}", "data": None}), 500)