from app.types.search_request import SearchRequest, SearchConfig
from app.types.flaskapp import FlaskWithExecutor
from flask import current_app
from typing import cast

def check_search_request(search_request: SearchRequest) -> SearchConfig:
    """
    检查搜索请求
    """
    task_uid = search_request.get("task_uid")
    target = search_request.get("target")
    target_name = target.get("name")
    target_value = target.get("value")
    search = search_request.get("search")
    alg_name = search.get("algorithm")
    alg_param = search.get("params")
    return SearchConfig(
        task_id=None,
        task_uid=task_uid,
        target_name=target_name,
        target_value=target_value,
        alg_name=alg_name,
        alg_param=alg_param,
        asynchronous=True,
        is_rabbitmq_ready=cast(FlaskWithExecutor, current_app).is_rabbitmq_ready
    )