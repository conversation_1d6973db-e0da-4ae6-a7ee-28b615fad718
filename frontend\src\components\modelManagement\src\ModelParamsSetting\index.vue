<template>
  <div class="params-setting-container" :class="{ 'optimizer-layout': isOptimizer }">
    <!-- 评估设置 - 仅在非优化算法时显示 -->
    <div v-if="!isOptimizer" class="evaluation-container">
      <span class="param-card-title">模型评估设置</span>
      <div class="evaluation-settings-wrapper">
        <!-- 划分训练测试集部分 -->
        <div class="param-section">
          <el-checkbox v-model="evaluationParams.splitDataset">划分训练/测试集</el-checkbox>
          <div v-if="evaluationParams.splitDataset" class="param-content">
            <div class="param-item">
              <span class="param-label">训练集比例：</span>
              <el-slider
                v-model="evaluationParams.trainRatio"
                :min="evaluationConfig.trainRatio?.min || 50"
                :max="evaluationConfig.trainRatio?.max || 90"
                :step="evaluationConfig.trainRatio?.step || 5"
                show-input
              />
            </div>
            <div class="param-item">
              <span class="param-label">随机种子：</span>
              <el-input-number
                v-model="evaluationParams.randomState"
                :min="evaluationConfig.randomState?.min || 0"
                :max="evaluationConfig.randomState?.max || 1000"
                controls-position="right"
              />
            </div>
          </div>
        </div>

        <!-- 交叉验证部分 -->
        <div class="param-section">
          <el-checkbox v-model="evaluationParams.useModelValidation">检验模型（交叉验证）</el-checkbox>
          <div v-if="evaluationParams.useModelValidation" class="param-content">
            <div class="param-item">
              <el-radio-group v-model="evaluationParams.validationType">
                <el-radio value="k-fold">k折交叉验证</el-radio>
                <el-radio value="leave-one-out">留一法验证</el-radio>
              </el-radio-group>
            </div>
            <div v-if="evaluationParams.validationType === 'k-fold'" class="param-item">
              <span class="param-label">k值：</span>
              <el-input-number
                v-model="evaluationParams.kFolds"
                :min="evaluationConfig.kFolds?.min || 2"
                :max="evaluationConfig.kFolds?.max || 10"
                controls-position="right"
              />
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 模型参数设置 -->
    <div class="param-card evaluation-container" :class="{ 'full-width': isOptimizer }">
      <div class="param-card-header">
        <span class="param-card-title">{{ isOptimizer ? '优化算法参数设置' : '模型参数设置' }}</span>
        <div class="param-switches">
          <el-switch
            v-if="!isOptimizer"
            v-model="isOptimizationEnabled"
            active-text="启用超参数优化"
            :disabled="false"
            @change="handleOptimizationToggle"
          />
          <el-switch
            v-model="useCustomParams"
            active-text="自定义参数"
            inactive-text="使用预设"
            @change="handleCustomParamsToggle"
            :disabled="isOptimizationEnabled && !isOptimizer"
          />
        </div>
      </div>

      <div class="evaluation-settings-wrapper" style="display: block">
        <div class="param-section">
          <!-- 预设参数模式 -->
          <div v-if="!useCustomParams && modelConfig" class="preset-params">
            <div v-for="(paramConfig, key) in modelConfig.defaultParams" :key="key" class="param-item">
              <span class="param-label" :title="paramConfig.description">
                {{ paramConfig.displayName }}：
              </span>
              <div class="param-control">
                <!-- 优化：范围输入 - 仅在非优化算法且启用了超参数优化时显示 -->
                <div v-if="!isOptimizer && isOptimizationEnabled && paramConfig.type === 'number' && paramConfig.optimizeParam" class="range-input">
                  <el-input-number
                    v-model="algorithmParamRanges[key].min"
                    :min="paramConfig.min"
                    :max="paramConfig.max"
                    :step="paramConfig.step || 0.001"
                    :precision="getPrecision(paramConfig.step)"
                    controls-position="right"
                    placeholder="最小值"
                  />
                  <span class="range-separator">至</span>
                  <el-input-number
                    v-model="algorithmParamRanges[key].max"
                    :min="paramConfig.min"
                    :max="paramConfig.max"
                    :step="paramConfig.step || 0.001"
                    :precision="getPrecision(paramConfig.step)"
                    controls-position="right"
                    placeholder="最大值"
                  />
                  <span class="range-separator">步长</span>
                  <el-input-number
                    v-model="algorithmParamRanges[key].step"
                    :min="0"
                    :step="paramConfig.step || 0.001"
                    :precision="getPrecision(paramConfig.step)"
                    controls-position="right"
                    placeholder="步长"
                  />
                </div>
                <!-- 标准：单值输入 -->
                <!-- 数字输入 -->
                <el-input-number
                  v-else-if="paramConfig.type === 'number'"
                  v-model="algorithmParams[key]"
                  :min="paramConfig.min"
                  :max="paramConfig.max"
                  :step="paramConfig.step || 0.1"
                  :precision="getPrecision(paramConfig.step)"
                  controls-position="right"
                  :placeholder="paramConfig.nullable ? '留空表示自动' : ''"
                  clearable
                />
                <!-- 布尔开关 -->
                <el-switch
                  v-else-if="paramConfig.type === 'boolean'"
                  v-model="algorithmParams[key]"
                />
                <!-- 选择器 -->
                <el-select
                  v-else-if="paramConfig.type === 'select'"
                  v-model="algorithmParams[key]"
                  placeholder="请选择"
                  :multiple="!isOptimizer && isOptimizationEnabled"
                  collapse-tags
                  :disabled="paramConfig.options.length === 0"
                >
                  <el-option
                    v-for="option in paramConfig.options"
                    :key="option.value"
                    :label="option.label"
                    :value="option.value"
                  />
                </el-select>
                <!-- 文本输入 -->
                <el-input
                  v-else
                  v-model="algorithmParams[key]"
                  :placeholder="paramConfig.description"
                />
              </div>
            </div>
          </div>

          <!-- 自定义参数模式 -->
          <div v-else class="custom-params">
            <div class="param-header">
              <span>自定义{{ isOptimizer ? '优化算法' : '模型' }}参数</span>
              <el-button
                link
                size="small"
                @click="resetToPreset"
                :disabled="!modelConfig"
              >
                重置为预设
              </el-button>
            </div>
            <div class="param-content-input">
              <el-input
                v-model="customParamsText"
                type="textarea"
                :rows="8"
                :placeholder="customParamsPlaceholder"
                @blur="validateCustomParams"
                @input="clearCustomParamsError"
              />
              <div v-if="customParamsError" class="error-message">
                {{ customParamsError }}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, watch, computed, onMounted } from "vue";
import { ElMessage } from "element-plus";
import { ModelMetaEvaluation, ModelAlgorithm } from "@/types/models";
import type { ModelParamConfig } from "@/utils/modelParamsLoader";
import { loadModelParams } from "@/utils/modelParamsLoader";
import { useWindowSize } from '@vueuse/core';

const emit = defineEmits<{
  (e: "update:params", value: {
    algorithm: ModelAlgorithm;
    evaluation: ModelMetaEvaluation;
  }): void;
}>();

const props = defineProps<{
  algorithmName: string;
  isOptimizer?: boolean; // 新增：是否为优化算法
}>();

const loading = ref(false);
const modelConfig = ref<ModelParamConfig | null>(null);
const evaluationConfig = ref<Record<string, any>>({});

// State management from BaseParamsSetting
const useCustomParams = ref(false);
const customParamsText = ref("");
const customParamsError = ref("");
const isOptimizationEnabled = ref(false);

// Algorithm parameters from BaseParamsSetting
const algorithmParams = reactive<Record<string, any>>({});
const algorithmParamRanges = reactive<Record<string, { min: number | undefined; max: number | undefined; step: number | undefined }>>({});

// Evaluation parameters from BaseParamsSetting
interface EvaluationParams {
  splitDataset: boolean;
  trainRatio: number;
  randomState: number;
  useModelValidation: boolean;
  validationType: "k-fold" | "leave-one-out";
  kFolds: number;
}

const evaluationParams = reactive<EvaluationParams>({
  splitDataset: false,
  trainRatio: 70,
  randomState: 42,
  useModelValidation: false,
  validationType: "k-fold",
  kFolds: 5,
});

// Computed properties from BaseParamsSetting
const finalAlgorithmParams = computed(() => {
  if (useCustomParams.value) {
    try {
      const customParams = JSON.parse(customParamsText.value || "{}");
      if (typeof customParams === 'object' && customParams !== null && !Array.isArray(customParams)) {
        return customParams;
      }
      return {};
    } catch {
      return {};
    }
  }
  
  const filteredParams: Record<string, any> = {};
  Object.entries(algorithmParams).forEach(([key, value]) => {
    if (value !== null && value !== undefined) {
      if (isOptimizationEnabled.value && Array.isArray(value) && value.length === 0) {
        // 在优化模式下，不提交空数组
      } else {
        filteredParams[key] = value;
      }
    }
  });

  // 如果启用了优化，合并范围参数
  if (isOptimizationEnabled.value) {
    Object.entries(algorithmParamRanges).forEach(([key, range]) => {
      if (range.min !== undefined && range.max !== undefined && range.step !== undefined) {
        filteredParams[key] = [range.min, range.max, range.step];
      }
    });
    filteredParams.optimize = true;
  }

  return filteredParams;
});

const finalEvaluationParams = computed((): ModelMetaEvaluation => {
  if (props.isOptimizer) {
    return {}; // 优化算法不包含评估参数
  }
  return {
    cv: evaluationParams.useModelValidation && evaluationParams.validationType === "k-fold"
      ? { k: evaluationParams.kFolds, randomState: evaluationParams.randomState }
      : undefined,
    loocv: evaluationParams.useModelValidation && evaluationParams.validationType === "leave-one-out",
    test: evaluationParams.splitDataset
      ? { size: 1 - evaluationParams.trainRatio / 100, randomState: evaluationParams.randomState }
      : undefined,
  };
});

// Methods from BaseParamsSetting
const handleCustomParamsToggle = (value: boolean) => {
  if (value) {
    customParamsText.value = JSON.stringify(algorithmParams, null, 2);
    customParamsError.value = "";
  } else {
    validateCustomParams();
  }
};

const validateCustomParams = () => {
  if (!useCustomParams.value || !customParamsText.value.trim()) {
    customParamsError.value = "";
    return true;
  }
  try {
    const parsed = JSON.parse(customParamsText.value);
    if (typeof parsed !== 'object' || parsed === null || Array.isArray(parsed)) {
      throw new Error("自定义参数必须是JSON对象");
    }
    customParamsError.value = "";
    return true;
  } catch (e) {
    customParamsError.value = (e as Error).message;
    return false;
  }
};

const clearCustomParamsError = () => {
  customParamsError.value = "";
};

const getPrecision = (step: number | undefined): number => {
  if (step === undefined) return 3;
  const stepString = step.toString();
  const decimalIndex = stepString.indexOf('.');
  return decimalIndex === -1 ? 0 : stepString.length - decimalIndex - 1;
};

const resetToPreset = () => {
  if (!modelConfig.value) return;
  initializeParams();
  ElMessage.success("已重置为预设参数");
};

const resetParams = async () => {
  useCustomParams.value = false;
  customParamsText.value = "";
  customParamsError.value = "";
  modelConfig.value = null;
  evaluationConfig.value = {};
  Object.assign(evaluationParams, {
    splitDataset: false,
    trainRatio: 70,
    randomState: 42,
    useModelValidation: false,
    validationType: "k-fold",
    kFolds: 5,
  });
  Object.keys(algorithmParams).forEach(key => delete algorithmParams[key]);
  await loadModelConfiguration();
  console.log('ModelParamsSetting: modelConfig after loadModelConfiguration:', modelConfig.value);
  console.log('ModelParamsSetting: evaluationConfig after loadModelConfiguration:', evaluationConfig.value);
};

const forceReloadConfig = async () => {
  useCustomParams.value = false;
  customParamsText.value = "";
  customParamsError.value = "";
  modelConfig.value = null;
  evaluationConfig.value = {};
  Object.assign(evaluationParams, {
    splitDataset: false,
    trainRatio: 70,
    randomState: 42,
    useModelValidation: false,
    validationType: "k-fold",
    kFolds: 5,
  });
  Object.keys(algorithmParams).forEach(key => delete algorithmParams[key]);
  await loadModelConfiguration();
};

// 自定义参数输入框占位符（根据模型类型动态变化）
const customParamsPlaceholder = computed(() => {
  return `请输入合法 JSON，例如：{ "param": 1 }`;
});

// 根据 algorithmName 加载模型配置与默认参数
const loadModelConfiguration = async () => {
  if (!props.algorithmName) return;

  try {
    loading.value = true;

    // 1. 加载配置文件
    const cfg = await loadModelParams(props.algorithmName as any);
    modelConfig.value = cfg;

    // 2. 初始化算法参数
    Object.keys(algorithmParams).forEach((k) => delete algorithmParams[k]);
    Object.keys(algorithmParamRanges).forEach(key => delete algorithmParamRanges[key]);
    Object.entries(cfg.defaultParams).forEach(([key, paramCfg]) => {
      algorithmParams[key] = (paramCfg as any).value;
      if (paramCfg.type === 'number') {
        algorithmParamRanges[key] = { 
          min: (paramCfg as any).min, 
          max: (paramCfg as any).max,
          step: (paramCfg as any).step ?? 0.001
        };
      }
    });

    // 3. 如果不是优化算法，才初始化评估参数
    if (!props.isOptimizer) {
      // 初始化评估参数配置与默认值
      evaluationConfig.value = cfg.evaluation || {};
      Object.keys(evaluationParams).forEach((key) => {
        const ec = (evaluationConfig.value as any)[key];
        if (ec && ec.value !== undefined) {
          (evaluationParams as any)[key] = ec.value;
        }
      });
    }

    // 完成后向父组件同步一次参数
    emitParamsUpdate();
  } catch (err) {
    console.error("loadModelConfiguration error:", err);
    ElMessage.error("加载模型配置失败");
  } finally {
    loading.value = false;
  }
};

// 向父组件同步最新参数
const emitParamsUpdate = () => {
  emit("update:params", {
    algorithm: {
      name: props.algorithmName,
      params: finalAlgorithmParams.value,
    },
    evaluation: finalEvaluationParams.value,
  });
};

// 获取参数方法 - 供外部调用
const getParams = () => {
  return finalAlgorithmParams.value;
};

// 监听关键依赖，实时向父组件同步
watch(
  () => [finalAlgorithmParams.value, finalEvaluationParams.value, props.algorithmName, useCustomParams.value, customParamsText.value],
  emitParamsUpdate,
  { deep: true }
);

// Watchers
watch(
  () => props.algorithmName,
  () => {
    loadModelConfiguration();
  },
  { immediate: true }
);

watch(
  () => modelConfig.value, // Watch the entire modelConfig object
  (newConfig) => {
    console.log('ModelParamsSetting: modelConfig.value watcher triggered. newConfig:', newConfig);
    if (newConfig && newConfig.defaultParams) {
      // Reset algorithmParams based on new modelConfig.defaultParams
      Object.keys(algorithmParams).forEach(key => delete algorithmParams[key]); // Clear existing
      Object.keys(algorithmParamRanges).forEach(key => delete algorithmParamRanges[key]);
      Object.entries(newConfig.defaultParams).forEach(([key, paramConfig]) => {
        algorithmParams[key] = (paramConfig as any).value;
        if (paramConfig.type === 'number') {
          algorithmParamRanges[key] = { 
            min: (paramConfig as any).min, 
            max: (paramConfig as any).max,
            step: (paramConfig as any).step ?? 0.001
          };
        }
      });
    }
  },
  { immediate: true, deep: true } // Immediate to run on initial load, deep to watch nested changes
);

// Expose for parent component calls
defineExpose({
  resetParams,
  forceReloadConfig,
  finalAlgorithmParams,
  finalEvaluationParams,
  getParams, // 导出获取参数方法
});

onMounted(() => {
  loadModelConfiguration();
});

// 处理超参数优化开关切换
const handleOptimizationToggle = (val: boolean) => {
  if (!val) {
    // 关闭优化时，将范围参数的当前值恢复到单值参数
    Object.entries(algorithmParamRanges).forEach(([key, range]) => {
      if (typeof range.min === 'number') algorithmParams[key] = range.min;
    });
  } else {
    // 打开优化时，从 modelConfig 重新加载范围参数的默认值
    if (modelConfig.value?.defaultParams) {
      Object.entries(modelConfig.value.defaultParams).forEach(
        ([key, paramConfig]) => {
          if (paramConfig.type === 'number') {
            algorithmParamRanges[key] = {
              min: (paramConfig as any).min,
              max: (paramConfig as any).max,
              step: (paramConfig as any).step ?? 0.001,
            };
          }
        }
      );
    }
  }
};

// 添加窗口大小监听
const { width } = useWindowSize();
const isWideScreen = computed(() => width.value >= 1200); // 当窗口宽度大于等于1200px时使用两列布局
</script>

<style scoped>
/* Styles from BaseParamsSetting */
.params-setting-container {
  padding: 0;
  display: flex;
  flex-direction: column;
  gap: 20px; /* space between cards */
  flex-wrap: wrap;
  width: 100%;
}

/* 横向排列优化算法参数卡片 */
.params-setting-container.optimizer-layout {
  display: flex !important;
  flex-wrap: wrap !important;
}
.params-setting-container.optimizer-layout .param-card {
  flex: 1 1 45% !important;
  margin: 10px !important;
}

.evaluation-container {
  flex: 1;
  min-width: 400px;
}

.param-card {
  margin-bottom: 0; /* 移除底部边距，使用grid gap控制间距 */
}

.param-card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.param-card-title {
  font-size: 16px;
  font-weight: bold;
}

.param-switches {
  display: flex;
  gap: 16px;
  flex-wrap: wrap; /* 允许在窄屏时换行 */
}

.param-section {
  margin-bottom: 0;
  padding: 1rem;
  flex: 1;
}

.param-content {
  padding-left: 20px;
  margin-top: 10px;
}

.param-item {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
  flex-wrap: wrap; /* 允许在窄屏时换行 */
  gap: 8px; /* 添加间距 */
}

.param-label {
  width: 120px;
  flex-shrink: 0;
  margin-right: 10px;
  font-size: 14px;
  color: var(--el-text-color-regular);
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.param-control {
  min-width: 200px; /* 确保控件有足够的最小宽度 */
}

.custom-params .param-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.error-message {
  color: var(--el-color-danger);
  font-size: 12px;
  margin-top: 5px;
}

.evaluation-settings-wrapper {
  display: flex;
  gap: 20px;
  margin-top: 1rem;
  background: #fbfbfb;
  border-radius: 4px;
  flex: 1;
}

.range-input {
  display: flex;
  align-items: center;
  gap: 8px;
  flex-wrap: wrap; /* 允许在窄屏时换行 */
  width: 100%;
}

.range-input .el-input-number {
  width: calc(33.33% - 16px); /* 减去间距的一半 */
  min-width: 100px; /* 设置最小宽度 */
}

.range-separator {
  color: #606266;
  font-size: 14px;
}

/* 在窄屏下优化开关的布局 */
@media screen and (max-width: 480px) {
  .param-card-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }

  .param-switches {
    width: 100%;
  }

  .param-label {
    width: 100%; /* 在极窄的屏幕上标签占满宽度 */
    margin-bottom: 4px;
  }

  .param-control {
    width: 100%;
  }
}

/* 当是优化算法时，参数卡片占用全宽 */
.param-card.full-width {
  grid-column: 1 / -1;
}

/* 横向排列预设参数，默认两列 */
.preset-params {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
}
.preset-params .param-item {
  /* 默认两列布局 */
  flex: 1 1 calc(50% - 16px);
  /* 保证标签和控件在同一行显示所需的最小宽度 */
  min-width: 380px;
}

/* 横向排列预设参数，仅在优化算法模式 */
.params-setting-container.optimizer-layout :deep(.preset-params) {
  display: flex !important;
  flex-wrap: wrap !important;
  gap: 16px;
}
.params-setting-container.optimizer-layout :deep(.preset-params .param-item) {
  /* 优化模式下三列布局 */
  flex: 0 1 calc(33.33% - 16px) !important;
  min-width: 120px;
}
</style> 