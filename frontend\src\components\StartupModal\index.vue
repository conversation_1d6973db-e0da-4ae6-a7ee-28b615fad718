<script setup lang="ts">
import { ref } from "vue";
import { ElMessage } from "element-plus";
import { useRouter } from "vue-router";
import { useWorkspaceStoreHook } from "@/store/modules/workspace";
import { useAppStoreHook } from "@/store/modules/app";

import StartMenuSvg from "@/assets/svg/start_menu.svg?component";
import LogoNoTitle from "@/assets/svg/logo_no_title.svg?component";
import FileColoredIcon from "@/assets/svg/file_colored.svg?component";
import FolderColoredIcon from "@/assets/svg/folder_colored.svg?component";
import CloseIcon from "@/assets/svg/close.svg?component";

const router = useRouter();
const workspaceStore = useWorkspaceStoreHook();
const appStore = useAppStoreHook();

const closeStartupModal = () => {
  appStore.setShowStartupModal(false);
};

const openDirectory = async () => {
  try {
    const path = await window.ipcRenderer.invoke("dialog:openDirectory");
    if (path) {
      workspaceStore.setWorkspacePath(path);
      if (workspaceStore.clearSingleFileMode) workspaceStore.clearSingleFileMode();
      router.push(`/workspace/${encodeURIComponent(path)}`);
      closeStartupModal();
    }
  } catch (error) {
    ElMessage.error("选择目录失败");
  }
};

const addFile = async () => {
  try {
    const filePath = await window.ipcRenderer.invoke("dialog:openFile");
    if (filePath) {
      const isExcelFile = /\.(xlsx|xls|csv)$/i.test(filePath);
      if (isExcelFile) {
        workspaceStore.setCurrentFile(filePath);
        workspaceStore.markDataAsSaved?.(filePath);
        await router.push(`/dataManagement/imandex/${encodeURIComponent(filePath)}`);
      } else {
        const fileDir = filePath.substring(0, filePath.lastIndexOf("/") !== -1 ? filePath.lastIndexOf("/") : filePath.lastIndexOf("\\"));
        if (!workspaceStore.getCurrentWorkspacePath) {
          workspaceStore.addFileToWorkspace?.(filePath);
        }
        workspaceStore.setCurrentFile(filePath);
        await router.push(`/workspace/${encodeURIComponent(fileDir)}`);
        setTimeout(() => {
          window.ipcRenderer.send('workspace-file-selected', filePath);
        }, 300);
      }
      closeStartupModal();
    }
  } catch (error) {
    ElMessage.error("打开文件失败");
  }
};
</script>

<template>
  <div v-if="appStore.showStartupModal" class="startup-overlay">
    <div class="startup-content">
      <StartMenuSvg class="startup-svg" />
      <div class="close-button" @click="closeStartupModal">
        <CloseIcon />
      </div>
      <div class="feature-text">
        研发单位：<br>中国航天科技集团有限公司<br>第四研究院四十二所
      </div>
      <div class="logo-buttons-container">
        <div class="action-buttons">
          <button class="workspace-action-button" @click="openDirectory">
            <FolderColoredIcon class="button-svg-icon" />
            <span>打开目录</span>
          </button>
          <button class="workspace-action-button" @click="addFile">
            <FileColoredIcon class="button-svg-icon" />
            <span>添加文件</span>
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
/* 启动浮层样式 */
.startup-overlay {
  position: fixed;
  top: 70px;
  /* 留出标题栏空间 */
  left: 0;
  width: 100vw;
  height: calc(100vh - 30px);
  background-color: rgba(0, 0, 0, 0.15);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
}

/* 深色模式下的启动浮层 */
html.dark .startup-overlay {
  background-color: rgba(0, 0, 0, 0.9);
}

.startup-content {
  position: relative;
  width: 650px;
  max-width: 90vw;
  display: flex;
  justify-content: center;
  align-items: center;
}

.startup-svg {
  width: 100%;
  height: auto;
}

.close-button {
  position: absolute;
  top: 15px;
  right: 15px;
  width: 30px;
  height: 30px;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  font-size: 24px;
  color: black;
}


.close-button svg {
  width: 20px;
  height: 20px;
}

.feature-text {
  position: absolute;
  left: 40px;
  bottom: 100px;
  color: white;
  font-size: 14px;
  max-width: 180px;
  text-align: left;
  line-height: 1.5;
}

.logo-no-title {
  height: auto;
}

.logo-buttons-container {
  position: absolute;
  left: 80px;
  bottom: 40px;
  display: flex;
  align-items: center;
}

.action-buttons {
  display: flex;
  flex-direction: row;
  margin-left: 258px;
  gap: 10px;
}

.workspace-action-button {
  width: 118px;
  height: 32px;
  border-radius: 4px;
  background: #FAFAFA;
  box-sizing: border-box;
  border: 1px solid #005DFF;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  cursor: pointer;
  color: #005DFF;
  font-size: 14px;
  transition: all 0.3s;
}

.workspace-action-button:hover {
  background: rgb(233, 241, 255);
}

.workspace-action-button:active {
  background: rgb(214, 229, 255);
}

.button-svg-icon {
  width: 14px;
  height: 14px;
  display: inline-block;
}

/* 深色模式适配 */
html.dark .workspace-action-button {
  background: #2A2A2A;
  border-color: #005DFF;
  color: #409EFF;
}

html.dark .workspace-action-button:hover {
  background: rgba(0, 93, 255, 0.1);
}
</style>