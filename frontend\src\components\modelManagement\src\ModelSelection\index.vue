<template>
  <ReTable :data="tableData">
    <template #default="{ data: paginatedData }">
      <el-table
        :data="paginatedData"
        :max-height="350"
        style="width: 100%"
        size="default"
        :border="false"
        row-key="name"
        class="variable-selection-table"
      >
        <el-table-column label="全选" width="80" align="center">
          <template #header>
            <el-checkbox
              v-model="isAllSelected"
              :indeterminate="isIndeterminate"
              @change="handleSelectAll"
            />
          </template>
          <template #default="scope">
            <el-checkbox
              v-model="scope.row.isSelected"
              @change="handleRowSelection(scope.row)"
            />
          </template>
        </el-table-column>

        <!-- 变量名称列 -->
        <el-table-column prop="name" label="变量名称" min-width="150" />

        <!-- 序号变量列 -->
        <el-table-column label="序号变量" min-width="90" align="center">
          <template #default="scope">
            <el-checkbox
              v-model="scope.row.isIndex"
              :disabled="!scope.row.isSelected"
              @change="handleTypeSelect(scope.row, 'isIndex')"
            />
          </template>
        </el-table-column>

        <!-- 目标变量列 -->
        <el-table-column label="目标变量" min-width="90" align="center">
          <template #default="scope">
            <el-checkbox
              v-model="scope.row.isTarget"
              :disabled="!scope.row.isSelected"
              @change="handleTypeSelect(scope.row, 'isTarget')"
            />
          </template>
        </el-table-column>

        <!-- 自变量列 -->
        <el-table-column label="自变量" min-width="90" align="center">
          <template #default="scope">
            <el-checkbox
              v-model="scope.row.isIndependent"
              :disabled="!scope.row.isSelected"
              @change="handleTypeSelect(scope.row, 'isIndependent')"
            />
          </template>
        </el-table-column>

        <!-- 注释变量列 -->
        <el-table-column label="注释变量" min-width="90" align="center">
          <template #default="scope">
            <el-checkbox
              v-model="scope.row.isNote"
              :disabled="!scope.row.isSelected"
              @change="handleTypeSelect(scope.row, 'isNote')"
            />
          </template>
        </el-table-column>
      </el-table>
    </template>
  </ReTable>
</template>

<script lang="ts" setup>
import { ref, watch, computed } from "vue";
import ReTable from "@/components/ReTable/index.vue";

interface VariableRow {
  name: string;
  isSelected: boolean;
  isIndependent: boolean;
  isTarget: boolean;
  isNote: boolean;
  isIndex: boolean;
}

const props = defineProps<{
  headers: any[];
  modelType?: string;
}>();

const emit = defineEmits<{
  (
    e: "update:selection",
    value: {
      features: string[];
      target: string[];
      deletes: string[];
      index: string[];
      all: string[];
    }
  ): void;
}>();

const tableData = ref<VariableRow[]>([]);

const isAllSelected = computed(() =>
  tableData.value.length > 0 && tableData.value.every(row => row.isSelected)
);

const isIndeterminate = computed(() => {
  const selectedCount = tableData.value.filter(row => row.isSelected).length;
  return selectedCount > 0 && selectedCount < tableData.value.length;
});

const updateSelection = () => {
  const result = {
    features: tableData.value
      .filter(row => row.isSelected && row.isIndependent)
      .map(row => row.name),
    target: tableData.value
      .filter(row => row.isSelected && row.isTarget)
      .map(row => row.name),
    deletes: tableData.value
      .filter(row => row.isSelected && row.isNote)
      .map(row => row.name),
    index: tableData.value
      .filter(row => row.isSelected && row.isIndex)
      .map(row => row.name),
    all: tableData.value.map(row => row.name)
  };
  emit("update:selection", result);
};

const initTableData = (headers: any[]) => {
  if (headers?.length) {
    tableData.value = headers.map((header, index) => {
      const name = typeof header === 'object' ? header.prop || header.title : header;
      const row: VariableRow = {
        name: name,
        isSelected: true,
        isIndependent: false,
        isTarget: false,
        isNote: false,
        isIndex: false
      };
      // Default selection logic
      if (index === 0) {
        row.isIndex = true;
      } else if (index === 1) {
        // The second column is the target variable
        row.isTarget = true;
      } else {
        row.isIndependent = true;
      }
      return row;
    });
    updateSelection();
  }
};

watch(() => props.headers, initTableData, { immediate: true, deep: true });

const handleSelectAll = (val: boolean) => {
  tableData.value.forEach(row => {
    row.isSelected = val;
  });
  updateSelection();
};

const handleRowSelection = (row: VariableRow) => {
  if (!row.isSelected) {
    row.isIndependent = false;
    row.isTarget = false;
    row.isNote = false;
    row.isIndex = false;
  }
  updateSelection();
};

const handleTypeSelect = (row: VariableRow, type: keyof VariableRow) => {
  if (row[type]) {
    const types: (keyof VariableRow)[] = [
      "isIndependent",
      "isTarget",
      "isNote",
      "isIndex"
    ];
    types.forEach(t => {
      if (t !== type) {
        (row[t as keyof VariableRow] as boolean) = false;
      }
    });
  }
  updateSelection();
};

const reset = () => {
  initTableData(props.headers);
};

defineExpose({
  reset
});
</script>

<style scoped>
/* All table styles have been moved to the ReTable component */
</style> 