export default {
  path: "/workspace",
  meta: {
    icon: "ri:folder-3-line",
    title: "工作区",
    showLink: false, // Hide from main navigation since it's accessed via sidebar
    rank: 10
  },
  children: [
    {
      path: "/workspace/:workspacePath(.*)",
      name: "Workspace",
      component: () => import("@/views/workspace/index.vue"),
      meta: {
        title: "工作区",
        showLink: false
      }
    },
    {
      path: "/workspace/file/:filePath(.*)",
      name: "WorkspaceFile",
      component: () => import("@/views/workspace/index.vue"),
      meta: {
        title: "文件",
        showLink: true
      }
    }
  ]
} satisfies RouteConfigsTable;
