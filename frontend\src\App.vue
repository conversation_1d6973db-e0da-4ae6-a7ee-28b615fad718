<template>
  <el-config-provider :locale="zhCn">
    <router-view />
    <ReDialog />
    <StartupModal v-if="route.name === 'Welcome'" />
  </el-config-provider>
</template>

<script setup lang="ts">
import { onMounted, onUnmounted } from "vue";
import { ElConfigProvider } from "element-plus";
import { ReDialog } from "@/components/ReDialog";
import StartupModal from "@/components/StartupModal/index.vue";
import { useRoute } from "vue-router";
import zhCn from "element-plus/es/locale/lang/zh-cn";
import { getSocket } from "@/utils/socket";

defineOptions({
  name: "App"
});

const route = useRoute();

const handleBeforeUnload = () => {
  const socket = getSocket();
  if (socket) {
    socket.disconnect();
  }
};

onMounted(() => {
  window.addEventListener("beforeunload", handleBeforeUnload);
});

onUnmounted(() => {
  window.removeEventListener("beforeunload", handleBeforeUnload);
});
</script>
