"use strict";
const node_os = require("node:os");
const node_url = require("node:url");
const node_path = require("node:path");
const electron = require("electron");
const node_child_process = require("node:child_process");
require("node:fs");
const fs = require("fs");
const path = require("path");
const node_module = require("node:module");
var _documentCurrentScript = typeof document !== "undefined" ? document.currentScript : null;
async function checkExistingBackendProcesses(processName = "runserver.exe") {
  return new Promise((resolve) => {
    if (process.platform !== "win32") {
      resolve([]);
      return;
    }
    const cmd = `wmic process where "name='${processName}'" get processid`;
    node_child_process.exec(cmd, (error, stdout) => {
      if (error) {
        console.log(
          "No existing backend processes found or error running command"
        );
        resolve([]);
        return;
      }
      const pidPattern = /(\d+)/g;
      const pids = [];
      let match;
      while ((match = pidPattern.exec(stdout)) !== null) {
        const pid = parseInt(match[1], 10);
        if (!isNaN(pid) && pid !== process.pid) {
          pids.push(pid);
        }
      }
      console.log(`Found ${pids.length} existing backend processes:`, pids);
      resolve(pids);
    });
  });
}
async function checkExistingRabbitMQProcesses() {
  return new Promise((resolve) => {
    if (process.platform !== "win32") {
      resolve([]);
      return;
    }
    const cmd = `wmic process where "name='erl.exe' or name='epmd.exe'" get processid,name`;
    node_child_process.exec(cmd, (error, stdout) => {
      if (error) {
        console.log(
          "No existing RabbitMQ processes found or error running command"
        );
        resolve([]);
        return;
      }
      const processLines = stdout.trim().split("\n").slice(1);
      const pids = [];
      for (const line of processLines) {
        const match = line.trim().match(/(\d+)\s+(.+)/);
        if (match) {
          const pid = parseInt(match[1], 10);
          const name = match[2].trim();
          if (!isNaN(pid) && pid !== process.pid) {
            console.log(
              `Found RabbitMQ related process: ${name} (PID: ${pid})`
            );
            pids.push(pid);
          }
        }
      }
      console.log(`Found ${pids.length} existing RabbitMQ processes`);
      resolve(pids);
    });
  });
}
const __filename$1 = node_url.fileURLToPath(typeof document === "undefined" ? require("url").pathToFileURL(__filename).href : _documentCurrentScript && _documentCurrentScript.tagName.toUpperCase() === "SCRIPT" && _documentCurrentScript.src || new URL("index.js", document.baseURI).href);
const __dirname$1 = node_path.dirname(__filename$1);
process.env.DIST_ELECTRON = node_path.join(__dirname$1, "..");
process.env.DIST = node_path.join(process.env.DIST_ELECTRON, "../dist");
process.env.PUBLIC = process.env.VITE_DEV_SERVER_URL ? node_path.join(process.env.DIST_ELECTRON, "../public") : process.env.DIST;
const isDev = process.env["NODE_ENV"] === "development";
if (node_os.release().startsWith("6.1")) electron.app.disableHardwareAcceleration();
if (process.platform === "win32") electron.app.setAppUserModelId(electron.app.getName());
if (!electron.app.requestSingleInstanceLock()) {
  electron.app.quit();
  process.exit(0);
}
let win = null;
const runningBackendProcesses = [];
let isShuttingDown = false;
const pendingModelCompleteEvents = /* @__PURE__ */ new Map();
const preload = node_path.join(__dirname$1, "../preload/index.js");
const url = process.env.VITE_DEV_SERVER_URL;
const indexHtml = node_path.join(process.env.DIST, "index.html");
async function killProcess(pid) {
  return new Promise((resolve) => {
    if (!pid || pid <= 0) {
      resolve(false);
      return;
    }
    console.log(`Attempting to kill process with PID: ${pid}`);
    try {
      if (process.platform === "win32") {
        node_child_process.exec(`taskkill /pid ${pid} /T /F`, (err) => {
          if (err) {
            console.error(`Failed to kill process ${pid}:`, err);
            resolve(false);
          } else {
            console.log(`Successfully terminated process ${pid}`);
            const index = runningBackendProcesses.indexOf(pid);
            if (index !== -1) {
              runningBackendProcesses.splice(index, 1);
            }
            resolve(true);
          }
        });
      } else {
        try {
          process.kill(pid, "SIGTERM");
          setTimeout(() => {
            try {
              process.kill(pid, 0);
              process.kill(pid, "SIGKILL");
              console.log(`Had to use SIGKILL for ${pid}`);
            } catch (e) {
            }
            const index = runningBackendProcesses.indexOf(pid);
            if (index !== -1) {
              runningBackendProcesses.splice(index, 1);
            }
            resolve(true);
          }, 1e3);
        } catch (e) {
          console.error(`Failed to kill process ${pid}:`, e);
          resolve(false);
        }
      }
    } catch (error) {
      console.error(`Error in killProcess for PID ${pid}:`, error);
      resolve(false);
    }
  });
}
async function terminateAllBackendProcesses() {
  console.log("Terminating all backend processes...");
  const processes = [...runningBackendProcesses];
  for (const pid of processes) {
    await killProcess(pid);
  }
  const remainingPids = await checkExistingBackendProcesses();
  for (const pid of remainingPids) {
    await killProcess(pid);
  }
}
async function terminateAllRabbitMQProcesses() {
  console.log("Terminating all RabbitMQ processes...");
  try {
    const remainingPids = await checkExistingRabbitMQProcesses();
    console.log(
      `Found ${remainingPids.length} remaining RabbitMQ processes to terminate`
    );
    for (const pid of remainingPids) {
      await killProcess(pid);
    }
  } catch (error) {
    console.error(`Error checking for remaining RabbitMQ processes:`, error);
  }
}
function createMenu(label = "进入全屏幕") {
  const menu = electron.Menu.buildFromTemplate(
    appMenu(label)
  );
  electron.Menu.setApplicationMenu(menu);
}
async function createMainWindow(initialRoute) {
  win = new electron.BrowserWindow({
    width: 1024,
    height: 768,
    minWidth: 1024,
    minHeight: 768,
    title: "Main window",
    icon: node_path.join(process.env.PUBLIC, "favicon.ico"),
    frame: false,
    // Remove default window frame
    transparent: true,
    // Enable transparency
    resizable: true,
    // Allow window resizing
    webPreferences: {
      preload,
      nodeIntegration: false,
      // Recommended for security
      contextIsolation: true
      // Required for contextBridge
    }
  });
  win.maximize();
  const targetUrl = initialRoute ? `${url}#${initialRoute}` : url;
  const targetIndexHtml = initialRoute ? { pathname: indexHtml, hash: initialRoute } : indexHtml;
  if (process.env.VITE_DEV_SERVER_URL) {
    win.loadURL(targetUrl);
    win.webContents.openDevTools({ mode: "bottom" });
  } else {
    win.loadFile(
      typeof targetIndexHtml === "string" ? targetIndexHtml : targetIndexHtml.pathname,
      typeof targetIndexHtml === "string" ? {} : { hash: targetIndexHtml.hash }
    );
  }
  createMenu();
  win.webContents.on("did-finish-load", () => {
    win == null ? void 0 : win.webContents.send("main-process-message", (/* @__PURE__ */ new Date()).toLocaleString());
  });
  win.webContents.setWindowOpenHandler(({ url: url2 }) => {
    const isModelResultWindow = url2.includes("/modelManagement/");
    const childWindow = new electron.BrowserWindow({
      width: 1024,
      height: 768,
      minWidth: 1024,
      minHeight: 768,
      autoHideMenuBar: true,
      // 隐藏菜单栏，防止出现bug
      frame: !isModelResultWindow,
      // 对模型结果页面使用无边框窗口
      transparent: isModelResultWindow,
      // 为模型结果窗口启用透明
      backgroundColor: isModelResultWindow ? "#00000000" : "#fff",
      // 透明背景
      webPreferences: {
        preload,
        nodeIntegration: false,
        contextIsolation: true
      },
      ...isModelResultWindow ? {
        show: false
        // 先不显示，等加载完成后再显示
      } : {}
    });
    childWindow.loadURL(url2);
    if (isModelResultWindow) {
      childWindow.once("ready-to-show", () => {
        childWindow.show();
      });
    }
    return { action: "deny" };
  });
  win.on("enter-full-screen", () => {
    createMenu("退出全屏幕");
  });
  win.on("leave-full-screen", () => {
    createMenu();
  });
}
const taskWindowMap = /* @__PURE__ */ new Map();
const printEventData = (eventName, data) => {
  console.log(`[IPC Event] ${eventName} - 接收数据:`, data);
  console.log(`[IPC Event] ${eventName} - taskUid类型:`, typeof data.taskUid);
  console.log(`[IPC Event] ${eventName} - taskUid值:`, data.taskUid);
  if (data.taskUid === void 0) {
    console.log(`[IPC Event] ${eventName} - 警告: taskUid未定义!`);
  }
};
electron.ipcMain.on("model_progress_update", (event, data) => {
  printEventData("model_progress_update", data);
  const taskUid = data.taskUid;
  const targetWindow = taskWindowMap.get(taskUid);
  console.log(
    `Progress update for task ${taskUid}, target window exists: ${!!targetWindow}`
  );
  if (targetWindow && !targetWindow.isDestroyed()) {
    targetWindow.webContents.send("model_progress_update", data);
  } else {
    console.log(
      `未找到窗口，当前映射中的taskUid有:`,
      Array.from(taskWindowMap.keys())
    );
  }
});
electron.ipcMain.on("model_complete", (event, data) => {
  console.log(data);
  const taskUid = data.taskUid;
  const targetWindow = taskWindowMap.get(taskUid);
  console.log(
    `Model complete for task ${taskUid}, target window exists: ${!!targetWindow}`
  );
  pendingModelCompleteEvents.set(taskUid, data);
  if (targetWindow && !targetWindow.isDestroyed()) {
    targetWindow.webContents.send("model_complete", data);
  }
});
electron.ipcMain.on("open_model_result_window", (event, data) => {
  printEventData("open_model_result_window", data);
  const taskUid = data.taskUid;
  if (!taskUid) {
    console.error("无效的taskUid:", data.taskUid);
    return;
  }
  console.log(
    `Registering window for task ${taskUid} from open_model_result_window event`
  );
  const sourceWindow = electron.BrowserWindow.fromWebContents(event.sender);
  if (sourceWindow) {
    taskWindowMap.set(taskUid, sourceWindow);
  }
});
electron.ipcMain.on("model_result_window_ready", (event, data) => {
  printEventData("model_result_window_ready", data);
  const taskUid = data.taskUid;
  if (!taskUid) {
    console.error("无效的taskUid:", data.taskUid);
    return;
  }
  console.log(`Model result window ready for task ${taskUid}`);
  const resultWindow = electron.BrowserWindow.fromWebContents(event.sender);
  if (resultWindow) {
    taskWindowMap.set(taskUid, resultWindow);
    console.log(`Model result window registered for task ID: ${taskUid}`);
    const pendingData = pendingModelCompleteEvents.get(taskUid);
    if (pendingData) {
      resultWindow.webContents.send("model_complete", pendingData);
      pendingModelCompleteEvents.delete(taskUid);
    }
  }
});
electron.ipcMain.on("request_model_info", async (event, data) => {
  printEventData("request_model_info", data);
  const taskUid = data.taskUid;
  if (!taskUid) {
    console.error("无效的taskUid:", data.taskUid);
    event.sender.send("model_info_response", {
      code: 400,
      msg: "无效的任务ID"
    });
    return;
  }
  console.log(`处理模型信息请求，taskUid: ${taskUid}`);
  const resultWindow = electron.BrowserWindow.fromWebContents(event.sender);
  if (resultWindow) {
    taskWindowMap.set(taskUid, resultWindow);
  }
  if (!win || win.isDestroyed()) {
    console.error("主窗口不可用");
    event.sender.send("model_info_response", {
      code: 500,
      msg: "主窗口不可用，无法执行WebSocket请求"
    });
    return;
  }
  try {
    const result = await win.webContents.executeJavaScript(`
      (function() {
        return new Promise((resolve) => {
          const socket = window.socketInstance;
          if (!socket || !socket.connected) {
            resolve({ code: 500, msg: 'WebSocket未连接' });
            return;
          }
          
          console.log('在主窗口中发送get_model_info请求, taskUid: ' + '${taskUid}');
          socket.emit('get_model_info', { taskUid: '${taskUid}' }, (response) => {
            console.log('主窗口收到get_model_info响应:', response);
            resolve(response || { code: 404, msg: '未收到响应' });
          });
          
          // 设置超时
          setTimeout(() => {
            resolve({ code: 408, msg: '请求超时' });
          }, 10000);
        });
      })()
    `);
    console.log("从主窗口获取到模型信息结果:", result);
    event.sender.send("model_info_response", result);
  } catch (error) {
    console.error("执行WebSocket请求失败:", error);
    event.sender.send("model_info_response", {
      code: 500,
      msg: "执行请求失败: " + (error.message || "未知错误")
    });
  }
});
electron.app.whenReady().then(async () => {
  console.log("App ready, starting services...");
  console.log("All services started successfully, creating UI...");
  createMainWindow("/welcome");
});
electron.app.on("window-all-closed", () => {
  if (electron.BrowserWindow.getAllWindows().length === 0) {
    electron.app.quit();
  }
});
electron.app.on("before-quit", async (event) => {
  const BackendEnabled = "false";
  if (!isShuttingDown && BackendEnabled === "true") {
    event.preventDefault();
    isShuttingDown = true;
    console.log("Application is quitting, terminating all services...");
    const terminateWithTimeout = async (terminateFunc, name, timeout) => {
      return Promise.race([
        terminateFunc(),
        new Promise((resolve) => {
          setTimeout(() => {
            console.warn(`${name} termination timeout after ${timeout}ms`);
            resolve();
          }, timeout);
        })
      ]);
    };
    try {
      await Promise.all([
        terminateWithTimeout(
          async () => await terminateAllRabbitMQProcesses(),
          "RabbitMQ",
          5e3
        ),
        terminateWithTimeout(
          async () => await terminateAllBackendProcesses(),
          "Backend",
          5e3
        )
      ]);
      console.log("All processes terminated successfully");
    } catch (error) {
      console.error("Error during service termination:", error);
    }
    try {
      const remainingBackendPids = await checkExistingBackendProcesses();
      const remainingRabbitMQPids = await checkExistingRabbitMQProcesses();
      const allRemainingPids = [
        ...remainingBackendPids,
        ...remainingRabbitMQPids
      ];
      if (allRemainingPids.length > 0) {
        console.log(
          `Found ${allRemainingPids.length} remaining processes, attempting final cleanup...`
        );
        await Promise.all(allRemainingPids.map((pid) => killProcess(pid)));
      }
    } catch (error) {
      console.error("Error during final process cleanup:", error);
    }
    setTimeout(() => {
      console.log("Clean exit completed, quitting application");
      electron.app.exit(0);
    }, 1e3);
  }
});
process.on("exit", () => {
});
process.on("uncaughtException", async (error) => {
  console.error("Uncaught exception:", error);
  isShuttingDown = true;
  const cleanup = async () => {
    console.log("Performing emergency cleanup due to uncaught exception");
    const promises = [];
    try {
      const backendPids = await checkExistingBackendProcesses();
      for (const pid of backendPids) {
        promises.push(killProcess(pid));
      }
      const rabbitmqPids = await checkExistingRabbitMQProcesses();
      for (const pid of rabbitmqPids) {
        promises.push(killProcess(pid));
      }
      await Promise.race([
        Promise.all(promises),
        new Promise((resolve) => setTimeout(resolve, 3e3))
        // 3秒超时
      ]);
      console.log("Emergency cleanup completed");
    } catch (e) {
      console.error("Emergency cleanup error:", e);
    }
  };
  await cleanup();
  electron.app.exit(1);
});
process.on("unhandledRejection", async (reason, promise) => {
  console.error("Unhandled Promise rejection:", reason);
});
electron.app.on("second-instance", () => {
  if (win && !win.isDestroyed()) {
    if (win.isMinimized()) win.restore();
    win.focus();
  }
});
electron.app.on("activate", () => {
  const allWindows = electron.BrowserWindow.getAllWindows();
  if (allWindows.length === 0) {
    createMainWindow("/welcome");
  } else {
    if (win && !win.isDestroyed()) {
      if (win.isMinimized()) win.restore();
      win.focus();
    } else {
      allWindows[0].focus();
    }
  }
});
electron.ipcMain.on(
  "APP_READY_TO_SHOW_MAIN_WINDOW",
  (event, args = {}) => {
    createMainWindow(args.targetRoute);
    if (args.openedFilePath) {
      console.log("Main process: Preparing to send file data:", {
        filePath: args.openedFilePath,
        targetRoute: args.targetRoute,
        singleFileMode: args.singleFileMode
      });
      const sendFileData = () => {
        var _a;
        console.log("Main process: Sending file data events");
        if ((_a = args.targetRoute) == null ? void 0 : _a.includes("/dataManagement/imandex")) {
          console.log("Main process: Sending excel-file-selected event");
          win == null ? void 0 : win.webContents.send("excel-file-selected", args.openedFilePath);
        } else {
          console.log("Main process: Sending workspace-file-selected event");
          win == null ? void 0 : win.webContents.send("workspace-file-selected", args.openedFilePath);
        }
        if (args.singleFileMode) {
          console.log("Main process: Sending set-single-file-mode event");
          win == null ? void 0 : win.webContents.send("set-single-file-mode", args.openedFilePath);
        }
      };
      win == null ? void 0 : win.webContents.once("did-finish-load", sendFileData);
      win == null ? void 0 : win.webContents.once("dom-ready", sendFileData);
      setTimeout(sendFileData, 1e3);
    }
  }
);
const appMenu = (fullscreenLabel) => {
  const devMenuItems = [];
  if (isDev) {
    devMenuItems.push(
      { label: "开发者工具", role: "toggleDevTools" },
      { label: "强制刷新", role: "forceReload" }
    );
  }
  const template = [
    {
      label: "文件",
      submenu: [
        {
          label: "导入项目...",
          accelerator: "CmdOrCtrl+Shift+O",
          // 添加动效提示
          toolTip: "导入现有项目文件夹",
          click: async () => {
            if (win && !win.isDestroyed()) {
              win.focus();
              win.webContents.send("menu-triggered-import-project");
            } else {
              const dialogOptions = {
                properties: ["openDirectory"]
              };
              const directoryPathResult = await electron.dialog.showOpenDialog(dialogOptions);
              if (!directoryPathResult.canceled && directoryPathResult.filePaths.length > 0) {
                const projectPath = directoryPathResult.filePaths[0];
                createMainWindow(
                  `/workspace/${encodeURIComponent(projectPath)}`
                );
              }
            }
          }
        },
        {
          label: "打开文件...",
          accelerator: "CmdOrCtrl+O",
          toolTip: "打开单个文件进行编辑",
          click: async () => {
            if (win && !win.isDestroyed()) {
              win.focus();
              const currentURL = win.webContents.getURL();
              console.log("Current window URL:", currentURL);
              const isDataImportPage = currentURL.includes(
                "/dataManagement/imandex"
              );
              if (isDataImportPage) {
                console.log(
                  "Sending menu-triggered-open-file to dataImandEx page"
                );
                win.webContents.send("menu-triggered-open-file");
              } else {
                const dialogOptions = {
                  properties: ["openFile"]
                };
                const filePathResult = await electron.dialog.showOpenDialog(dialogOptions);
                if (!filePathResult.canceled && filePathResult.filePaths.length > 0) {
                  const filePath = filePathResult.filePaths[0];
                  const isExcelFile = /\.(xlsx|xls|csv)$/i.test(filePath);
                  if (isExcelFile) {
                    createMainWindow(`/dataManagement/imandex`);
                    const sendExcelFile = () => {
                      win == null ? void 0 : win.webContents.send("excel-file-selected", filePath);
                      win == null ? void 0 : win.webContents.send("set-single-file-mode", filePath);
                    };
                    win == null ? void 0 : win.webContents.once("did-finish-load", sendExcelFile);
                    win == null ? void 0 : win.webContents.once("dom-ready", sendExcelFile);
                    setTimeout(sendExcelFile, 1e3);
                  } else {
                    const fileDir = filePath.substring(
                      0,
                      filePath.lastIndexOf("/") || filePath.lastIndexOf("\\")
                    );
                    createMainWindow(
                      `/workspace/${encodeURIComponent(fileDir)}`
                    );
                    const sendWorkspaceFile = () => {
                      win == null ? void 0 : win.webContents.send(
                        "workspace-file-selected",
                        filePath
                      );
                      win == null ? void 0 : win.webContents.send("set-single-file-mode", filePath);
                    };
                    win == null ? void 0 : win.webContents.once("did-finish-load", sendWorkspaceFile);
                    win == null ? void 0 : win.webContents.once("dom-ready", sendWorkspaceFile);
                    setTimeout(sendWorkspaceFile, 1e3);
                  }
                }
              }
            } else {
              const dialogOptions = {
                properties: ["openFile"]
              };
              const filePathResult = await electron.dialog.showOpenDialog(dialogOptions);
              if (!filePathResult.canceled && filePathResult.filePaths.length > 0) {
                const filePath = filePathResult.filePaths[0];
                const isExcelFile = /\.(xlsx|xls|csv)$/i.test(filePath);
                if (isExcelFile) {
                  createMainWindow(`/dataManagement/imandex`);
                  const sendExcelFile = () => {
                    win == null ? void 0 : win.webContents.send("excel-file-selected", filePath);
                    win == null ? void 0 : win.webContents.send("set-single-file-mode", filePath);
                  };
                  win == null ? void 0 : win.webContents.once("did-finish-load", sendExcelFile);
                  win == null ? void 0 : win.webContents.once("dom-ready", sendExcelFile);
                  setTimeout(sendExcelFile, 1e3);
                } else {
                  const fileDir = filePath.substring(
                    0,
                    filePath.lastIndexOf("/") || filePath.lastIndexOf("\\")
                  );
                  createMainWindow(`/workspace/${encodeURIComponent(fileDir)}`);
                  const sendWorkspaceFile = () => {
                    win == null ? void 0 : win.webContents.send("workspace-file-selected", filePath);
                    win == null ? void 0 : win.webContents.send("set-single-file-mode", filePath);
                  };
                  win == null ? void 0 : win.webContents.once("did-finish-load", sendWorkspaceFile);
                  win == null ? void 0 : win.webContents.once("dom-ready", sendWorkspaceFile);
                  setTimeout(sendWorkspaceFile, 1e3);
                }
              }
            }
          }
        },
        { type: "separator" },
        {
          label: "退出",
          role: "quit",
          accelerator: "CmdOrCtrl+Q"
        }
      ]
    },
    {
      label: "编辑",
      submenu: [
        {
          label: "撤销",
          role: "undo",
          accelerator: "CmdOrCtrl+Z"
        },
        {
          label: "重做",
          role: "redo",
          accelerator: "CmdOrCtrl+Shift+Z"
        },
        { type: "separator" },
        {
          label: "剪切",
          role: "cut",
          accelerator: "CmdOrCtrl+X"
        },
        {
          label: "复制",
          role: "copy",
          accelerator: "CmdOrCtrl+C"
        },
        {
          label: "粘贴",
          role: "paste",
          accelerator: "CmdOrCtrl+V"
        },
        {
          label: "删除",
          role: "delete",
          accelerator: "Delete"
        },
        {
          label: "全选",
          role: "selectAll",
          accelerator: "CmdOrCtrl+A"
        }
      ]
    },
    {
      label: "显示",
      submenu: [
        {
          label: "放大",
          role: "zoomIn",
          accelerator: "CmdOrCtrl+Plus"
        },
        {
          label: "默认大小",
          role: "resetZoom",
          accelerator: "CmdOrCtrl+0"
        },
        {
          label: "缩小",
          role: "zoomOut",
          accelerator: "CmdOrCtrl+-"
        },
        { type: "separator" },
        {
          label: fullscreenLabel,
          role: "togglefullscreen",
          accelerator: "F11"
        }
      ]
    },
    // 将开发者工具菜单添加到这里（仅在开发模式下）
    ...isDev ? [
      {
        label: "开发",
        submenu: devMenuItems
      }
    ] : [],
    // 关于菜单放在最后
    {
      label: "关于",
      submenu: [
        {
          label: "关于应用",
          role: "about",
          accelerator: "F1"
        }
      ]
    }
  ];
  return template;
};
electron.ipcMain.handle("open-win", (_, arg) => {
  const childWindow = new electron.BrowserWindow({
    webPreferences: {
      preload,
      nodeIntegration: false,
      // Then these should also be updated
      contextIsolation: true
    }
  });
  if (process.env.VITE_DEV_SERVER_URL) {
    childWindow.loadURL(`${url}#${arg}`);
  } else {
    childWindow.loadFile(indexHtml, { hash: arg });
  }
});
const require$1 = node_module.createRequire(typeof document === "undefined" ? require("url").pathToFileURL(__filename).href : _documentCurrentScript && _documentCurrentScript.tagName.toUpperCase() === "SCRIPT" && _documentCurrentScript.src || new URL("index.js", document.baseURI).href);
electron.ipcMain.handle("dialog:openDirectory", async () => {
  const result = await electron.dialog.showOpenDialog({
    properties: ["openDirectory"]
  });
  return result.filePaths[0];
});
electron.ipcMain.handle("dialog:openFile", async () => {
  const result = await electron.dialog.showOpenDialog({
    properties: ["openFile"]
    // You can add filters, e.g., for specific file types
    // filters: [
    //   { name: 'Text Files', extensions: ['txt', 'md'] },
    //   { name: 'All Files', extensions: ['*'] }
    // ]
  });
  if (result.canceled || result.filePaths.length === 0) {
    return null;
  }
  return result.filePaths[0];
});
electron.ipcMain.handle("fs:readDirectory", async (_, dirPath) => {
  const files = await fs.promises.readdir(dirPath, { withFileTypes: true });
  return files.map((dirent) => ({
    name: dirent.name,
    isDirectory: dirent.isDirectory(),
    path: path.join(dirPath, dirent.name)
  }));
});
electron.ipcMain.handle("fs:createDirectory", async (_, targetPath) => {
  await fs.promises.mkdir(targetPath, { recursive: true });
  return { success: true };
});
electron.ipcMain.handle("fs:createFile", async (_, filePath) => {
  await fs.promises.writeFile(filePath, "");
  return { success: true };
});
electron.ipcMain.handle("fs:deletePath", async (_, targetPath) => {
  const stats = await fs.promises.stat(targetPath);
  if (stats.isDirectory()) {
    await fs.promises.rmdir(targetPath, { recursive: true });
  } else {
    await fs.promises.unlink(targetPath);
  }
  return { success: true };
});
const getFileType = (filePath) => {
  const ext = path.extname(filePath).toLowerCase();
  const textExtensions = [
    ".txt",
    ".md",
    ".json",
    ".xml",
    ".html",
    ".css",
    ".js",
    ".ts",
    ".vue",
    ".py",
    ".java",
    ".cpp",
    ".c",
    ".h",
    ".sql",
    ".log",
    ".ini",
    ".cfg",
    ".conf",
    ".yaml",
    ".yml"
  ];
  const officeExtensions = [".doc", ".docx", ".xls", ".xlsx", ".ppt", ".pptx"];
  const pdfExtensions = [".pdf"];
  const imageExtensions = [
    ".jpg",
    ".jpeg",
    ".png",
    ".gif",
    ".bmp",
    ".svg",
    ".ico"
  ];
  const archiveExtensions = [".zip", ".rar", ".7z", ".tar", ".gz"];
  const executableExtensions = [".exe", ".msi", ".dmg", ".app", ".deb", ".rpm"];
  const mediaExtensions = [".mp3", ".mp4", ".avi", ".mov", ".wav", ".flac"];
  if (textExtensions.includes(ext)) {
    return { type: ext, category: "text", supported: true };
  } else if (officeExtensions.includes(ext)) {
    return { type: ext, category: "office", supported: true };
  } else if (pdfExtensions.includes(ext)) {
    return { type: ext, category: "pdf", supported: true };
  } else if (imageExtensions.includes(ext)) {
    return { type: ext, category: "image", supported: true };
  } else if (archiveExtensions.includes(ext)) {
    return { type: ext, category: "archive", supported: false };
  } else if (executableExtensions.includes(ext)) {
    return { type: ext, category: "executable", supported: false };
  } else if (mediaExtensions.includes(ext)) {
    return { type: ext, category: "media", supported: false };
  } else {
    return { type: ext, category: "unknown", supported: false };
  }
};
electron.ipcMain.handle("fs:readFile", async (_, filePath) => {
  try {
    const content = await fs.promises.readFile(filePath, "utf-8");
    return content;
  } catch (error) {
    console.error("Error reading file:", error);
    throw error;
  }
});
electron.ipcMain.handle("fs:readFileWithType", async (_, filePath) => {
  try {
    const fileInfo = getFileType(filePath);
    if (!fileInfo.supported) {
      return {
        success: false,
        fileInfo,
        error: `不支持的文件类型: ${fileInfo.type}`,
        message: getUnsupportedMessage(fileInfo)
      };
    }
    let content = "";
    let imageData = null;
    if (fileInfo.category === "text") {
      content = await fs.promises.readFile(filePath, "utf-8");
    } else if (fileInfo.category === "office") {
      if (fileInfo.type === ".docx") {
        content = await extractDocxText(filePath);
      } else if (fileInfo.type === ".doc") {
        content = "暂不支持 .doc 格式，请转换为 .docx 格式";
      } else {
        content = `不支持的Office 文档 (${fileInfo.type})，请使用Office工具进行编辑`;
      }
    } else if (fileInfo.category === "pdf") {
      content = "PDF 文档，暂不支持文本提取";
    } else if (fileInfo.category === "image") {
      const imageBuffer = await fs.promises.readFile(filePath);
      const base64Data = imageBuffer.toString("base64");
      const mimeType = getMimeType(fileInfo.type);
      imageData = `data:${mimeType};base64,${base64Data}`;
      content = "";
    }
    return {
      success: true,
      fileInfo,
      content,
      imageData
    };
  } catch (error) {
    console.error("Error reading file with type:", error);
    return {
      success: false,
      fileInfo: getFileType(filePath),
      error: (error == null ? void 0 : error.message) || "Unknown error",
      message: "读取文件时发生错误"
    };
  }
});
const getMimeType = (extension) => {
  const mimeTypes = {
    ".jpg": "image/jpeg",
    ".jpeg": "image/jpeg",
    ".png": "image/png",
    ".gif": "image/gif",
    ".bmp": "image/bmp",
    ".svg": "image/svg+xml",
    ".ico": "image/x-icon",
    ".webp": "image/webp"
  };
  return mimeTypes[extension.toLowerCase()] || "image/jpeg";
};
const getUnsupportedMessage = (fileInfo) => {
  switch (fileInfo.category) {
    case "image":
      return "图片文件不支持文本编辑，请使用图片查看器打开";
    case "archive":
      return "压缩文件不支持直接编辑，请先解压缩";
    case "executable":
      return "可执行文件不支持编辑";
    case "media":
      return "音视频文件不支持文本编辑，请使用媒体播放器打开";
    default:
      return "不支持的文件类型，无法在文本编辑器中打开";
  }
};
const extractDocxText = async (filePath) => {
  try {
    try {
      const mammoth = require$1("mammoth");
      const result = await mammoth.extractRawText({ path: filePath });
      return result.value || "无法提取文档内容";
    } catch (mammothError) {
      console.log("Mammoth not available, using fallback method");
      try {
        const AdmZip = require$1("adm-zip");
        const zip = new AdmZip(filePath);
        const documentXml = zip.readAsText("word/document.xml");
        if (documentXml) {
          const textContent = documentXml.replace(/<[^>]*>/g, " ").replace(/\s+/g, " ").trim();
          return textContent || "文档内容为空";
        }
      } catch (zipError) {
        console.log(
          "ZIP extraction failed:",
          (zipError == null ? void 0 : zipError.message) || "Unknown error"
        );
      }
      return `Word 文档预览

文件路径: ${filePath}

注意：无法提取文档内容，请使用 Microsoft Word 或其他兼容软件打开此文件。

要完整支持 Word 文档，请安装 mammoth 库：npm install mammoth`;
    }
  } catch (error) {
    console.error("Error extracting DOCX text:", error);
    return `Word 文档预览

文件路径: ${filePath}

错误：无法读取文档内容 - ${(error == null ? void 0 : error.message) || "Unknown error"}`;
  }
};
electron.ipcMain.handle("fs:readFileBuffer", async (_, filePath) => {
  try {
    const buffer = await fs.promises.readFile(filePath);
    return buffer;
  } catch (error) {
    console.error("Error reading file buffer:", error);
    throw error;
  }
});
electron.ipcMain.handle("fs:writeFile", async (_, filePath, content) => {
  try {
    await fs.promises.writeFile(filePath, content, "utf-8");
    return { success: true };
  } catch (error) {
    console.error("Error writing file:", error);
    throw error;
  }
});
electron.ipcMain.handle(
  "fs:writeFileBuffer",
  async (_, filePath, buffer) => {
    try {
      await fs.promises.writeFile(filePath, buffer);
      return { success: true };
    } catch (error) {
      console.error("Error writing file buffer:", error);
      throw error;
    }
  }
);
electron.ipcMain.on("workspace-file-selected", (event, filePath) => {
  if (win && !win.isDestroyed()) {
    win.webContents.send("workspace-file-selected", filePath);
  }
});
electron.ipcMain.on("excel-file-selected", (event, filePath) => {
  if (win && !win.isDestroyed()) {
    win.webContents.send("excel-file-selected", filePath);
  }
});
electron.ipcMain.on("app-quit", () => {
  electron.app.quit();
});
electron.ipcMain.on("minimize-window", () => {
  if (win && !win.isDestroyed()) {
    win.minimize();
  }
});
electron.ipcMain.on("maximize-window", () => {
  if (win && !win.isDestroyed()) {
    if (win.isMaximized()) {
      win.unmaximize();
    } else {
      win.maximize();
    }
  }
});
electron.ipcMain.on("close-window", () => {
  if (win && !win.isDestroyed()) {
    win.close();
  }
});
electron.ipcMain.on("minimize-current-window", (event) => {
  const currentWindow = electron.BrowserWindow.fromWebContents(event.sender);
  if (currentWindow && !currentWindow.isDestroyed()) {
    currentWindow.minimize();
  }
});
electron.ipcMain.on("maximize-current-window", (event) => {
  const currentWindow = electron.BrowserWindow.fromWebContents(event.sender);
  if (currentWindow && !currentWindow.isDestroyed()) {
    if (currentWindow.isMaximized()) {
      currentWindow.unmaximize();
    } else {
      currentWindow.maximize();
    }
  }
});
electron.ipcMain.on("close-current-window", (event) => {
  const currentWindow = electron.BrowserWindow.fromWebContents(event.sender);
  if (currentWindow && !currentWindow.isDestroyed()) {
    currentWindow.close();
  }
});
electron.ipcMain.on("show-file-menu", (event, position) => {
  if (!win || win.isDestroyed()) return;
  const fileMenu = electron.Menu.buildFromTemplate([
    {
      label: "导入项目...",
      click: () => {
        win == null ? void 0 : win.webContents.send("menu-triggered-import-project");
      }
    },
    {
      label: "打开文件...",
      click: () => {
        win == null ? void 0 : win.webContents.send("menu-triggered-open-file");
      }
    },
    { type: "separator" },
    {
      label: "退出",
      click: () => electron.app.quit()
    }
  ]);
  fileMenu.popup({ window: win });
});
electron.ipcMain.on("show-edit-menu", (event, position) => {
  if (!win || win.isDestroyed()) return;
  const editMenu = electron.Menu.buildFromTemplate([
    { label: "撤销", role: "undo" },
    { label: "重做", role: "redo" },
    { type: "separator" },
    { label: "剪切", role: "cut" },
    { label: "复制", role: "copy" },
    { label: "粘贴", role: "paste" },
    { type: "separator" },
    { label: "全选", role: "selectAll" }
  ]);
  editMenu.popup({ window: win });
});
electron.ipcMain.on("show-view-menu", (event, position) => {
  if (!win || win.isDestroyed()) return;
  const viewMenu = electron.Menu.buildFromTemplate([
    { label: "放大", role: "zoomIn" },
    { label: "默认大小", role: "resetZoom" },
    { label: "缩小", role: "zoomOut" },
    { type: "separator" },
    {
      label: (win == null ? void 0 : win.isFullScreen()) ? "退出全屏" : "进入全屏",
      role: "togglefullscreen"
    }
  ]);
  viewMenu.popup({ window: win });
});
electron.ipcMain.on("show-dev-menu", (event, position) => {
  if (!win || win.isDestroyed() || !isDev) return;
  const devMenu = electron.Menu.buildFromTemplate([
    { label: "开发者工具", role: "toggleDevTools" },
    { label: "强制刷新", role: "forceReload" }
  ]);
  devMenu.popup({ window: win });
});
electron.ipcMain.on("show-about-menu", (event, position) => {
  if (!win || win.isDestroyed()) return;
  const aboutMenu = electron.Menu.buildFromTemplate([
    {
      label: "关于应用",
      click: () => {
        electron.dialog.showMessageBox(win, {
          title: "关于应用",
          message: "ML Desktop",
          detail: "版本 1.0.0\n一个机器学习数据处理和建模的桌面应用。",
          buttons: ["确定"],
          type: "info"
        });
      }
    }
  ]);
  aboutMenu.popup({ window: win });
});
electron.ipcMain.handle("dialog:showOpenDialog", async (event, options) => {
  if (!win) {
    return { canceled: true, filePaths: [] };
  }
  return await electron.dialog.showOpenDialog(win, options);
});
//# sourceMappingURL=index.js.map
