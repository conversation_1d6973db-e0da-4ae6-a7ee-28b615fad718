// 模型管理模块组件导出

// 通用模型相关组件
export { default as ModelDialog } from "./src/ModelDialog/index.vue";
export { default as ModelSelection } from "./src/ModelSelection/index.vue";
export { default as ModelParamsSetting } from "./src/ModelParamsSetting/index.vue";
export { default as ModelResult } from "./src/ModelResult/index.vue";
export { default as ModelIntroduction } from "./src/ModelIntroduction/index.vue";

// 模型结果展示组件
import ModelInfoCard from "./src/ModelInfoCard/index.vue";
import MetricsTable from "./src/MetricsTable/index.vue";
import ModelCharts from "./src/ModelCharts/index.vue";
import ModelPrediction from "./src/ModelPrediction/index.vue";
import PredictionTable from "./src/PredictionTable/index.vue";
import DataSplitTable from "./src/DataSplitTable/index.vue";
import OptimizationResultTable from "./src/OptimizationResultTable/index.vue";

export {
  ModelInfoCard,
  MetricsTable,
  ModelCharts,
  ModelPrediction,
  PredictionTable,
  DataSplitTable,
  OptimizationResultTable,
};
