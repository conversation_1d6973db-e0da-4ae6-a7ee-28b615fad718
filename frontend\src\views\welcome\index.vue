<script setup lang="ts">
import { ref, onMounted, onUnmounted, computed } from "vue";
import { Bell } from "@element-plus/icons-vue";
import { ElMessage, ElButton } from "element-plus";
import { getSocket } from "@/utils/socket";

defineOptions({
  name: "Welcome",
});

const isVisible = ref(false);
const showSubtitle = ref(false);
const socketStatus = ref("disconnected");
const receivedMessage = ref("");

const statusClass = computed(() => {
  if (socketStatus.value === "connected") return "status-connected";
  if (socketStatus.value === "connecting") return "status-connecting";
  return "status-disconnected";
});

onMounted(() => {
  // 延迟显示主标题
  setTimeout(() => {
    isVisible.value = true;
  }, 300);

  // 延迟显示副标题
  setTimeout(() => {
    showSubtitle.value = true;
  }, 1500);

  // 获取socket实例并设置监听器
  const socket = getSocket();
  if (socket) {
    if (socket.connected) {
      socketStatus.value = "connected";
    }

    const onConnect = () => {
      socketStatus.value = "connected";
      console.log("Socket connected on Welcome page");
    };
    const onDisconnect = () => {
      socketStatus.value = "disconnected";
      console.log("Socket disconnected on Welcome page");
    };
    const onConnectError = () => {
      socketStatus.value = "error";
      console.log("Socket connection error on Welcome page");
    };
    const onTestResponse = data => {
      receivedMessage.value = JSON.stringify(data);
      ElMessage.success("收到来自服务器的响应！");
    };

    socket.on("connect", onConnect);
    socket.on("disconnect", onDisconnect);
    socket.on("connect_error", onConnectError);
    socket.on("test_response", onTestResponse);

    // 在 unmounted 时移除监听器
    onUnmounted(() => {
      if (socket) {
        socket.off("connect", onConnect);
        socket.off("disconnect", onDisconnect);
        socket.off("connect_error", onConnectError);
        socket.off("test_response", onTestResponse);
      }
    });
  }
});

onUnmounted(() => {
  // onUnmounted logic is now inside onMounted to correctly capture socket instance
});

// 测试Socket.IO消息推送
const testSocketIO = () => {
  const socket = getSocket();
  if (socket?.connected) {
    const message = `Hello World, Current time: ${new Date().toLocaleTimeString()}`;
    socket.emit("test", { message });
    ElMessage.info("已发送测试消息到服务器。");
  } else {
    ElMessage.error("Socket.IO 未连接，无法发送消息。");
  }
};
</script>

<template>
  <div class="welcome-container">
    <!-- 背景装饰元素 -->
    <div class="background-decoration">
      <div class="floating-circle circle-1"></div>
      <div class="floating-circle circle-2"></div>
      <div class="floating-circle circle-3"></div>
      <div class="floating-circle circle-4"></div>
    </div>

    <!-- 主要内容 -->
    <div class="welcome-content">
      <div class="logo-container">
        <!-- 移除图标，只保留空间用于布局 -->
      </div>

      <h1 :class="{ 'fade-in-up': isVisible }" class="main-title">
        欢迎使用
      </h1>

      <h2 class="main-title-highlight">
        <span class="highlight-text">固体推进剂配方智能设计软件</span>
      </h2>

      <p :class="{ 'fade-in-up': showSubtitle }" class="subtitle typewriter">
        请使用菜单栏的"文件"菜单来打开文件或导入项目
      </p>

      <!-- Socket.IO测试按钮 -->
      <!-- <div class="test-buttons" v-if="showSubtitle">
        <div class="socket-status-container">
          <span class="status-indicator" :class="statusClass" />
          <span>Socket.IO 状态: <strong>{{ socketStatus }}</strong></span>
        </div>
        <el-button type="primary" :icon="Bell" @click="testSocketIO" class="test-btn"
          :disabled="socketStatus !== 'connected'">
          测试Socket.IO消息
        </el-button>
        <div v-if="receivedMessage" class="response-container">
          <strong>收到回复:</strong> {{ receivedMessage }}
        </div>
      </div> -->
    </div>
  </div>
</template>

<style scoped>
.welcome-container {
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;
  background: linear-gradient(135deg, #f5faff 0%, #dff1ff 100%);
  width: 100%;
  height: 85vh;
}

/* 深色模式适配 */
html.dark .welcome-container {
  background: linear-gradient(135deg, #2d3436 0%, #636e72 100%);
}

/* 背景装饰动画 */
.background-decoration {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 1;
}

.floating-circle {
  position: absolute;
  border-radius: 50%;
  background: linear-gradient(45deg, rgba(255, 255, 255, 0.1), rgba(116, 185, 255, 0.05));
  animation: background-bubble-float 8s ease-in-out infinite;
  box-shadow: 0 0 20px rgba(255, 255, 255, 0.1);
}

.circle-1 {
  width: 80px;
  height: 80px;
  top: 20%;
  left: 10%;
  animation-delay: 0s;
}

.circle-2 {
  width: 120px;
  height: 120px;
  top: 60%;
  right: 15%;
  animation-delay: 2s;
}

.circle-3 {
  width: 60px;
  height: 60px;
  bottom: 30%;
  left: 20%;
  animation-delay: 4s;
}

.circle-4 {
  width: 100px;
  height: 100px;
  top: 10%;
  right: 30%;
  animation-delay: 1s;
}

/* 背景气泡上浮动画 */
@keyframes background-bubble-float {
  0% {
    transform: translateY(100vh) scale(0.5);
    opacity: 0;
  }

  10% {
    opacity: 0.6;
  }

  90% {
    opacity: 0.6;
  }

  100% {
    transform: translateY(-100px) scale(1.2);
    opacity: 0;
  }
}

/* 主要内容样式 */
.welcome-content {
  text-align: center;
  position: relative;
  z-index: 2;
  max-width: 600px;
  padding: 40px;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
}

/* 深色模式适配 */
html.dark .welcome-content {
  background: rgba(0, 0, 0, 0.7);
  color: var(--el-text-color-primary);
}

/* Logo 容器 */
.logo-container {
  margin-bottom: 30px;
}

/* Logo容器保留用于布局间距 */

/* 主标题样式 */
.main-title {
  font-size: 3em;
  margin-bottom: 20px;
  color: #333;
  font-weight: bold;
  background: linear-gradient(45deg, #409eff, #67c23a);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  opacity: 0;
  transform: translateY(30px);
  transition: all 0.8s ease-out;
}

/* 深色模式适配 */
html.dark .main-title {
  background: linear-gradient(45deg, #409eff, #67c23a);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.main-title.fade-in-up {
  opacity: 1;
  transform: translateY(0);
}

/* 主标题高亮样式 */
.main-title-highlight {
  font-size: 2.2em;
  margin-bottom: 25px;
  font-weight: 600;
  line-height: 1.4;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
}

.highlight-text {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  position: relative;
  padding: 8px 20px;
  border-radius: 12px;
  transition: all 0.3s ease;
}

/* 深色模式适配 */
html.dark .highlight-text {
  background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  box-shadow: 0 4px 15px rgba(116, 185, 255, 0.3);
}

html.dark .highlight-text:hover {
  box-shadow: 0 6px 20px rgba(116, 185, 255, 0.4);
}

/* 副标题样式 */
.subtitle {
  font-size: 1.3em;
  color: #666;
  margin-bottom: 30px;
  opacity: 0;
  transform: translateY(20px);
  transition: all 0.8s ease-out 0.3s;
}

/* 深色模式适配 */
html.dark .subtitle {
  color: var(--el-text-color-regular);
}

.subtitle.fade-in-up {
  opacity: 1;
  transform: translateY(0);
}

/* 打字机效果 */
.typewriter {
  overflow: hidden;
  border-right: 2px solid #409eff;
  white-space: nowrap;
  animation: typing 3s steps(40, end) 1.5s forwards, blink-caret 0.75s step-end infinite;
  width: 0;
}

@keyframes typing {
  from {
    width: 0;
  }

  to {
    width: 100%;
  }
}

@keyframes blink-caret {

  from,
  to {
    border-color: transparent;
  }

  50% {
    border-color: #409eff;
  }
}

/* Socket.IO 连接状态样式 */
.test-buttons {
  display: flex;
  flex-direction: column;
  align-items: center;
  opacity: 0;
  animation: fade-in 1s ease-out 2s forwards;
}

.socket-status-container {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
  padding: 10px;
  border-radius: 8px;
  background-color: rgba(255, 255, 255, 0.6);
}

.status-indicator {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  margin-right: 10px;
}

.status-connected {
  background-color: #67c23a;
  box-shadow: 0 0 8px #67c23a;
}

.status-connecting {
  background-color: #e6a23c;
  box-shadow: 0 0 8px #e6a23c;
}

.status-disconnected {
  background-color: #f56c6c;
  box-shadow: 0 0 8px #f56c6c;
}

.test-btn {
  margin-bottom: 15px;
}

.response-container {
  margin-top: 15px;
  padding: 10px;
  border-radius: 8px;
  background-color: rgba(255, 255, 255, 0.6);
  max-width: 300px;
  overflow-wrap: break-word;
}

@keyframes fade-in {
  from {
    opacity: 0;
  }

  to {
    opacity: 1;
  }
}

/* 响应式适配 */
@media (max-width: 768px) {
  .welcome-content {
    max-width: 90%;
    padding: 30px 20px;
  }

  .main-title {
    font-size: 2.5em;
  }

  .main-title-highlight {
    font-size: 1.8em;
    gap: 6px;
  }

  .highlight-text {
    padding: 6px 15px;
  }

  .subtitle {
    font-size: 1.1em;
  }
}
</style>
