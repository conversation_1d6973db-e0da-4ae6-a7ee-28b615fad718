<template>
  <div class="data-split-table">
    <div class="model-controls">
      <div class="selection-controls">
        <button
          v-for="key in availableDatasets"
          :key="key"
          type="button"
          :class="['selection-button', { 'is-active': selectedDataset === key }]"
          @click="selectedDataset = key"
          :disabled="availableDatasets.length === 0"
        >
          {{ datasetConfig.titles[key] }}
        </button>
      </div>
      
      <!-- 添加导出数据按钮 -->
      <el-button 
        type="primary"
        :icon="Download"
        :disabled="tableData.length === 0"
        @click="exportSplitData"
      >
        导出数据
      </el-button>
    </div>

    <!-- 训练集/测试集表格 -->
    <ReTable
      v-if="selectedDataset !== 'cv' && tableData.length > 0"
      :data="tableData"
    >
      <template #default="{ data: paginatedData }">
        <el-table
          :data="paginatedData"
          style="width: 100%"
          class="model-table"
          height="400"
        >
          <el-table-column
            v-for="header in headers"
            :key="header"
            :prop="header"
            :label="header"
            sortable
          />
        </el-table>
      </template>
    </ReTable>

    <!-- 交叉验证结果 -->
    <div
      v-else-if="selectedDataset === 'cv' && tableData.length > 0"
      class="cv-results"
    >
      <el-collapse v-model="activeCvFold">
        <el-collapse-item
          v-for="(fold, index) in tableData"
          :key="index"
          :title="`Fold ${index + 1}`"
          :name="index"
        >
          <div class="fold-content">
            <strong>训练集索引:</strong>
            <p>{{ fold['Train Indices'] }}</p>
            <strong>测试集索引:</strong>
            <p>{{ fold['Test Indices'] }}</p>
          </div>
        </el-collapse-item>
      </el-collapse>
    </div>

    <el-empty v-else description="暂无数据" />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from "vue";
import { ElMessage } from "element-plus";
import { Download } from "@element-plus/icons-vue"; // 导入Download图标
import ReTable from "@/components/ReTable/index.vue";
import { useEpThemeStoreHook } from "@/store/modules/epTheme";
import { exportSingleSheet } from "@/utils/exportUtils";

interface Props {
  modelResult: any;
  datasetConfig?: {
    order: readonly string[];
    titles: Record<string, string>;
  };
}

const props = withDefaults(defineProps<Props>(), {
  datasetConfig: () => ({
    order: ["train", "test", "cv"] as const,
    titles: {
      train: "训练集",
      test: "测试集",
      cv: "交叉验证",
    },
  }),
});

const epThemeColor = useEpThemeStoreHook().getEpThemeColor;

// 模型参数
const params = computed(() => props.modelResult?.model_params || {});

// 可用数据集
const availableDatasets = computed(() => {
  const p = params.value;
  const arr: string[] = [];
  if (p.x_train && p.y_train) arr.push("train");
  if (p.x_test && p.y_test) arr.push("test");
  if (Array.isArray(p.cv) && p.cv.length > 0) arr.push("cv");
  return arr;
});

// 选中数据集
const selectedDataset = ref(
  availableDatasets.value.length > 0 ? availableDatasets.value[0] : "train"
);

// 交叉验证激活项
const activeCvFold = ref(0);

// 生成表头
const headers = computed(() => {
  const p = params.value;
  const key = selectedDataset.value;
  if (key === 'cv') return [];
  const xKey = key === 'train' ? 'x_train' : 'x_test';
  const yKey = key === 'train' ? 'y_train' : 'y_test';
  const xData = p[xKey] || {};
  const featureKeys = Object.keys(xData);
  const yData = p[yKey] || {};
  const targetKeys = Object.keys(yData);
  const targetCol = targetKeys[0] || yKey;
  return ['原始索引', ...featureKeys, targetCol];
});

// 格式化数据
const tableData = computed(() => {
  const p = params.value;
  const key = selectedDataset.value;
  if (key === 'cv') {
    return p.cv.map((fold: any, idx: number) => ({
      Fold: idx + 1,
      'Train Indices': Array.isArray(fold.train_indices) ? fold.train_indices.join(', ') : '',
      'Test Indices': Array.isArray(fold.test_indices) ? fold.test_indices.join(', ') : '',
    }));
  }
  const xKey = key === 'train' ? 'x_train' : 'x_test';
  const yKey = key === 'train' ? 'y_train' : 'y_test';
  const xData = p[xKey] || {};
  const yData = p[yKey] || {};
  const featureKeys = Object.keys(xData);
  const idxKeys = featureKeys.length
    ? Object.keys(xData[featureKeys[0]])
    : [];
  const targetKeys = Object.keys(yData);
  const targetCol = targetKeys[0] || yKey;
  return idxKeys.map((idx) => {
    const row: Record<string, any> = { '原始索引': idx };
    featureKeys.forEach((f) => {
      row[f] = xData[f]?.[idx];
    });
    row[targetCol] = yData[targetCol]?.[idx];
    return row;
  });
});

// 导出功能
async function exportSplitData() {
  if (!tableData.value.length) {
    ElMessage.warning('没有可导出的数据');
    return;
  }

  try {
    const content = tableData.value.map((r) => headers.value.map((h) => r[h]));

    await exportSingleSheet(
      { headers: headers.value, content },
      {
        suggestedName: `数据集_${props.datasetConfig.titles[selectedDataset.value]}`,
        sheetName: props.datasetConfig.titles[selectedDataset.value] || 'Dataset',
        exportType: "auto"
      }
    );
    
    ElMessage.success('数据导出成功');
  } catch (error) {
    console.error('导出失败:', error);
    ElMessage.error('导出失败，请重试');
  }
}

// 监听可用数据集变化
watch(
  availableDatasets,
  (newArr) => {
    if (newArr.length && !newArr.includes(selectedDataset.value)) {
      selectedDataset.value = newArr[0];
    }
  },
  { immediate: true }
);
</script>

<style lang="scss" scoped>
.data-split-table {
  .model-controls {
    display: flex;
    align-items: center;
    justify-content: space-between; // 改为两端对齐
    margin-bottom: 16px;
  }
  .cv-results {
    .fold-content {
      padding: 10px;
      p {
        background-color: #f8f9fa;
        padding: 8px;
        border-radius: 4px;
        word-break: break-all;
        margin-top: 4px;
        margin-bottom: 12px;
      }
    }
    :deep(.el-collapse) {
      border: none;
    }
    :deep(.el-collapse-item__header) {
      background: #f9fbff;
      border: 1px solid transparent;
      border-radius: 4px;
      margin-bottom: 8px;
    }
    :deep(.el-collapse-item__wrap) {
      border-bottom: none;
    }
    :deep(.el-collapse-item__content) {
      padding-bottom: 0;
    }
  }
}

.selection-controls {
  display: flex;
  gap: 12px;
}

.selection-button {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 16px;
  height: 36px;
  border-radius: 24px;
  border: 1px solid transparent;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.3s ease;
  background: rgba(206, 206, 206, 0.1);
  color: #666;
}

.selection-button.is-active {
  background: rgba(0, 93, 255, 0.1);
  border: 1px solid #005dff;
  color: #005dff;
  font-weight: 500;
}

/* 误差颜色样式 */
:deep(.error-low) {
  color: #67c23a;
}
</style> 