import * as XLSX from 'xlsx';

// 传统下载方法
const downloadFile = (content: string | ArrayBuffer, filename: string, mimeType: string) => {
  const blob = new Blob([content], { type: mimeType });
  const url = URL.createObjectURL(blob);
  const link = document.createElement('a');
  link.href = url;
  link.download = filename;
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
  URL.revokeObjectURL(url);
};

export interface ExportData {
  headers: string[];
  content: any[][];
}

export interface ExportOptions {
  suggestedName?: string;
  sheetName?: string;
  exportType?: 'csv' | 'excel' | 'auto';
  currentSheetName?: string;
}

export interface MultiSheetData {
  [sheetName: string]: ExportData;
}

/**
 * 导出单个工作表数据
 */
export const exportSingleSheet = async (
  data: ExportData,
  options: ExportOptions = {}
): Promise<void> => {
  const {
    suggestedName = `数据导出_${Date.now()}`,
    sheetName = 'Sheet1',
    exportType = 'auto'
  } = options;

  try {
    if (exportType === 'csv') {
      const csvContent = [
        data.headers.join(','),
        ...data.content.map(row => row.map(cell => `"${cell || ''}"`).join(','))
      ].join('\n');
      downloadFile(csvContent, `${suggestedName}.csv`, 'text/csv');
    } else {
      // 默认导出为 Excel
      const wb = XLSX.utils.book_new();
      const dataForSheet = [data.headers, ...data.content];
      const ws = XLSX.utils.aoa_to_sheet(dataForSheet);
      XLSX.utils.book_append_sheet(wb, ws, sheetName);
      const excelBuffer = XLSX.write(wb, { bookType: 'xlsx', type: 'array' });
      downloadFile(excelBuffer, `${suggestedName}.xlsx`, 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
    }
  } catch (error: any) {
    console.warn('导出失败:', error);
    throw error;
  }
};

/**
 * 导出多个工作表数据
 */
export const exportMultiSheet = async (
  sheetsData: MultiSheetData,
  options: ExportOptions = {}
): Promise<void> => {
  const { suggestedName = `数据导出_${Date.now()}` } = options;

  try {
    // 对于多工作表，默认导出为 Excel
    const wb = XLSX.utils.book_new();
    Object.entries(sheetsData).forEach(([sheetName, sheetData]) => {
      const dataForSheet = [sheetData.headers, ...sheetData.content];
      const ws = XLSX.utils.aoa_to_sheet(dataForSheet);
      XLSX.utils.book_append_sheet(wb, ws, sheetName);
    });
    const excelBuffer = XLSX.write(wb, { bookType: 'xlsx', type: 'array' });
    downloadFile(excelBuffer, `${suggestedName}.xlsx`, 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
  } catch (error: any) {
    console.warn('导出失败:', error);
    throw error;
  }
};

/**
 * 导出图表数据
 */
export const exportChartData = async (
  chartData: { columns: string[]; data: any[][] },
  chartTitle: string,
  options: ExportOptions = {}
): Promise<void> => {
  const { suggestedName = `${chartTitle}_数据_${Date.now()}` } = options;

  try {
    // 默认导出为 Excel
    const wb = XLSX.utils.book_new();
    const ws = XLSX.utils.aoa_to_sheet([
      chartData.columns,
      ...chartData.data
    ]);
    XLSX.utils.book_append_sheet(wb, ws, 'Sheet1');
    const excelBuffer = XLSX.write(wb, { bookType: 'xlsx', type: 'array' });
    downloadFile(excelBuffer, `${suggestedName}.xlsx`, 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
  } catch (error: any) {
    console.warn('导出失败:', error);
    throw error;
  }
};
