import * as XLSX from 'xlsx';

// 扩展 Window 接口以支持 File System Access API
declare global {
  interface Window {
    showSaveFilePicker?(options: {
      suggestedName?: string;
      types?: Array<{
        description: string;
        accept: Record<string, string[]>;
      }>;
    }): Promise<FileSystemFileHandle>;
  }
}

// 检查是否支持 File System Access API
const supportsFileSystemAccess = () => {
  return 'showSaveFilePicker' in window && typeof window.showSaveFilePicker === 'function';
};

// 传统下载方法
const downloadFile = (content: string | ArrayBuffer, filename: string, mimeType: string) => {
  const blob = new Blob([content], { type: mimeType });
  const url = URL.createObjectURL(blob);
  const link = document.createElement('a');
  link.href = url;
  link.download = filename;
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
  URL.revokeObjectURL(url);
};

export interface ExportData {
  headers: string[];
  content: any[][];
}

export interface ExportOptions {
  suggestedName?: string;
  sheetName?: string;
  exportType?: 'csv' | 'excel' | 'auto';
  currentSheetName?: string;
}

export interface MultiSheetData {
  [sheetName: string]: ExportData;
}

/**
 * 导出单个工作表数据
 */
export const exportSingleSheet = async (
  data: ExportData,
  options: ExportOptions = {}
): Promise<void> => {
  const {
    suggestedName = `数据导出_${Date.now()}`,
    sheetName = 'Sheet1',
    exportType = 'auto'
  } = options;

  try {
    if (supportsFileSystemAccess()) {
      // 使用 File System Access API
      const fileHandle = await window.showSaveFilePicker!({
        suggestedName,
        types: [
          {
            description: 'Excel 文件',
            accept: {
              'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': ['.xlsx']
            }
          },
          {
            description: 'CSV 文件',
            accept: {
              'text/csv': ['.csv']
            }
          }
        ]
      });

      const writable = await fileHandle.createWritable();
      const fileName = fileHandle.name;

      if (fileName.endsWith('.csv') || exportType === 'csv') {
        const csvContent = [
          data.headers.join(','),
          ...data.content.map(row => row.map(cell => `"${cell || ''}"`).join(','))
        ].join('\n');
        await writable.write(csvContent);
      } else if (fileName.endsWith('.xlsx') || exportType === 'excel') {
        const wb = XLSX.utils.book_new();
        const dataForSheet = [data.headers, ...data.content];
        const ws = XLSX.utils.aoa_to_sheet(dataForSheet);
        XLSX.utils.book_append_sheet(wb, ws, sheetName);
        const excelBuffer = XLSX.write(wb, { bookType: 'xlsx', type: 'array' });
        await writable.write(excelBuffer);
      }

      await writable.close();
    } else {
      // 使用传统下载方式
      if (exportType === 'csv') {
        const csvContent = [
          data.headers.join(','),
          ...data.content.map(row => row.map(cell => `"${cell || ''}"`).join(','))
        ].join('\n');
        downloadFile(csvContent, `${suggestedName}.csv`, 'text/csv');
      } else {
        // 默认导出为 Excel
        const wb = XLSX.utils.book_new();
        const dataForSheet = [data.headers, ...data.content];
        const ws = XLSX.utils.aoa_to_sheet(dataForSheet);
        XLSX.utils.book_append_sheet(wb, ws, sheetName);
        const excelBuffer = XLSX.write(wb, { bookType: 'xlsx', type: 'array' });
        downloadFile(excelBuffer, `${suggestedName}.xlsx`, 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
      }
    }
  } catch (error: any) {
    if (error.name !== 'AbortError') {
      console.warn('导出失败:', error);
      throw error;
    }
  }
};

/**
 * 导出多个工作表数据
 */
export const exportMultiSheet = async (
  sheetsData: MultiSheetData,
  options: ExportOptions = {}
): Promise<void> => {
  const { suggestedName = `数据导出_${Date.now()}`, currentSheetName } = options;

  try {
    if (supportsFileSystemAccess()) {
      // 使用 File System Access API
      const fileHandle = await window.showSaveFilePicker!({
        suggestedName,
        types: [
          {
            description: 'Excel 文件 (所有工作表)',
            accept: {
              'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': ['.xlsx']
            }
          },
          {
            description: 'CSV 文件 (当前工作表)',
            accept: {
              'text/csv': ['.csv']
            }
          }
        ]
      });

      const writable = await fileHandle.createWritable();
      const fileName = fileHandle.name;

      if (fileName.endsWith('.csv')) {
        const sheetNameToExport = currentSheetName || Object.keys(sheetsData)[0];
        const currentSheetData = sheetsData[sheetNameToExport];
        if (currentSheetData) {
          const csvContent = [
            currentSheetData.headers.join(','),
            ...currentSheetData.content.map(row => row.map(cell => `"${cell || ''}"`).join(','))
          ].join('\n');
          await writable.write(csvContent);
        }
      } else if (fileName.endsWith('.xlsx')) {
        const wb = XLSX.utils.book_new();
        Object.entries(sheetsData).forEach(([sheetName, sheetData]) => {
          const dataForSheet = [sheetData.headers, ...sheetData.content];
          const ws = XLSX.utils.aoa_to_sheet(dataForSheet);
          XLSX.utils.book_append_sheet(wb, ws, sheetName);
        });
        const excelBuffer = XLSX.write(wb, { bookType: 'xlsx', type: 'array' });
        await writable.write(excelBuffer);
      }

      await writable.close();
    } else {
      // 使用传统下载方式
      // 对于多工作表，默认导出为 Excel
      const wb = XLSX.utils.book_new();
      Object.entries(sheetsData).forEach(([sheetName, sheetData]) => {
        const dataForSheet = [sheetData.headers, ...sheetData.content];
        const ws = XLSX.utils.aoa_to_sheet(dataForSheet);
        XLSX.utils.book_append_sheet(wb, ws, sheetName);
      });
      const excelBuffer = XLSX.write(wb, { bookType: 'xlsx', type: 'array' });
      downloadFile(excelBuffer, `${suggestedName}.xlsx`, 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
    }
  } catch (error: any) {
    if (error.name !== 'AbortError') {
      console.warn('导出失败:', error);
      throw error;
    }
  }
};

/**
 * 导出图表数据
 */
export const exportChartData = async (
  chartData: { columns: string[]; data: any[][] },
  chartTitle: string,
  options: ExportOptions = {}
): Promise<void> => {
  const { suggestedName = `${chartTitle}_数据_${Date.now()}` } = options;

  try {
    if (supportsFileSystemAccess()) {
      // 使用 File System Access API
      const fileHandle = await window.showSaveFilePicker!({
        suggestedName,
        types: [
          {
            description: 'Excel 文件',
            accept: {
              'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': ['.xlsx']
            }
          },
          {
            description: 'CSV 文件',
            accept: {
              'text/csv': ['.csv']
            }
          }
        ]
      });

      const writable = await fileHandle.createWritable();
      const fileName = fileHandle.name;

      if (fileName.endsWith('.csv')) {
        const csvContent = [
          chartData.columns.join(','),
          ...chartData.data.map(row => row.map(cell => `"${cell || ''}"`).join(','))
        ].join('\n');
        await writable.write(csvContent);
      } else if (fileName.endsWith('.xlsx')) {
        const wb = XLSX.utils.book_new();
        const ws = XLSX.utils.aoa_to_sheet([
          chartData.columns,
          ...chartData.data
        ]);
        XLSX.utils.book_append_sheet(wb, ws, 'Sheet1');
        const excelBuffer = XLSX.write(wb, { bookType: 'xlsx', type: 'array' });
        await writable.write(excelBuffer);
      }

      await writable.close();
    } else {
      // 使用传统下载方式，默认导出为 Excel
      const wb = XLSX.utils.book_new();
      const ws = XLSX.utils.aoa_to_sheet([
        chartData.columns,
        ...chartData.data
      ]);
      XLSX.utils.book_append_sheet(wb, ws, 'Sheet1');
      const excelBuffer = XLSX.write(wb, { bookType: 'xlsx', type: 'array' });
      downloadFile(excelBuffer, `${suggestedName}.xlsx`, 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
    }
  } catch (error: any) {
    if (error.name !== 'AbortError') {
      console.warn('导出失败:', error);
      throw error;
    }
  }
};
