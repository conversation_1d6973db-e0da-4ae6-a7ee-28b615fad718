import request from "@/utils/request";
import type { PreprocessConfig } from "@/types/preprocess";
import { AxiosHeaders } from "axios";

export type DataPreprocessResponse = {
  code: number;
  msg: string;
  data: any;
};

export const reqDataFill = (data?: PreprocessConfig) => {
  return request.post<DataPreprocessResponse>("/fill/", data, {
    headers: new AxiosHeaders({}),
    skipLoading: true,
  });
};

export const reqOutlierDetection = (data?: PreprocessConfig) => {
  return request.post<DataPreprocessResponse>("/outlier/", data, {
    headers: new AxiosHeaders({}),
    skipLoading: true,
  });
};
