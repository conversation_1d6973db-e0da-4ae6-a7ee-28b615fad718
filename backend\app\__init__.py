from flask import Flask, jsonify
import platform, logging, os
from app.extensions import cors, socketio
from app.services.DBService import DBService
from app.services.RabbitmqService import setup_rabbitmq
from concurrent.futures import ProcessPoolExecutor
from app.types.flaskapp import FlaskWithExecutor
from typing import cast
from app.log import configure_logging

logger = logging.getLogger()

def init_worker():
    """子进程初始化函数"""
    logger.info("subprocess started, initializing RabbitMQ...")
    setup_rabbitmq()
    logger.info("subprocess RabbitMQ initialization completed")

def start_worker_processes(app):
    """在应用启动后启动工作进程"""
    if hasattr(app, 'executor'):
        cast(FlaskWithExecutor, app).executor.submit(init_worker)

def create_app(mode="dev"):
    
    app = Flask(__name__)
    app.config.from_object(f"app.configs.{mode}") # 从configs.py中加载配置
    
    # 使用setattr避免类型检查错误
    cast(FlaskWithExecutor, app).connected_sids = {}
    cast(FlaskWithExecutor, app).url_prefix = "/" if platform.system() == "Windows" else f"/{app.config['SERVER_PREFIX']}/"
    cast(FlaskWithExecutor, app).temp_folder = app.config["TEMP_FOLDER"]
    cast(FlaskWithExecutor, app).statics_folder = app.config["STATIC_FOLDER"]
    cast(FlaskWithExecutor, app).upload_folder = app.config["UPLOAD_FOLDER"]
    cast(FlaskWithExecutor, app).projects_folder = app.config["PROJECTS_FOLDER"]
    cast(FlaskWithExecutor, app).model_path = app.config["MODEL_PATH"]
    cast(FlaskWithExecutor, app).db_path = app.config["DB_PATH"]
    cast(FlaskWithExecutor, app).log_dir = app.config["LOG_DIR"]
    cast(FlaskWithExecutor, app).is_rabbitmq_ready = False
    
    for i in [
        cast(FlaskWithExecutor, app).temp_folder,
        cast(FlaskWithExecutor, app).statics_folder,
        cast(FlaskWithExecutor, app).upload_folder,
        cast(FlaskWithExecutor, app).model_path,
        cast(FlaskWithExecutor, app).log_dir
    ]: 
        if not i.exists(): i.mkdir()

    configure_logging(app, cast(FlaskWithExecutor, app).log_dir)

    # 配置flask插件
    cors.init_app(app)
    socketio.init_app(app)
    cast(FlaskWithExecutor, app).executor = ProcessPoolExecutor(
        max_workers=min(int(os.cpu_count() * 0.8), 4),  # type: ignore
    )
    cast(FlaskWithExecutor, app).is_rabbitmq_ready = setup_rabbitmq()
    
    # 配置蓝图
    from .routers import linear_bp
    app.register_blueprint(linear_bp, url_prefix=f"{cast(FlaskWithExecutor, app).url_prefix}/linear")
    from .routers.models.tree import tree_bp
    app.register_blueprint(tree_bp, url_prefix=f"{cast(FlaskWithExecutor, app).url_prefix}/tree")
    from .routers.models.other import other_bp
    app.register_blueprint(other_bp, url_prefix=f"{cast(FlaskWithExecutor, app).url_prefix}/other")
    from .routers import project_bp
    app.register_blueprint(project_bp, url_prefix=f"{cast(FlaskWithExecutor, app).url_prefix}/project")
    from .routers import fill_bp
    app.register_blueprint(fill_bp, url_prefix=f"{cast(FlaskWithExecutor, app).url_prefix}/fill")
    from .routers import outlier_bp
    app.register_blueprint(outlier_bp, url_prefix=f"{cast(FlaskWithExecutor, app).url_prefix}/outlier")
    from .routers.models.common import common_bp
    app.register_blueprint(common_bp, url_prefix=f"{cast(FlaskWithExecutor, app).url_prefix}/")
    from .routers.applications.single_target_search import single_target_search_bp
    app.register_blueprint(single_target_search_bp, url_prefix=f"{cast(FlaskWithExecutor, app).url_prefix}/search")

    # 初始化数据库
    # app.config["DB_PATH"].unlink()
    if not cast(FlaskWithExecutor, app).db_path.exists():
        DBService.init_db()

    # global error handling
    # @app.errorhandler(Exception)
    # def handle_error(error):
    #     error_msg = f'unhandled error: {str(error)}'
    #     app.logger.error(error_msg)
    #     return jsonify({'error': error_msg}), 500
    
    app.logger.info(app.url_map)

    logger.info(f"create_app: application started - project directory: {app.config['PROJECTS_FOLDER']}")

    return app
        
