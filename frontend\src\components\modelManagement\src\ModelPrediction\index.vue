<template>
  <div class="model-prediction">
    <div class="upload-container">
      <div class="prediction-controls">
        <el-upload
          ref="uploadRef"
          :action="''"
          :auto-upload="false"
          :show-file-list="true"
          :on-change="handleFileChange"
          :on-remove="handleFileRemove"
          :limit="1"
          :on-exceed="handleExceed"
        >
          <template #trigger>
            <el-button type="primary" :icon="Upload" size="default"
              >选择文件</el-button
            >
          </template>
          <el-button
            class="ml-3"
            type="success"
            @click="submitUpload"
            :loading="loading"
            :disabled="!selectedFile"
            size="default"
          >
            开始预测
          </el-button>
          <template #tip>
            <div class="el-upload__tip">
              请选择要用于预测的数据文件 (e.g., .csv, .xlsx)
            </div>
          </template>
        </el-upload>
      </div>
    </div>

    <div
      class="result-container mt-4"
      v-if="predictionResult.length > 0 || loading"
    >
      <div class="result-header">
        <span class="result-title">预测结果</span>
        <el-button
          type="primary"
          @click="exportData"
          :icon="Download"
          v-if="predictionResult.length > 0"
          >导出结果</el-button
        >
      </div>
      <div v-if="loading" class="loading-container">
        <el-icon class="is-loading" :size="20">
          <Loading />
        </el-icon>
        <span>预测中...</span>
      </div>
      <ReTable
        v-if="predictionResult.length > 0 && !loading"
        :data="predictionResult"
      >
        <template #default="{ data: paginatedData }">
          <el-table :data="paginatedData" class="model-table" height="400">
            <el-table-column
              v-for="column in tableColumns"
              :key="column.prop"
              :prop="column.prop"
              :label="column.label"
              sortable
              show-overflow-tooltip
              :header-cell-class-name="getHeaderClass"
            >
              <template #default="{ row }">
                {{ formatValue(row[column.prop]) }}
              </template>
            </el-table-column>
          </el-table>
        </template>
      </ReTable>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from "vue";
import { ElMessage, genFileId } from "element-plus";
import { Upload, Download, Loading, CircleCheck } from "@element-plus/icons-vue";
import type {
  UploadInstance,
  UploadProps,
  UploadFile,
  UploadRawFile
} from "element-plus";
import * as XLSX from "xlsx";
import ReTable from "@/components/ReTable/index.vue";
import { predictWithData } from "@/api/models/model";
import { exportSingleSheet } from "@/utils/exportUtils";

const props = defineProps<{
  modelResult?: {
    model_name?: string;
  };
  taskUid?: string;
}>();

const uploadRef = ref<UploadInstance>();
const selectedFile = ref<File | null>(null);
const loading = ref(false);
const predictionResult = ref<any[]>([]);
const originalData = ref<any[]>([]);
const predictionLabel = ref<string>("");

const predictionColumnKey = "_prediction";

const tableColumns = computed(() => {
  if (predictionResult.value.length === 0) {
    return [];
  }
  const firstRow = predictionResult.value[0];
  const otherKeys = Object.keys(firstRow).filter(
    k => k !== predictionColumnKey
  );

  const predictionColumnHeader = predictionLabel.value
    ? `预测值 (${predictionLabel.value})`
    : "预测值";

  return [
    { prop: predictionColumnKey, label: predictionColumnHeader },
    ...otherKeys.map(key => ({ prop: key, label: key }))
  ];
});

const handleFileChange: UploadProps["onChange"] = (
  uploadFile: UploadFile
) => {
  if (uploadFile.raw) {
    selectedFile.value = uploadFile.raw;
  }
};

const handleFileRemove = () => {
  selectedFile.value = null;
  predictionResult.value = [];
  originalData.value = [];
  predictionLabel.value = "";
};

const submitUpload = async () => {
  if (!selectedFile.value) {
    ElMessage.warning("请先选择一个文件");
    return;
  }
  if (!props.taskUid) {
    ElMessage.error("模型ID不存在，无法预测。");
    return;
  }

  loading.value = true;
  predictionResult.value = [];
  originalData.value = [];
  predictionLabel.value = "";

  const reader = new FileReader();
  reader.onload = async (e: ProgressEvent<FileReader>) => {
    try {
      const data = e.target?.result;
      const workbook = XLSX.read(data, { type: "array" });
      const firstSheetName = workbook.SheetNames[0];
      const worksheet = workbook.Sheets[firstSheetName];
      const jsonData = XLSX.utils.sheet_to_json(worksheet);
      originalData.value = jsonData; // Store original data

      const res = await predictWithData(props.taskUid, jsonData);

      const { predictions, label } = res.data;
      predictionLabel.value = label || "";

      if (originalData.value.length === predictions.length) {
        predictionResult.value = originalData.value.map((row, index) => ({
          [predictionColumnKey]: predictions[index],
          ...row
        }));
      } else {
        ElMessage.error(
          "Prediction results do not match the number of rows in the input file."
        );
        // Fallback to show only predictions if lengths mismatch
        predictionResult.value = predictions.map(p => ({
          [predictionColumnKey]: p
        }));
      }

      ElMessage.success("预测成功");
    } catch (err) {
      ElMessage.error("预测失败: " + ((err as any)?.message || "未知错误"));
    } finally {
      loading.value = false;
    }
  };
  reader.onerror = () => {
    loading.value = false;
    ElMessage.error("文件读取失败");
  };
  reader.readAsArrayBuffer(selectedFile.value);
};

const handleExceed: UploadProps["onExceed"] = files => {
  uploadRef.value!.clearFiles();
  const file = files[0] as UploadRawFile;
  file.uid = genFileId();
  uploadRef.value!.handleStart(file);
};

const formatValue = (value: any) => {
  if (typeof value === "number") {
    return parseFloat(value.toFixed(4));
  }
  return value;
};

const getHeaderClass = ({ column }: { column: any }) => {
  if (column.property === predictionColumnKey) {
    return "prediction-header";
  }
  return "";
};

const exportData = async () => {
  if (predictionResult.value.length === 0) {
    ElMessage.warning("没有可导出的数据");
    return;
  }

  try {
    // 获取表头
    const headers = Object.keys(predictionResult.value[0] || {});
    const content = predictionResult.value.map(row => {
      return headers.map(header => row[header]);
    });

    await exportSingleSheet(
      { headers, content },
      {
        suggestedName: `${props.modelResult?.model_name || "model"}-预测结果`,
        sheetName: "预测结果",
        exportType: "auto"
      }
    );

    ElMessage.success("结果已开始导出");
  } catch (error) {
    console.error('导出失败:', error);
    ElMessage.error('导出失败，请重试');
  }
};
</script>

<style lang="scss" scoped>
.model-prediction {
  .mt-4 {
    margin-top: 1rem;
  }
  .ml-3 {
    margin-left: 0.75rem;
  }
  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  .prediction-controls {
    display: flex;
    align-items: center;
  }
  .loading-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 20px;
    color: #909399;
    font-size: 14px;
    height: 100px;
    gap: 8px;
  }
}

.upload-container {
  background: #f9fbff;
  border-radius: 8px;
  padding: 20px;
}

html.dark .upload-container {
  background: var(--el-fill-color-darker);
  border-color: var(--el-border-color-lighter);
}

.result-container {
  margin-top: 1rem;
}

.result-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.result-title {
  font-size: 16px;
  font-weight: 600;
  color: var(--el-text-color-primary);
}

:deep(.el-table .prediction-header) {
  background-color: var(--el-color-success-light-3) !important;
  color: var(--el-color-success);
}
</style>
