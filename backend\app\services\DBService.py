from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from app.types.services import Base, ModelTask
from typing import Optional, List
from app.configs import BasicConfig
from datetime import datetime
from app.types.model_request import ModelConfig
from app.types.search_request import SearchConfig
from app.types.services import SearchTask

db_path = "sqlite:///" + str(BasicConfig.DB_PATH)

class DBService(object):

    @staticmethod
    def init_db():
        engine = create_engine(db_path, echo=False)
        Base.metadata.create_all(engine)
        engine.dispose()

    @staticmethod
    def get_session():
        engine = create_engine(db_path, echo=False)
        Session = sessionmaker(bind=engine)
        return Session()
    
    @staticmethod
    def create_model_task(task_uid: str, name: str, params: ModelConfig) -> ModelTask:
        model_task: ModelTask = ModelTask(
            task_uid=task_uid,
            name=f"{name}_build",
            params=params.to_dict(),
            status="pending",
            progress=None,
            created_at=datetime.now(),
            updated_at=datetime.now(),
            result=None,
            error=None,
            is_rabbitmq_ready=params.is_rabbitmq_ready,
            asynchronous=params.asynchronous
        )
        return model_task
    
    @staticmethod
    def add_model_task(model_task: ModelTask):
        session = DBService.get_session()
        session.add(model_task)
        session.commit()
        # 获取新创建任务的ID
        task_id = model_task.id
        session.close()
        return task_id
    
    @staticmethod
    def update_model_task(task_uid: str, **kwargs):
        session = DBService.get_session()
        update_values = {getattr(ModelTask, k): v for k, v in kwargs.items()}
        session.query(ModelTask).filter(ModelTask.task_uid == task_uid).update(update_values)
        session.commit()
        session.close()
    
    @staticmethod
    def get_model_info(task_uid: str) -> Optional[ModelTask]:
        session = DBService.get_session()
        model_info = session.query(ModelTask).filter(ModelTask.task_uid == task_uid).first()
        session.close()
        return model_info
    
    @staticmethod
    def get_model_list() -> List[ModelTask]:
        session = DBService.get_session()
        model_list = session.query(ModelTask).all()
        session.close()
        return model_list
    
    @staticmethod
    def create_search_task(task_uid: str, name: str, params: SearchConfig) -> SearchTask:
        search_task: SearchTask = SearchTask(
            task_uid=task_uid,
            name=f"{name}_search",
            params=params.to_dict(),
            status="running",
            progress=None,
            created_at=datetime.now(),
            updated_at=datetime.now(),
            result=None,
            error=None,
            is_rabbitmq_ready=params.is_rabbitmq_ready,
            asynchronous=params.asynchronous
        )
        return search_task
    
    @staticmethod
    def add_search_task(search_task: SearchTask):
        session = DBService.get_session()
        session.add(search_task)
        session.commit()
        # 获取新创建任务的ID
        task_id = search_task.id
        session.close()
        return task_id
    
    @staticmethod
    def update_search_task(task_id: str, **kwargs):
        session = DBService.get_session()
        update_values = {getattr(SearchTask, k): v for k, v in kwargs.items()}
        session.query(SearchTask).filter(SearchTask.id == task_id).update(update_values)
        session.commit()
        session.close()
    
    @staticmethod
    def get_search_info(task_id: str) -> Optional[SearchTask]:
        session = DBService.get_session()
        search_info = session.query(SearchTask).filter(SearchTask.id == task_id).first()
        session.close()
        return search_info
    