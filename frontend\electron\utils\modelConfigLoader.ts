/**
 * Node.js版本的模型配置加载器
 * 用于在Electron主进程中动态加载模型配置
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

// 在ES模块中获取__dirname的等价物
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// 模型配置接口（与前端保持一致）
export interface ModelConfig {
  name: string;
  displayName: string;
  description: string;
  category: 'linear' | 'tree' | 'ml' | 'clustering' | 'preprocessing';
  type: 'regression' | 'classification' | 'clustering' | 'preprocessing';
  [key: string]: any;
}

// 模型元数据接口
export interface ModelMetadata {
  id: string;
  fileName: string;
  config: ModelConfig;
}

// Node.js版本的模型配置管理器
export class NodeModelConfigManager {
  private static instance: NodeModelConfigManager;
  private modelConfigs: Map<string, ModelMetadata> = new Map();
  private modelDisplayNameMap: Map<string, string> = new Map();
  private initialized = false;
  private configDir: string;

  private constructor() {
    // 获取配置文件目录路径
    // 在开发环境中：electron/utils -> src/config/modelParams
    // 在生产环境中：需要考虑打包后的路径结构
    this.configDir = this.getConfigDirectory();
  }

  /**
   * 获取配置文件目录路径
   */
  private getConfigDirectory(): string {
    // 尝试多个可能的路径
    const possiblePaths = [
      // 开发环境路径
      path.join(__dirname, '../../src/config/modelParams'),
      // 生产环境路径（相对于打包后的位置）
      path.join(__dirname, '../../../src/config/modelParams'),
      path.join(__dirname, '../../config/modelParams'),
      // 如果在resources目录中
      path.join(__dirname, '../config/modelParams'),
    ];

    for (const configPath of possiblePaths) {
      if (fs.existsSync(configPath)) {
        console.log(`Found model config directory at: ${configPath}`);
        return configPath;
      }
    }

    // 如果都找不到，使用默认路径并记录警告
    const defaultPath = possiblePaths[0];
    console.warn(`Model config directory not found, using default: ${defaultPath}`);
    return defaultPath;
  }

  public static getInstance(): NodeModelConfigManager {
    if (!NodeModelConfigManager.instance) {
      NodeModelConfigManager.instance = new NodeModelConfigManager();
    }
    return NodeModelConfigManager.instance;
  }

  /**
   * 初始化模型配置
   */
  public async initialize(): Promise<void> {
    if (this.initialized) return;

    try {
      await this.loadAllModelConfigs();
      this.buildDisplayNameMap();
      this.initialized = true;
      console.log('Node model config manager initialized successfully');
    } catch (error) {
      console.error('Failed to initialize node model config manager:', error);
      throw error;
    }
  }

  /**
   * 加载所有模型配置文件
   */
  private async loadAllModelConfigs(): Promise<void> {
    try {
      // 检查配置目录是否存在
      if (!fs.existsSync(this.configDir)) {
        console.warn(`Model config directory not found: ${this.configDir}`);
        return;
      }

      // 读取配置目录中的所有JSON文件
      const files = fs.readdirSync(this.configDir);
      const jsonFiles = files.filter(file => file.endsWith('.json'));

      for (const fileName of jsonFiles) {
        try {
          const filePath = path.join(this.configDir, fileName);
          const fileContent = fs.readFileSync(filePath, 'utf-8');
          const config: ModelConfig = JSON.parse(fileContent);

          const configId = fileName.replace('.json', '');

          // 自动推断分类（如果配置文件中没有指定）
          if (!config.category) {
            config.category = this.inferCategory(configId);
          }

          const metadata: ModelMetadata = {
            id: configId,
            fileName,
            config
          };

          this.modelConfigs.set(configId, metadata);
          console.log(`Loaded model config: ${configId}`);
        } catch (error) {
          console.warn(`Failed to load model config: ${fileName}`, error);
        }
      }
    } catch (error) {
      console.error('Error loading model configs:', error);
      throw error;
    }
  }

  /**
   * 根据模型ID推断分类
   */
  private inferCategory(modelId: string): ModelConfig['category'] {
    const treeModels = ['DecisionTreeRegressor', 'RandomForestRegressor', 'GradientBoostingRegressor', 'XGBoost'];
    const linearModels = ['LinearRegression', 'Ridge', 'Lasso', 'ElasticNet'];
    
    if (treeModels.includes(modelId)) return 'tree';
    if (linearModels.includes(modelId)) return 'linear';
    return 'ml';
  }

  /**
   * 构建显示名称映射
   */
  private buildDisplayNameMap(): void {
    this.modelDisplayNameMap.clear();
    
    this.modelConfigs.forEach((metadata, id) => {
      this.modelDisplayNameMap.set(id, metadata.config.displayName);
    });
  }

  /**
   * 获取所有模型配置
   */
  public getAllModels(): ModelMetadata[] {
    return Array.from(this.modelConfigs.values());
  }

  /**
   * 根据ID获取模型配置
   */
  public getModelById(id: string): ModelMetadata | undefined {
    return this.modelConfigs.get(id);
  }

  /**
   * 获取模型显示名称
   */
  public getModelDisplayName(modelId: string): string {
    // 如果没有初始化或找不到配置，使用默认映射
    if (!this.initialized || this.modelDisplayNameMap.size === 0) {
      return this.getDefaultDisplayName(modelId);
    }
    return this.modelDisplayNameMap.get(modelId) || this.getDefaultDisplayName(modelId);
  }

  /**
   * 获取默认显示名称（当配置文件不可用时的降级方案）
   */
  private getDefaultDisplayName(modelId: string): string {
    const defaultMap: Record<string, string> = {
      'DecisionTreeRegressor': '决策树',
      'RandomForestRegressor': '随机森林',
      'XGBoost': 'XGBoost',
      'GradientBoostingRegressor': '梯度提升回归',
      'SVR': '支持向量机',
      'MLPRegressor': '人工神经网络',
      'LinearRegression': '多元线性回归',
      'Ridge': '岭回归',
      'Lasso': 'Lasso回归',
      'ElasticNet': '弹性网络回归'
    };
    return defaultMap[modelId] || modelId;
  }

  /**
   * 获取所有模型的显示名称映射
   */
  public getModelDisplayNameMap(): Record<string, string> {
    const map: Record<string, string> = {};
    this.modelDisplayNameMap.forEach((displayName, id) => {
      map[id] = displayName;
    });
    return map;
  }

  /**
   * 检查模型是否为ML模型（非线性模型）
   */
  public isMLModel(modelId: string): boolean {
    const metadata = this.getModelById(modelId);
    return metadata ? metadata.config.category !== 'linear' : false;
  }

  /**
   * 根据分类获取模型
   */
  public getModelsByCategory(category: string): ModelMetadata[] {
    return Array.from(this.modelConfigs.values()).filter(
      metadata => metadata.config.category === category
    );
  }

  /**
   * 重新加载所有配置
   */
  public async reload(): Promise<void> {
    this.modelConfigs.clear();
    this.modelDisplayNameMap.clear();
    this.initialized = false;
    await this.initialize();
  }

  /**
   * 获取初始化状态
   */
  public isInitialized(): boolean {
    return this.initialized;
  }
}

// 导出单例实例
export const nodeModelConfigManager = NodeModelConfigManager.getInstance();

// 便捷函数
export const initializeNodeModelManager = () => nodeModelConfigManager.initialize();
export const getNodeModelDisplayName = (modelId: string) => nodeModelConfigManager.getModelDisplayName(modelId);
export const getNodeModelDisplayNameMap = () => nodeModelConfigManager.getModelDisplayNameMap();
export const getNodeModelById = (id: string) => nodeModelConfigManager.getModelById(id);
export const isNodeMLModel = (modelId: string) => nodeModelConfigManager.isMLModel(modelId);
