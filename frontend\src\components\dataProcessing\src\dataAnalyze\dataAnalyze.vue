<template>
  <div v-if="visible" class="analyze-container">
    <!-- 数据概览 -->
    <div class="section-title">
      <StatIcon class="title-icon" />
      <span>数据分析</span>
    </div>
    <div class="overview-cards">
      <div class="stat-card">
        <div class="stat-content">
          <IconStatRect class="card-icon" />
          <div class="stat-info">
            <div class="stat-value">{{ rowCount }}</div>
            <div class="stat-label">数据总量</div>
          </div>
        </div>
      </div>

      <div class="stat-card">
        <div class="stat-content">
          <IconStatRect class="card-icon" />
          <div class="stat-info">
            <div class="stat-value">{{ columnCount }}</div>
            <div class="stat-label">列数</div>
          </div>
        </div>
      </div>

      <div class="stat-card">
        <div class="stat-content">
          <IconStatRect class="card-icon" />
          <div class="stat-info">
            <div class="stat-value">{{ totalMissingCount }}</div>
            <div class="stat-label">缺失值总数</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 列统计分析 -->
    <div class="section-header">
      <div class="section-title">
        <StatIcon class="title-icon" />
        <span>列统计分析</span>
      </div>
      <div class="search-container">
        <el-input
          v-model="searchKeyword"
          placeholder="搜索列名称"
          class="search-input"
          clearable
          size="small"
        >
          <template #prefix>
            <el-icon><Search /></el-icon>
          </template>
        </el-input>
      </div>
    </div>
    <div v-if="loading" class="loading-container">
      <el-skeleton :rows="3" animated />
    </div>
    <div v-else-if="filteredColumnStats.length > 0" class="column-analysis-container">
      <div class="column-stats-cards">
        <div class="cards-wrapper">
          <div
            v-for="stat in paginatedStats"
            :key="stat.column"
            class="column-stat-card"
          >
            <div class="column-name-badge">
              <span>{{ stat.column }}</span>
            </div>
            <div class="column-stats-details">
              <div class="stat-detail-item">
                <div class="stat-detail-value">
                  {{ stat.mean.toFixed(2) }}
                </div>
                <div class="stat-detail-label">均值</div>
              </div>
              <div class="stat-detail-item">
                <div class="stat-detail-value">
                  {{ stat.median.toFixed(2) }}
                </div>
                <div class="stat-detail-label">中位数</div>
              </div>
              <div class="stat-detail-item">
                <div class="stat-detail-value">
                  {{ stat.std.toFixed(2) }}
                </div>
                <div class="stat-detail-label">标准差</div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div v-if="totalPages > 1" class="pagination-dots">
        <span
          v-for="page in totalPages"
          :key="page"
          :class="['dot', { active: currentPage === page - 1 }]"
          @click="goToPage(page - 1)"
        />
      </div>
    </div>
    <el-empty v-else description="无数据或无法计算统计信息" />
  </div>
</template>

<script setup lang="ts">
import {
  ref,
  computed,
  onMounted,
  nextTick,
  onBeforeUnmount,
  watchEffect,
  watch
} from "vue";
import StatIcon from "@/assets/svg/icon_stat.svg?component";
import IconStatRect from "@/assets/svg/icon_stat_rect.svg?component";
import type { TableInstance } from "../dataTable/types";
import { Search } from '@element-plus/icons-vue'
import { ElInput } from 'element-plus'

// 导入性能优化工具
import {
  dataAnalysisCache,
  batchDataProcessor,
  StatsCalculator,
  measureAsyncPerformance,
  type DataStats,
  type ColumnStats
} from "@/utils/dataAnalysisOptimization";

const props = defineProps<{
  tableInstance?: TableInstance;
  visible: boolean;
}>();

// 缓存数据和统计信息
const cachedData = ref<any[][]>([]);
const cachedHeaders = ref<string[]>([]);
const cachedStats = ref<DataStats>({
  rowCount: 0,
  columnCount: 0,
  missingValues: {},
  totalMissingCount: 0,
  dataHash: ""
});
const columnStats = ref<ColumnStats[]>([]);

// 加载状态
const loading = ref(true);

// 分页状态
const currentPage = ref(0);
const itemsPerPage = ref(6); // 2x3 grid

const totalPages = computed(() => {
  return Math.ceil(columnStats.value.length / itemsPerPage.value);
});

const searchKeyword = ref("");

// 过滤后的列统计
const filteredColumnStats = computed(() => {
  if (!searchKeyword.value) return columnStats.value;
  return columnStats.value.filter(stat =>
    stat.column.toLowerCase().includes(searchKeyword.value.toLowerCase())
  );
});

// 分页时用过滤后的数据
const paginatedStats = computed(() => {
  const start = currentPage.value * itemsPerPage.value;
  const end = start + itemsPerPage.value;
  return filteredColumnStats.value.slice(start, end);
});

watch(filteredColumnStats, () => {
  // 搜索后如果当前页超出范围，重置到第一页
  if (currentPage.value > Math.floor(filteredColumnStats.value.length / itemsPerPage.value)) {
    currentPage.value = 0;
  }
});

const goToPage = page => {
  currentPage.value = page;
};

// 实时获取表格数据
const data = computed(() => {
  if (!props.tableInstance) return [];
  try {
    return props.tableInstance.getData() || [];
  } catch (error) {
    console.warn("Failed to get table data:", error);
    return [];
  }
});

// 实时获取表格头部
const header = computed(() => {
  if (!props.tableInstance) return [];
  try {
    const columns = props.tableInstance.getColumns() || [];
    return columns.map(col => col.title || col.data);
  } catch (error) {
    console.warn("Failed to get table columns:", error);
    return [];
  }
});

// 使用缓存的基本信息
const rowCount = computed(() => cachedStats.value.rowCount);
const columnCount = computed(() => cachedStats.value.columnCount);
const totalMissingCount = computed(() => cachedStats.value.totalMissingCount);

// 异步计算统计信息
const calculateAllStats = measureAsyncPerformance(async () => {
  loading.value = true;
  const currentData = data.value;
  const currentHeaders = header.value;

  if (currentData.length === 0 || currentHeaders.length === 0) {
    resetStats();
    loading.value = false;
    return;
  }

  const dataHash = dataAnalysisCache.generateDataHash(
    currentData,
    currentHeaders
  );
  // Check for cached overall stats
  const cached = dataAnalysisCache.getStats(dataHash);

  if (cached) {
    cachedStats.value = cached;
    // Check for cached column stats
    const allCached = currentHeaders.every(header =>
      dataAnalysisCache.getColumnStats(`${dataHash}-${header}`)
    );
    if (allCached) {
      columnStats.value = currentHeaders.map(header => {
        const colStats = dataAnalysisCache.getColumnStats(
          `${dataHash}-${header}`
        )!;
        return { column: header, ...colStats };
      });
      loading.value = false;
      return;
    }
  }

  await nextTick();

  try {
    const missingCounts = await batchDataProcessor.calculateMissingValues(
      currentData,
      currentHeaders
    );
    const totalMissing = Object.values(missingCounts).reduce(
      (sum, count) => sum + count,
      0
    );

    const statsResult: DataStats = {
      rowCount: currentData.length,
      columnCount: currentHeaders.length,
      missingValues: missingCounts,
      totalMissingCount: totalMissing,
      dataHash
    };
    cachedStats.value = statsResult;
    dataAnalysisCache.setStats(dataHash, statsResult);

    // Calculate stats for all columns
    const allColumnStats: ColumnStats[] = [];
    for (let i = 0; i < currentHeaders.length; i++) {
      const header = currentHeaders[i];
      const values = await batchDataProcessor.extractNumericValues(
        currentData,
        i
      );
      if (values.length > 0) {
        const colStats = StatsCalculator.calculateBasicStats(values);
        allColumnStats.push({ column: header, ...colStats });
        // Cache each column's stats individually
        dataAnalysisCache.setColumnStats(`${dataHash}-${header}`, colStats);
      }
    }
    columnStats.value = allColumnStats;
  } catch (error) {
    console.error("Failed to calculate stats:", error);
    resetStats();
  } finally {
    loading.value = false;
  }
}, "数据统计计算");

const resetStats = () => {
  cachedStats.value = {
    rowCount: 0,
    columnCount: 0,
    missingValues: {},
    totalMissingCount: 0,
    dataHash: ""
  };
  columnStats.value = [];
};

// 监听可见性和数据变化
watchEffect(() => {
  if (props.visible) {
    calculateAllStats();
  }
});

onMounted(() => {
  // No longer need resize listener if we have a fixed grid
});

onBeforeUnmount(() => {
  // No longer need resize listener if we have a fixed grid
});
</script>

<style lang="scss" scoped>
.analyze-container {
  padding: 16px;
  background: transparent;
}

.section-title {
  font-size: 16px;
  font-weight: bold;
  color: #3d3d3d;
  margin-bottom: 6px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.title-icon {
  width: 14px;
  height: 14px;
}

.overview-cards {
  display: flex;
  gap: 16px;
  margin-bottom: 12px;
  flex-wrap: wrap;
  padding: 8px;
}

.stat-card {
  width: 170px;
  height: 62px;
  border-radius: 2px;
  background: #ffffff;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  border: none;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
}

.stat-content {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 0 16px;
  width: 100%;
}

.card-icon {
  width: 36px;
  height: 36px;
}

.stat-info {
  display: flex;
  flex-direction: column;
}

.stat-value {
  font-family: Anton, sans-serif;
  font-size: 16px;
  font-weight: bold;
  color: #3d3d3d;
}

.stat-label {
  font-size: 14px;
  color: #999999;
}

.section-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 6px;
}

.search-container {
  min-width: 180px;
}

.search-input {
  border-radius: 18px !important;
  .el-input__inner {
    border-radius: 18px !important;
  }
}

.column-analysis-container {
  overflow: visible;
  padding: 4px;
}

.column-stats-cards {
  overflow: visible;
}

.cards-wrapper {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  padding: 4px;
}

.column-stat-card {
  flex-basis: calc(33.333% - 11px); /* 3 columns with gap */
  height: 72px;
  border-radius: 2px;
  background: #ffffff;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  padding: 0 16px;
  gap: 16px;
}

.column-name-badge {
  width: 94px;
  height: 36px;
  border-radius: 12px;
  background: rgba(98, 46, 255, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;

  span {
    font-size: 14px;
    font-weight: bold;
    text-align: center;
    color: #622EFF;;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    padding: 0 4px;
  }
}

.column-stats-details {
  display: flex;
  justify-content: space-around;
  flex-grow: 1;
}

.stat-detail-item {
  display: flex;
  flex-direction: column;
}

.stat-detail-value {
  font-family: Anton, sans-serif;
  font-size: 16px;
  font-weight: bold;
  color: #3d3d3d;
}

.stat-detail-label {
  font-size: 14px;
  color: #999999;
}

.pagination-dots {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: 16px;
  gap: 8px;
}

.dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: #dcdfe6;
  cursor: pointer;
  transition: all 0.3s ease;

  &.active {
    width: 20px;
    border-radius: 4px;
    background-color: var(--el-color-primary);
  }
}

.loading-container {
  padding: 40px;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .column-stat-card {
    flex-basis: calc(50% - 8px); /* 2个 */
  }
}
@media (max-width: 992px) {
  .column-stat-card {
    flex-basis: 100%; /* 1个 */
  }
}
@media (max-width: 768px) {
  .overview-cards {
    grid-template-columns: 1fr;
  }
  .column-stat-card {
    flex-basis: 100%; /* 1个 */
  }
}
</style>

