// <!--
// * @Description:封装导出echarts图表工具
// * @Author:李盈谕
// * @Date: Sun Apr 27 2025 15:08:54
// -->

import type { ECharts } from "echarts";

export function exportChartInstance(
  chartInstance: ECharts | undefined,
  type: "png" | "svg" = "png"
) {
  if (!chartInstance) {
    console.warn("图表实例不存在");
    return;
  }

  const dataURL = chartInstance.getDataURL({
    type,
    pixelRatio: 2,
    backgroundColor: "#fff"
  });

  const link = document.createElement("a");
  link.href = dataURL;
  link.download = `chart-${Date.now()}.${type}`;
  link.click();
}
