/**
 * 模型信息接口
 */
interface Model {
    id: number;
    name: string;
    taskUid: string;
    targetName: string;
    targetValues: number[];
    status: "completed" | "running" | "failed";
    progress: string;
    asynchronous: boolean;
    isRabbitmqReady: boolean;
    createdAt: string;
    updatedAt: string;
    error: string | null;
}

/**
 * 模型列表响应
 */
interface GetModelListResponse {
    code: number;
    data: Model[];
    msg: string;
}

/**
 * 优化请求参数
 */
interface OptimizationRequest {
    taskUid: string;       // 预测模型ID，更名为taskUid
    target: {              // 优化目标对象
        name: string;        // 优化目标名称
        value: number;       // 目标值
    };
    search: {              // 搜索算法参数对象
        algorithm: string;   // 优化算法ID
        params: Record<string, any>; // 优化算法参数
    };
}

/**
 * 优化结果接口
 */
interface OptimizationResult {
    code: number;
    data: Array<Record<string, number>>; // 优化结果数组
    msg: string;
}

