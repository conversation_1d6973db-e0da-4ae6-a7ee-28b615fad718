<template>
  <div class="chart-content" ref="chartRef"></div>
</template>

<script setup lang="ts">
import { ref, onMounted, onBeforeUnmount, watch } from "vue";
import * as echarts from "echarts";
import "echarts/extension/dataTool";
import type { ECharts } from "echarts";

const props = defineProps<{
  chartData: any[][];
  columns: string[];
  selectedColumns: string[];
}>();

const chartRef = ref<HTMLElement | null>(null);
let chartInstance: ECharts | null = null;

// 导出函数，供父组件调用
const getChartInstance = () => chartInstance;
const getChartOptions = () => createBoxplotOption();
const getChartData = () => {
  if (props.selectedColumns.length === 0) return null;
  
  const valueIndex = props.columns.indexOf(props.selectedColumns[0]);
  
  // 获取原始数据
  const values = props.chartData
    .map(row => parseFloat(row[valueIndex]))
    .filter(val => !isNaN(val));
  
  if (values.length === 0) return null;
  
  // 计算统计信息
  const sortedValues = values.sort((a, b) => a - b);
  const min = sortedValues[0];
  const max = sortedValues[sortedValues.length - 1];
  const q1 = sortedValues[Math.floor(sortedValues.length * 0.25)];
  const median = sortedValues[Math.floor(sortedValues.length * 0.5)];
  const q3 = sortedValues[Math.floor(sortedValues.length * 0.75)];
  
  return {
    columns: ['统计指标', '值'],
    data: [
      ['最小值', min],
      ['下四分位数', q1],
      ['中位数', median],
      ['上四分位数', q3],
      ['最大值', max],
      ['数据点数量', values.length]
    ]
  };
};
defineExpose({ getChartInstance, getChartOptions, getChartData });

// 创建箱线图
const createBoxplotOption = () => {
  if (props.selectedColumns.length === 0) return {};
  
  // 始终使用第一个选择的列作为数据源
  const valueIndex = props.columns.indexOf(props.selectedColumns[0]);
  
  // 单列模式：使用整个数据集作为一个箱线图
  const values = props.chartData
    .map(row => parseFloat(row[valueIndex]))
    .filter(val => !isNaN(val));
  
  if (values.length === 0) return {};
  
  // 确保echarts.dataTool是可用的
  if (!echarts.dataTool) {
    console.error('ECharts dataTool extension is not loaded');
    return {};
  }
  
  // 使用ECharts的prepareBoxplotData处理数据
  const boxplotData = echarts.dataTool.prepareBoxplotData([values]);
  
  return {
    title: {
      text: props.selectedColumns[0],
      left: 'center',
      show: false
    },
    grid: {
      left: '10%',
      right: '10%',
      bottom: '15%',
      top: '10%'  // 为顶部图例提供更多空间
    },
    tooltip: {
      trigger: 'item',
      formatter: function(param) {
        if (param.componentSubType === 'boxplot') {
          return [
            `${props.selectedColumns[0]}: `,
            `最大值: ${param.data[5].toFixed(2)}`,
            `上四分位数: ${param.data[4].toFixed(2)}`,
            `中位数: ${param.data[3].toFixed(2)}`,
            `下四分位数: ${param.data[2].toFixed(2)}`,
            `最小值: ${param.data[1].toFixed(2)}`
          ].join('<br/>');
        } else {
          return param.name + ': ' + param.data;
        }
      }
    },
    xAxis: {
      type: 'category',
      data: [props.selectedColumns[0]],
      boundaryGap: true,
      nameGap: 30,
      splitArea: { show: false },
      axisLabel: {
        formatter: function (val) {
          return val.length > 10 ? val.substring(0, 10) + '...' : val;
        }
      },
      splitLine: { show: false }
    },
    yAxis: {
      type: 'value',
      splitArea: { show: true }
    },
    series: [
      {
        name: 'boxplot',
        type: 'boxplot',
        datasetIndex: 1,
        itemStyle: {
          borderColor: '#005DFF',
          borderWidth: 2
        },
        emphasis: {
          itemStyle: {
            borderColor: '#178BFB'
          }
        },
        data: boxplotData.boxData,
      },
      {
        name: 'outlier',
        type: 'scatter',
        datasetIndex: 1,
        itemStyle: {
          color: '#FF0000'
        },
        data: boxplotData.outliers
      }
    ]
  };
};

// 初始化图表
const initChart = () => {
  if (chartRef.value) {
    chartInstance = echarts.init(chartRef.value);
    updateChart();
  }
};

// 更新图表
const updateChart = () => {
  if (chartInstance) {
    const option = createBoxplotOption();
    chartInstance.setOption(option);
  }
};

// 监听数据变化，更新图表
watch(() => props.chartData, updateChart, { deep: true });
watch(() => props.selectedColumns, updateChart, { deep: true });

// 窗口大小变化时，重新调整图表大小
const handleResize = () => {
  if (chartInstance) {
    chartInstance.resize();
  }
};

onMounted(() => {
  initChart();
  window.addEventListener('resize', handleResize);
});

onBeforeUnmount(() => {
  if (chartInstance) {
    chartInstance.dispose();
    chartInstance = null;
  }
  window.removeEventListener('resize', handleResize);
});
</script>

<style scoped>
.chart-content {
  width: 100%;
  height: 100%;
}
</style> 