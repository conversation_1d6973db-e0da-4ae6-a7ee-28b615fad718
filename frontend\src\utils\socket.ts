import { io, Socket } from "socket.io-client";
import { ElMessage } from "element-plus";

/**
 * 根据模型状态获取显示消息
 */
const getStatusMessage = (status: string): string => {
  // 处理中文状态
  if (status === "计算完成") {
    return "模型构建已完成";
  }

  // 处理状态可能包含的特定关键字
  if (status && typeof status === "string") {
    if (status.includes("完成")) {
      return "模型构建已完成";
    }
    if (status.includes("计算中")) {
      return "正在执行复杂计算...";
    }
    if (status.includes("开始")) {
      return "开始构建模型...";
    }
  }

  // 处理英文状态
  switch (status) {
    case "training":
      return "正在训练模型...";
    case "validating":
      return "正在验证模型...";
    case "preprocessing":
      return "正在预处理数据...";
    case "preparing":
      return "正在准备模型环境...";
    case "testing":
      return "正在测试模型性能...";
    case "completed":
      return "模型构建已完成";
    case "error":
      return "模型构建失败";
    default:
      return "正在处理中...";
  }
};

// 添加全局类型声明
declare global {
  interface Window {
    ipcRenderer?: {
      on: (channel: string, listener: (...args: any[]) => void) => void;
      off: (channel: string, listener: (...args: any[]) => void) => void;
      send: (channel: string, ...args: any[]) => void;
      invoke: (channel: string, ...args: any[]) => Promise<any>;
      removeAllListeners: (channel: string) => void;
    };
  }
}

let socket: Socket | null = null;
const SOCKET_CONNECTED_KEY = "socket_is_connected";

export const initializeSocket = async () => {
  // 严格检查环境变量
  const socketEnabled = import.meta.env.VITE_SOCKETIO_ENABLED;
  console.log("Socket.IO enabled:", socketEnabled);

  if (socketEnabled !== "true") {
    console.log("Socket.IO is explicitly disabled");
    return Promise.resolve(); // 直接返回已解决的 Promise
  }

  // 检查是否已经有其他窗口创建了连接
  const isMainWindow = !window.opener; // 判断是否为主窗口
  const isConnected = localStorage.getItem(SOCKET_CONNECTED_KEY) === "true";

  if (!isMainWindow && isConnected) {
    console.log(
      "This is a child window and a socket connection already exists in the main window. Skipping connection.",
    );
    return Promise.resolve();
  }

  const socketUrl = import.meta.env.VITE_SOCKETIO_URL;
  const socketPath = import.meta.env.VITE_SOCKETIO_PATH;

  if (!socketUrl) {
    console.log("Socket.IO URL is not configured");
    return Promise.resolve(); // URL 未配置时也不连接
  }

  socket = io(socketUrl, {
    path: socketPath,
    transports: ["websocket", "polling"],
    autoConnect: false, // 改为手动连接
    reconnection: true,
    reconnectionAttempts: 5,
    reconnectionDelay: 1000,
    timeout: 20000,
    forceNew: true,
  });

  // 将socket实例暴露给全局window对象，供主进程访问
  if (isMainWindow && typeof window !== "undefined") {
    (window as any).socketInstance = socket;
    console.log("Socket instance exposed to window object for main window");
  }

  socket.on("connect", () => {
    console.log("Socket.IO connected");
    localStorage.setItem(SOCKET_CONNECTED_KEY, "true");
    ElMessage.success("已连接到服务器");
  });

  socket.on("disconnect", () => {
    console.log("Socket.IO disconnected");
    localStorage.removeItem(SOCKET_CONNECTED_KEY);
    ElMessage.warning("与服务器的连接已断开");
  });

  socket.on("error", (error: Error) => {
    console.error("Socket.IO error:", error);
    localStorage.removeItem(SOCKET_CONNECTED_KEY);
    ElMessage.error("连接错误：" + error.message);
  });

  socket?.on("model_info", (data: any) => {
    console.log("Model info update received:", data);
    try {
      // 如果data是字符串，尝试解析
      let parsedData = data;
      if (typeof data === "string") {
        try {
          parsedData = JSON.parse(data);
        } catch (e) {
          console.error("Failed to parse model_info data:", e);
        }
      }

      // 获取data中的信息
      const responseData = parsedData.data || parsedData;

      // 扩展完成状态检测
      const isCompleted =
        responseData.status === "completed" ||
        responseData.progress === "completed" ||
        responseData.progress === "100" ||
        (typeof responseData.status === "string" &&
          responseData.status.includes("完成"));

      // 检查是否包含模型信息对象或100%进度
      const hasModelInfo =
        (responseData.modelInfo &&
          typeof responseData.modelInfo === "object") ||
        (responseData.progress === "100" && responseData.modelInfo);

      // 检查是否包含完整的模型结果
      if (isCompleted && hasModelInfo) {
        console.log("收到完整的模型结果数据，直接触发complete事件");

        // 构造结果事件数据
        const resultData = {
          taskUid: responseData.taskUid,
          status: "completed",
          result: responseData.modelInfo,
          // 保留原始数据，便于调试
          originalData: responseData,
        };

        // 触发模型完成事件，直接传递结果数据
        window.dispatchEvent(
          new CustomEvent("model_complete", { detail: resultData }),
        );

        // 发送到Electron主进程
        if (window.ipcRenderer) {
          window.ipcRenderer.send("model_complete", resultData);
        }

        ElMessage.success("模型构建完成");
      } else {
        // 常规的进度更新处理
        const normalizedData = {
          taskUid: responseData.taskUid,
          progress: parseInt(responseData.progress || "0", 10),
          status: responseData.status || "processing",
          message:
            responseData.message || getStatusMessage(responseData.status),
        };

        console.log("Normalized model_info data:", normalizedData);

        // 触发进度更新事件
        window.dispatchEvent(
          new CustomEvent("model_progress", { detail: normalizedData }),
        );

        // 发送到Electron主进程，以便通知其他窗口
        if (window.ipcRenderer) {
          window.ipcRenderer.send("model_progress_update", normalizedData);
        }
      }
    } catch (err) {
      console.error("Error handling model_info event:", err);
    }
  });

  socket?.on("model_progress", (data: any) => {
    console.log("Model progress update:", data);

    // 统一使用taskUid，处理可能的数据格式差异
    const normalizedData = {
      taskUid: data.taskUid !== undefined ? data.taskUid : null,
      progress: data.progress || 0,
      status: data.status || "processing",
      message: data.message || "正在处理中...",
    };

    // 触发进度更新事件，让组件更新进度条
    window.dispatchEvent(
      new CustomEvent("model_progress", { detail: normalizedData }),
    );

    // 发送到Electron主进程，以便通知其他窗口
    if (window.ipcRenderer) {
      window.ipcRenderer.send("model_progress_update", normalizedData);
    }
  });

  socket?.on("model_build_task", (data: any) => {
    // 适应多种可能的数据格式
    // 统一使用 taskUid
    const taskUid = data.taskUid !== undefined ? data.taskUid : null;

    if (taskUid === undefined) {
      console.error("收到无效的model_build_task事件，缺少taskUid属性:", data);
      return;
    }

    // 检查是否是完成状态
    const isCompleted =
      data.status === "completed" ||
      (typeof data.status === "string" && data.status.includes("完成")) ||
      data.progress === 100;

    // 如果是完成状态
    if (isCompleted) {
      console.log("模型构建任务完成，更新进度状态");

      // 直接触发model_complete事件（如果最后一次model_info已经返回了结果，这个可以作为备份）
      const completeData = {
        taskUid: taskUid,
        status: "completed",
        progress: 100,
        message: "模型构建已完成",
      };

      // 触发100%进度更新
      window.dispatchEvent(
        new CustomEvent("model_progress", { detail: completeData }),
      );
      if (window.ipcRenderer) {
        window.ipcRenderer.send("model_progress_update", completeData);
      }

      // 注意：已移除完成后的get_model_info请求，避免重复请求
    } else {
      // 格式转换为统一格式，只使用taskUid
      const normalizedData = {
        taskUid: taskUid,
        status: data.status,
        progress: data.progress || 0,
        message:
          data.message ||
          getStatusMessage(data.status) ||
          `任务${data.status === "completed" ? "已完成" : "进行中"}`,
      };

      console.log("Model build task update:", normalizedData);
      // 触发进度更新事件
      window.dispatchEvent(
        new CustomEvent("model_progress", { detail: normalizedData }),
      );
      // 发送到Electron主进程，以便通知其他窗口
      if (window.ipcRenderer) {
        window.ipcRenderer.send("model_progress_update", normalizedData);
      }
    }
  });

  socket?.on("model_complete", (data: any) => {
    console.log("Model complete:", data);

    // 统一使用taskUid
    const normalizedData = {
      taskUid: data.taskUid !== undefined ? data.taskUid : null,
      status: data.status,
      result: data.result,
    };

    // 触发完成事件，让组件显示结果
    window.dispatchEvent(
      new CustomEvent("model_complete", { detail: normalizedData }),
    );
    // 发送到Electron主进程，以便通知其他窗口
    if (window.ipcRenderer) {
      window.ipcRenderer.send("model_complete", normalizedData);
    }

    if (data.status === "completed") {
      ElMessage.success("模型构建完成");
    } else {
      ElMessage.error("模型构建失败：" + (data.result?.error || "未知错误"));
    }
  });

  return new Promise<void>((resolve, reject) => {
    const timeout = setTimeout(() => {
      if (socket) {
        socket.close();
        socket = null;
        localStorage.removeItem(SOCKET_CONNECTED_KEY);
      }
      reject(new Error("连接超时"));
    }, 5000);

    if (!socket) {
      reject(new Error("Socket not initialized"));
      return;
    }

    socket.on("connect", () => {
      clearTimeout(timeout);
      resolve();
    });

    socket.on("connect_error", (error: any) => {
      clearTimeout(timeout);
      if (socket) {
        socket.close();
        socket = null;
        localStorage.removeItem(SOCKET_CONNECTED_KEY);
      }
      reject(error);
    });

    // 手动开始连接
    socket.connect();
  });
};

// 在窗口关闭时断开连接
if (typeof window !== "undefined") {
  window.addEventListener("beforeunload", () => {
    if (socket?.connected) {
      socket.disconnect();
      localStorage.removeItem(SOCKET_CONNECTED_KEY);
    }
  });
}

// 导出获取 socket 实例的函数，而不是直接导出实例
export const getSocket = () => socket;
