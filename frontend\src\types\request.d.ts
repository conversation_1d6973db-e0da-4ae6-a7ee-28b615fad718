import type { AxiosInstance, InternalAxiosRequestConfig } from "axios";

interface ApiResponse<T = any> {
  code: number;
  msg: string;
  data: T;
  size?: string | number;
}

interface CustomRequestConfig extends InternalAxiosRequestConfig {
  skipLoading?: boolean;
}

interface CustomAxiosInstance
  extends Omit<AxiosInstance, "get" | "post" | "delete" | "put" | "patch"> {
  get<T = any, R = ApiResponse<T>>(
    url: string,
    config?: CustomRequestConfig,
  ): Promise<R>;
  post<T = any, R = ApiResponse<T>>(
    url: string,
    data?: any,
    config?: CustomRequestConfig,
  ): Promise<R>;
  delete<T = any, R = ApiResponse<T>>(
    url: string,
    config?: CustomRequestConfig,
  ): Promise<R>;
  put<T = any, R = ApiResponse<T>>(
    url: string,
    data?: any,
    config?: CustomRequestConfig,
  ): Promise<R>;
  patch<T = any, R = ApiResponse<T>>(
    url: string,
    data?: any,
    config?: CustomRequestConfig,
  ): Promise<R>;
}

export { ApiResponse, CustomRequestConfig, CustomAxiosInstance };
