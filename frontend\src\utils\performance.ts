/**
 * 性能监控工具
 * 用于监控表格组件的性能指标
 */

interface PerformanceMetric {
  name: string;
  startTime: number;
  endTime?: number;
  duration?: number;
}

class PerformanceMonitor {
  private metrics: Map<string, PerformanceMetric> = new Map();
  private enabled: boolean = process.env.NODE_ENV === 'development';

  /**
   * 开始性能监控
   * @param name 监控名称
   */
  start(name: string): void {
    if (!this.enabled) return;
    
    this.metrics.set(name, {
      name,
      startTime: performance.now(),
    });
  }

  /**
   * 结束性能监控
   * @param name 监控名称
   */
  end(name: string): number | undefined {
    if (!this.enabled) return;
    
    const metric = this.metrics.get(name);
    if (!metric) {
      console.warn(`Performance metric "${name}" not found`);
      return;
    }

    const endTime = performance.now();
    const duration = endTime - metric.startTime;
    
    metric.endTime = endTime;
    metric.duration = duration;

    console.log(`⏱️ ${name}: ${duration.toFixed(2)}ms`);
    return duration;
  }

  /**
   * 测量函数执行时间
   * @param name 监控名称
   * @param fn 要测量的函数
   */
  async measure<T>(name: string, fn: () => T | Promise<T>): Promise<T> {
    if (!this.enabled) {
      return await fn();
    }

    this.start(name);
    try {
      const result = await fn();
      this.end(name);
      return result;
    } catch (error) {
      this.end(name);
      throw error;
    }
  }

  /**
   * 获取所有性能指标
   */
  getMetrics(): PerformanceMetric[] {
    return Array.from(this.metrics.values());
  }

  /**
   * 清空所有性能指标
   */
  clear(): void {
    this.metrics.clear();
  }

  /**
   * 启用/禁用性能监控
   */
  setEnabled(enabled: boolean): void {
    this.enabled = enabled;
  }

  /**
   * 生成性能报告
   */
  generateReport(): string {
    const metrics = this.getMetrics().filter(m => m.duration !== undefined);
    
    if (metrics.length === 0) {
      return 'No performance metrics available';
    }

    const report = [
      '📊 Performance Report',
      '==================',
      ...metrics.map(m => `${m.name}: ${m.duration!.toFixed(2)}ms`),
      '',
      `Total metrics: ${metrics.length}`,
      `Average duration: ${(metrics.reduce((sum, m) => sum + m.duration!, 0) / metrics.length).toFixed(2)}ms`,
    ];

    return report.join('\n');
  }
}

// 创建全局实例
export const performanceMonitor = new PerformanceMonitor();

// 表格性能监控的便捷方法
export const tablePerformance = {
  /**
   * 监控数据加载性能
   */
  measureDataLoad: <T>(fn: () => T | Promise<T>) => 
    performanceMonitor.measure('Table Data Load', fn),

  /**
   * 监控工作表切换性能
   */
  measureSheetSwitch: <T>(fn: () => T | Promise<T>) => 
    performanceMonitor.measure('Sheet Switch', fn),

  /**
   * 监控表格渲染性能
   */
  measureRender: <T>(fn: () => T | Promise<T>) => 
    performanceMonitor.measure('Table Render', fn),

  /**
   * 监控数据更新性能
   */
  measureDataUpdate: <T>(fn: () => T | Promise<T>) => 
    performanceMonitor.measure('Data Update', fn),

  /**
   * 监控文件解析性能
   */
  measureFileParse: <T>(fn: () => T | Promise<T>) => 
    performanceMonitor.measure('File Parse', fn),
};

// 内存使用监控
export const memoryMonitor = {
  /**
   * 获取当前内存使用情况
   */
  getMemoryUsage(): any {
    if ('memory' in performance) {
      return (performance as any).memory;
    }
    return null;
  },

  /**
   * 记录内存使用情况
   */
  logMemoryUsage(label: string = 'Memory Usage'): void {
    const memory = this.getMemoryUsage();
    if (memory) {
      console.log(`💾 ${label}:`, {
        used: `${(memory.usedJSHeapSize / 1024 / 1024).toFixed(2)} MB`,
        total: `${(memory.totalJSHeapSize / 1024 / 1024).toFixed(2)} MB`,
        limit: `${(memory.jsHeapSizeLimit / 1024 / 1024).toFixed(2)} MB`,
      });
    }
  },

  /**
   * 强制垃圾回收（仅在开发环境下有效）
   */
  forceGC(): void {
    if (process.env.NODE_ENV === 'development' && 'gc' in window) {
      (window as any).gc();
      console.log('🗑️ Forced garbage collection');
    }
  },
};

// 导出默认实例
export default performanceMonitor;
