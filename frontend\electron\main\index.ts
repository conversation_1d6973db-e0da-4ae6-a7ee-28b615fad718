import { release } from "node:os";
import { fileURLToPath } from "node:url";
import { join, dirname } from "node:path";
import {
  type MenuItem,
  type MenuItemConstructorOptions,
  app,
  Menu,
  shell,
  ipcMain,
  BrowserWindow,
  dialog,
} from "electron";
import { spawn, ChildProcess, exec } from "node:child_process";
import { existsSync } from "node:fs";
import {
  checkExistingBackendProcesses,
  checkExistingRabbitMQProcesses,
} from "./utils";

// 在文件顶部添加 fs 和 path 的引入
// import { existsSync, mkdirSync, readFileSync, writeFileSync, readdirSync, Stats, statSync } from 'node:fs'
// import { dialog } from 'electron'

// The built directory structure
//
// ├─┬ dist-electron
// │ ├─┬ main
// │ │ └── index.js    > Electron-Main
// │ └─┬ preload
// │   └── index.mjs    > Preload-Scripts
// ├─┬ dist
// │ └── index.html    > Electron-Renderer
//
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);
process.env.DIST_ELECTRON = join(__dirname, "..");
process.env.DIST = join(process.env.DIST_ELECTRON, "../dist");
process.env.PUBLIC = process.env.VITE_DEV_SERVER_URL
  ? join(process.env.DIST_ELECTRON, "../public")
  : process.env.DIST;
// 是否为开发环境
const isDev = process.env["NODE_ENV"] === "development";

// Disable GPU Acceleration for Windows 7
if (release().startsWith("6.1")) app.disableHardwareAcceleration();

// Set application name for Windows 10+ notifications
if (process.platform === "win32") app.setAppUserModelId(app.getName());

if (!app.requestSingleInstanceLock()) {
  app.quit();
  process.exit(0);
}

// Remove electron security warnings
// This warning only shows in development mode
// Read more on https://www.electronjs.org/docs/latest/tutorial/security
// process.env['ELECTRON_DISABLE_SECURITY_WARNINGS'] = 'true'

let win: BrowserWindow | null = null; // Will become mainWindow
let backendProcess: ChildProcess | null = null; // 后端进程
let backendPID: number | null = null; // 存储后端进程PID
const runningBackendProcesses: number[] = []; // 存储所有正在运行的后端进程PID
let rabbitmqProcess: ChildProcess | null = null; // RabbitMQ进程
let isShuttingDown = false; // 添加关闭标志

// 用于暂存找不到目标窗口时的模型完成事件
const pendingModelCompleteEvents: Map<string, any> = new Map();

// Here, you can also use other preload
const preload = join(__dirname, "../preload/index.js");
const url = process.env.VITE_DEV_SERVER_URL;
const indexHtml = join(process.env.DIST, "index.html");

// 终止进程
async function killProcess(pid: number): Promise<boolean> {
  return new Promise((resolve) => {
    if (!pid || pid <= 0) {
      resolve(false);
      return;
    }

    console.log(`Attempting to kill process with PID: ${pid}`);

    try {
      // Windows使用taskkill命令终止进程树
      if (process.platform === "win32") {
        exec(`taskkill /pid ${pid} /T /F`, (err) => {
          if (err) {
            console.error(`Failed to kill process ${pid}:`, err);
            resolve(false);
          } else {
            console.log(`Successfully terminated process ${pid}`);
            // 从正在运行的进程列表中删除
            const index = runningBackendProcesses.indexOf(pid);
            if (index !== -1) {
              runningBackendProcesses.splice(index, 1);
            }
            resolve(true);
          }
        });
      } else {
        // 非Windows平台使用process.kill
        try {
          process.kill(pid, "SIGTERM");
          setTimeout(() => {
            try {
              // 检查进程是否还存在
              process.kill(pid, 0);
              // 如果没有抛出异常，则进程仍然存在，尝试SIGKILL
              process.kill(pid, "SIGKILL");
              console.log(`Had to use SIGKILL for ${pid}`);
            } catch (e) {
              // 进程已经终止
            }

            // 从正在运行的进程列表中删除
            const index = runningBackendProcesses.indexOf(pid);
            if (index !== -1) {
              runningBackendProcesses.splice(index, 1);
            }

            resolve(true);
          }, 1000);
        } catch (e) {
          console.error(`Failed to kill process ${pid}:`, e);
          resolve(false);
        }
      }
    } catch (error) {
      console.error(`Error in killProcess for PID ${pid}:`, error);
      resolve(false);
    }
  });
}

// 启动后端服务
async function startBackendService(): Promise<boolean> {
  return new Promise(async (resolve) => {
    try {
      // 先检查是否有已运行的后端进程
      const existingPids = await checkExistingBackendProcesses();

      // 终止所有已存在的进程
      for (const pid of existingPids) {
        await killProcess(pid);
      }

      // 如果当前已有进程，则不再启动新的
      if (backendProcess !== null && backendPID !== null) {
        console.log(`Backend service already running with PID: ${backendPID}`);
        resolve(true);
        return;
      }

      const backendExecutablePath = app.isPackaged
        ? join(process.resourcesPath, "backend", "runserver.exe")
        : join(app.getAppPath(), "../backend/dist/backend", "runserver.exe");

      console.log("Resource path:", process.resourcesPath);
      console.log(`Starting backend service from: ${backendExecutablePath}`);

      // Check if the file exists before spawning
      if (!existsSync(backendExecutablePath)) {
        console.error(
          `Backend executable not found at: ${backendExecutablePath}`,
        );
        resolve(false);
        return;
      }

      console.log(`Backend executable exists, attempting to spawn process`);

      backendProcess = spawn(backendExecutablePath, [], {
        windowsHide: false, // 在Windows上显示控制台窗口
        stdio: "pipe", // 改为pipe以便捕获输出
        cwd: app.isPackaged
          ? join(process.resourcesPath, "backend")
          : undefined, // Set working directory to backend folder in packaged app
        detached: false, // 确保进程不会脱离父进程
      });

      if (!backendProcess || !backendProcess.pid) {
        console.error("Failed to start backend service: Invalid process");
        resolve(false);
        return;
      }

      backendPID = backendProcess.pid;
      runningBackendProcesses.push(backendPID);

      console.log("Backend service started with PID:", backendPID);

      // 监听后端输出，确认启动成功
      let startupTimeout: NodeJS.Timeout;
      let isStarted = false;
      let hasFlaskApp = false;
      let hasRunningOn = false;

      const checkStartup = (data: Buffer) => {
        const output = data.toString();
        console.log("Backend output:", output);

        // 检查Flask应用是否启动
        if (
          output.includes("Serving Flask app") ||
          output.includes("Flask 应")
        ) {
          hasFlaskApp = true;
          console.log("Flask app detected");
        }

        // 检查服务器是否开始监听
        if (
          output.includes("Running on http://") ||
          output.includes("* Running on all addresses") ||
          output.includes("Press CTRL+C to quit")
        ) {
          hasRunningOn = true;
          console.log("Server listening detected");
        }

        // 只有当两个条件都满足时才认为启动成功
        if (hasFlaskApp && hasRunningOn && !isStarted) {
          isStarted = true;
          clearTimeout(startupTimeout);
          console.log(
            "Backend service started successfully - Flask app is serving and listening",
          );
          resolve(true);
        }

        // 或者直接检测到完整的启动信息
        if (
          (output.includes("Running on http://") &&
            output.includes("Press CTRL+C to quit")) ||
          output.includes("Server started") ||
          output.includes("Backend ready") ||
          output.includes("服务已启动")
        ) {
          if (!isStarted) {
            isStarted = true;
            clearTimeout(startupTimeout);
            console.log("Backend service started successfully");
            resolve(true);
          }
        }
      };

      const checkStartupError = (data: Buffer) => {
        const output = data.toString();
        console.log("Backend error:", output);

        // 检查Flask应用是否启动（有些信息可能在stderr中）
        if (
          output.includes("Serving Flask app") ||
          output.includes("Flask 应")
        ) {
          hasFlaskApp = true;
          console.log("Flask app detected (from stderr)");
        }

        // 检查服务器是否开始监听（有些信息可能在stderr中）
        if (
          output.includes("Running on http://") ||
          output.includes("* Running on all addresses") ||
          output.includes("Press CTRL+C to quit")
        ) {
          hasRunningOn = true;
          console.log("Server listening detected (from stderr)");
        }

        // 只有当两个条件都满足时才认为启动成功
        if (hasFlaskApp && hasRunningOn && !isStarted) {
          isStarted = true;
          clearTimeout(startupTimeout);
          console.log(
            "Backend service started successfully - Flask app is serving and listening (from stderr)",
          );
          resolve(true);
        }

        // 检查真正的错误信息
        if (
          output.includes("Address already in use") ||
          output.includes("Permission denied") ||
          output.includes("Fatal error") ||
          output.includes("Failed to start") ||
          (output.includes("Error:") &&
            !output.includes("INFO") &&
            !output.includes("WARNING"))
        ) {
          if (!isStarted) {
            console.error("Backend startup error detected:", output);
            clearTimeout(startupTimeout);
            resolve(false);
          }
        }
      };

      if (backendProcess.stdout) {
        backendProcess.stdout.on("data", checkStartup);
      }

      if (backendProcess.stderr) {
        backendProcess.stderr.on("data", checkStartupError);
      }

      backendProcess.on("error", (err) => {
        console.error("Failed to start backend service:", err);

        if (backendPID !== null) {
          const index = runningBackendProcesses.indexOf(backendPID);
          if (index !== -1) {
            runningBackendProcesses.splice(index, 1);
          }
        }

        backendProcess = null;
        backendPID = null;

        if (!isStarted) {
          clearTimeout(startupTimeout);
          resolve(false);
        }
      });

      backendProcess.on("close", (code) => {
        console.log(`Backend service exited with code ${code}`);

        if (backendPID !== null) {
          const index = runningBackendProcesses.indexOf(backendPID);
          if (index !== -1) {
            runningBackendProcesses.splice(index, 1);
          }
        }

        backendProcess = null;
        backendPID = null;

        if (!isStarted && !isShuttingDown) {
          clearTimeout(startupTimeout);
          resolve(false);
        }
      });

      // 设置启动超时 - 增加超时时间，因为后端需要初始化数据库等
      startupTimeout = setTimeout(() => {
        if (!isStarted) {
          console.error("Backend service startup timeout");
          console.log(
            `Startup status: hasFlaskApp=${hasFlaskApp}, hasRunningOn=${hasRunningOn}`,
          );
          resolve(false);
        }
      }, 45000); // 增加到45秒超时
    } catch (error) {
      console.error("Error starting backend service:", error);
      backendProcess = null;
      backendPID = null;
      resolve(false);
    }
  });
}

// 终止所有后端进程
async function terminateAllBackendProcesses() {
  console.log("Terminating all backend processes...");

  // 首先尝试终止当前记录的进程
  if (backendProcess !== null && backendPID !== null) {
    try {
      await killProcess(backendPID);
    } catch (error) {
      console.error(
        `Error terminating current backend process ${backendPID}:`,
        error,
      );
    }
    backendProcess = null;
    backendPID = null;
  }

  // 然后终止所有已知的后端进程
  const processes = [...runningBackendProcesses];
  for (const pid of processes) {
    await killProcess(pid);
  }

  // 最后检查系统中是否还有任何runserver.exe进程
  const remainingPids = await checkExistingBackendProcesses();
  for (const pid of remainingPids) {
    await killProcess(pid);
  }
}

// 终止所有RabbitMQ相关进程
async function terminateAllRabbitMQProcesses() {
  console.log("Terminating all RabbitMQ processes...");

  // 首先尝试正常终止当前RabbitMQ进程
  if (rabbitmqProcess !== null && rabbitmqProcess.pid) {
    try {
      console.log(
        `Terminating current RabbitMQ process with PID: ${rabbitmqProcess.pid}`,
      );
      await terminateRabbitMQ();
    } catch (error) {
      console.error(`Error terminating current RabbitMQ process:`, error);
    }
    rabbitmqProcess = null;
  }

  // 然后检查并终止系统中所有RabbitMQ相关进程
  try {
    const remainingPids = await checkExistingRabbitMQProcesses();
    console.log(
      `Found ${remainingPids.length} remaining RabbitMQ processes to terminate`,
    );
    for (const pid of remainingPids) {
      await killProcess(pid);
    }
  } catch (error) {
    console.error(`Error checking for remaining RabbitMQ processes:`, error);
  }
}

// 创建菜单
function createMenu(label = "进入全屏幕") {
  const menu = Menu.buildFromTemplate(
    appMenu(label) as (MenuItemConstructorOptions | MenuItem)[],
  );
  Menu.setApplicationMenu(menu);
}

async function createMainWindow(initialRoute?: string) {
  // Renamed from createWindow, added initialRoute
  win = new BrowserWindow({
    width: 1024,
    height: 768,
    minWidth: 1024,
    minHeight: 768,
    title: "Main window",
    icon: join(process.env.PUBLIC, "favicon.ico"),
    frame: false, // Remove default window frame
    transparent: true, // Enable transparency
    resizable: true, // Allow window resizing
    webPreferences: {
      preload,
      nodeIntegration: false, // Recommended for security
      contextIsolation: true, // Required for contextBridge
    },
  });

  // 窗口创建后立即最大化
  win.maximize();

  const targetUrl = initialRoute ? `${url}#${initialRoute}` : url;
  const targetIndexHtml = initialRoute
    ? { pathname: indexHtml, hash: initialRoute }
    : indexHtml;

  if (process.env.VITE_DEV_SERVER_URL) {
    // electron-vite-vue#298
    win.loadURL(targetUrl); // MainWindow loads the main content
    // Open devTool if the app is not packaged
    win.webContents.openDevTools({ mode: "bottom" }); // 打开开发者工具
  } else {
    win.loadFile(
      typeof targetIndexHtml === "string"
        ? targetIndexHtml
        : targetIndexHtml.pathname,
      typeof targetIndexHtml === "string" ? {} : { hash: targetIndexHtml.hash },
    ); // MainWindow loads the main content
  }

  createMenu();

  // Test actively push message to the Electron-Renderer
  win.webContents.on("did-finish-load", () => {
    win?.webContents.send("main-process-message", new Date().toLocaleString());
  });

  // Make all links open with the browser, not with the application
  win.webContents.setWindowOpenHandler(({ url }) => {
    // if (url.startsWith("https:")) shell.openExternal(url);
    // return { action: "deny" };
    // 这里用于新开一个页面--------->必须要把这个设置成允许，否则将不能使用路由打开一个子页面

    // 创建自定义窗口，隐藏菜单栏防止出现bug
    const isModelResultWindow = url.includes("/modelManagement/");
    const childWindow = new BrowserWindow({
      width: 1024,
      height: 768,
      minWidth: 1024,
      minHeight: 768,
      autoHideMenuBar: true, // 隐藏菜单栏，防止出现bug
      frame: !isModelResultWindow, // 对模型结果页面使用无边框窗口
      transparent: isModelResultWindow, // 为模型结果窗口启用透明
      backgroundColor: isModelResultWindow ? "#00000000" : "#fff", // 透明背景
      webPreferences: {
        preload,
        nodeIntegration: false,
        contextIsolation: true,
      },
      ...(isModelResultWindow
        ? {
            show: false, // 先不显示，等加载完成后再显示
          }
        : {}),
    });

    childWindow.loadURL(url);

    // 如果是模型结果窗口，等待内容加载完成后显示
    if (isModelResultWindow) {
      childWindow.once("ready-to-show", () => {
        childWindow.show();
      });
    }

    return { action: "deny" }; // 阻止默认行为，使用我们自定义的窗口
  });
  // win.webContents.on('will-navigate', (event, url) => { }) #344

  // 窗口进入全屏状态时触发
  win.on("enter-full-screen", () => {
    createMenu("退出全屏幕");
  });

  // 窗口离开全屏状态时触发
  win.on("leave-full-screen", () => {
    createMenu();
  });
}

// 存储任务ID和相关窗口的映射
const taskWindowMap = new Map<number, BrowserWindow>();

// 调试函数
const printEventData = (eventName: string, data: any) => {
  console.log(`[IPC Event] ${eventName} - 接收数据:`, data);
  console.log(`[IPC Event] ${eventName} - taskUid类型:`, typeof data.taskUid);
  console.log(`[IPC Event] ${eventName} - taskUid值:`, data.taskUid);
  if (data.taskUid === undefined) {
    console.log(`[IPC Event] ${eventName} - 警告: taskUid未定义!`);
  }
};

// WebSocket实例存储 - 只在主窗口维护一个WebSocket连接
let mainWindowSocket: any = null;

// 从主窗口获取WebSocket连接
const getMainWindowSocketStatus = () => {
  if (!win) return { connected: false };

  return new Promise((resolve) => {
    win?.webContents
      .executeJavaScript(
        `
      (function() {
        const socket = window.socketInstance;
        return { 
          connected: socket ? socket.connected : false,
          id: socket ? socket.id : null
        };
      })()
    `,
      )
      .then((status) => {
        console.log("Main window socket status:", status);
        resolve(status);
      })
      .catch((error) => {
        console.error("Error getting socket status:", error);
        resolve({ connected: false });
      });
  });
};

// 监听模型进度更新，并转发到相关窗口
ipcMain.on("model_progress_update", (event, data) => {
  printEventData("model_progress_update", data);

  const taskUid = data.taskUid;
  const targetWindow = taskWindowMap.get(taskUid);

  console.log(
    `Progress update for task ${taskUid}, target window exists: ${!!targetWindow}`,
  );

  if (targetWindow && !targetWindow.isDestroyed()) {
    targetWindow.webContents.send("model_progress_update", data);
  } else {
    console.log(
      `未找到窗口，当前映射中的taskUid有:`,
      Array.from(taskWindowMap.keys()),
    );
  }
});

// 监听模型完成事件，并转发到相关窗口
ipcMain.on("model_complete", (event, data) => {
  console.log(data);
  const taskUid = data.taskUid;
  const targetWindow = taskWindowMap.get(taskUid);

  console.log(
    `Model complete for task ${taskUid}, target window exists: ${!!targetWindow}`,
  );

  // 无论当前是否找到窗口，一律缓存，保证后续窗口可获取
  pendingModelCompleteEvents.set(taskUid, data);

  if (targetWindow && !targetWindow.isDestroyed()) {
    targetWindow.webContents.send("model_complete", data);
  }
});

// 监听打开模型结果窗口事件
ipcMain.on("open_model_result_window", (event, data) => {
  printEventData("open_model_result_window", data);
  const taskUid = data.taskUid;

  if (!taskUid) {
    console.error("无效的taskUid:", data.taskUid);
    return;
  }

  console.log(
    `Registering window for task ${taskUid} from open_model_result_window event`,
  );

  // 将发送事件的渲染进程所属的窗口标记为该任务ID对应的窗口
  const sourceWindow = BrowserWindow.fromWebContents(event.sender);
  if (sourceWindow) {
    taskWindowMap.set(taskUid, sourceWindow);
  }
});

// 监听模型结果窗口准备就绪事件
ipcMain.on("model_result_window_ready", (event, data) => {
  printEventData("model_result_window_ready", data);

  const taskUid = data.taskUid;

  if (!taskUid) {
    console.error("无效的taskUid:", data.taskUid);
    return;
  }

  console.log(`Model result window ready for task ${taskUid}`);

  // 将当前窗口注册为接收该任务ID的窗口
  const resultWindow = BrowserWindow.fromWebContents(event.sender);
  if (resultWindow) {
    taskWindowMap.set(taskUid, resultWindow);
    console.log(`Model result window registered for task ID: ${taskUid}`);

    // 如果之前已经收到完成事件但窗口尚未就绪，现在立即发送
    const pendingData = pendingModelCompleteEvents.get(taskUid);
    if (pendingData) {
      resultWindow.webContents.send("model_complete", pendingData);
      pendingModelCompleteEvents.delete(taskUid);
    }
  }
});

// 监听模型信息请求 - 由主窗口处理WebSocket请求
ipcMain.on("request_model_info", async (event, data) => {
  printEventData("request_model_info", data);

  const taskUid = data.taskUid;

  if (!taskUid) {
    console.error("无效的taskUid:", data.taskUid);
    event.sender.send("model_info_response", {
      code: 400,
      msg: "无效的任务ID",
    });
    return;
  }

  console.log(`处理模型信息请求，taskUid: ${taskUid}`);

  // 确保将当前窗口注册为该任务的接收窗口
  const resultWindow = BrowserWindow.fromWebContents(event.sender);
  if (resultWindow) {
    taskWindowMap.set(taskUid, resultWindow);
  }

  // 在主窗口中执行WebSocket请求
  if (!win || win.isDestroyed()) {
    console.error("主窗口不可用");
    event.sender.send("model_info_response", {
      code: 500,
      msg: "主窗口不可用，无法执行WebSocket请求",
    });
    return;
  }

  try {
    // 在主窗口中执行WebSocket请求
    const result = await win.webContents.executeJavaScript(`
      (function() {
        return new Promise((resolve) => {
          const socket = window.socketInstance;
          if (!socket || !socket.connected) {
            resolve({ code: 500, msg: 'WebSocket未连接' });
            return;
          }
          
          console.log('在主窗口中发送get_model_info请求, taskUid: ' + '${taskUid}');
          socket.emit('get_model_info', { taskUid: '${taskUid}' }, (response) => {
            console.log('主窗口收到get_model_info响应:', response);
            resolve(response || { code: 404, msg: '未收到响应' });
          });
          
          // 设置超时
          setTimeout(() => {
            resolve({ code: 408, msg: '请求超时' });
          }, 10000);
        });
      })()
    `);

    console.log("从主窗口获取到模型信息结果:", result);

    // 将结果发送回请求窗口
    event.sender.send("model_info_response", result);
  } catch (error: any) {
    console.error("执行WebSocket请求失败:", error);
    event.sender.send("model_info_response", {
      code: 500,
      msg: "执行请求失败: " + (error.message || "未知错误"),
    });
  }
});

// 启动RabbitMQ服务
async function startRabbitMQ(): Promise<boolean> {
  return new Promise(async (resolve) => {
    try {
      // 先检查是否有已运行的RabbitMQ进程
      const existingPids = await checkExistingRabbitMQProcesses();

      // 终止所有已存在的进程
      if (existingPids.length > 0) {
        console.log(
          `Terminating ${existingPids.length} existing RabbitMQ processes before starting new one`,
        );
        for (const pid of existingPids) {
          await killProcess(pid);
        }

        // 等待一些时间确保进程完全终止
        await new Promise((resolve) => setTimeout(resolve, 2000));
      }

      // 获取RabbitMQ脚本路径
      const rabbitmqScriptPath = app.isPackaged
        ? join(process.resourcesPath, "start_rabbitmq.bat")
        : join(app.getAppPath(), "../start_rabbitmq.bat");

      console.log(`Starting RabbitMQ service from: ${rabbitmqScriptPath}`);

      // 检查脚本文件是否存在
      if (!existsSync(rabbitmqScriptPath)) {
        console.error(`RabbitMQ script not found at: ${rabbitmqScriptPath}`);
        resolve(false);
        return;
      }

      console.log(`RabbitMQ script exists, attempting to start service`);

      // 计算工作目录路径
      const workingDir = app.isPackaged
        ? dirname(rabbitmqScriptPath) // 在打包后的环境中，使用脚本所在目录作为工作目录
        : join(app.getAppPath(), ".."); // 在开发环境中，使用项目根目录

      console.log(`RabbitMQ working directory: ${workingDir}`);

      // 启动RabbitMQ
      rabbitmqProcess = spawn(rabbitmqScriptPath, [], {
        windowsHide: false, // 显示命令提示符窗口以便查看输出
        stdio: "pipe", // 改为pipe以便捕获输出
        cwd: workingDir, // 设置工作目录
        shell: true, // 使用shell执行，确保批处理文件可以正确执行
      });

      if (!rabbitmqProcess || !rabbitmqProcess.pid) {
        console.error("Failed to start RabbitMQ: Invalid process");
        resolve(false);
        return;
      }

      console.log("RabbitMQ process started with PID:", rabbitmqProcess.pid);

      // 监听RabbitMQ输出，确认启动成功
      let startupTimeout: NodeJS.Timeout;
      let isStarted = false;

      const checkStartup = (data: Buffer) => {
        const output = data.toString();
        console.log("RabbitMQ output:", output);

        // 检查RabbitMQ是否启动成功的标志
        // 根据实际的RabbitMQ输出调整这些条件
        if (
          output.includes("completed with") ||
          output.includes("started") ||
          (output.includes("RabbitMQ") && output.includes("running")) ||
          output.includes("broker running") ||
          output.includes("Starting broker")
        ) {
          isStarted = true;
          clearTimeout(startupTimeout);
          console.log("RabbitMQ service started successfully");
          resolve(true);
        }

        // 检查错误信息
        if (
          output.includes("error") ||
          output.includes("failed") ||
          output.includes("could not start")
        ) {
          console.error("RabbitMQ startup error detected:", output);
          clearTimeout(startupTimeout);
          resolve(false);
        }
      };

      if (rabbitmqProcess.stdout) {
        rabbitmqProcess.stdout.on("data", checkStartup);
      }

      if (rabbitmqProcess.stderr) {
        rabbitmqProcess.stderr.on("data", (data) => {
          const errorOutput = data.toString();
          console.error("RabbitMQ error:", errorOutput);
          checkStartup(data); // 也检查错误输出
        });
      }

      // 监听进程事件
      rabbitmqProcess.on("error", (err) => {
        console.error("Failed to start RabbitMQ service:", err);
        rabbitmqProcess = null;

        if (!isStarted) {
          clearTimeout(startupTimeout);
          resolve(false);
        }
      });

      rabbitmqProcess.on("close", (code) => {
        console.log(`RabbitMQ service exited with code ${code}`);
        rabbitmqProcess = null;

        if (!isStarted && !isShuttingDown) {
          clearTimeout(startupTimeout);
          resolve(false);
        }
      });

      // 设置启动超时
      startupTimeout = setTimeout(() => {
        if (!isStarted) {
          console.error("RabbitMQ service startup timeout");
          resolve(false);
        }
      }, 60000); // 60秒超时，RabbitMQ可能需要更长时间
    } catch (error) {
      console.error("Error starting RabbitMQ service:", error);
      rabbitmqProcess = null;
      resolve(false);
    }
  });
}

// 终止RabbitMQ服务
async function terminateRabbitMQ(): Promise<void> {
  return new Promise((resolve) => {
    if (rabbitmqProcess === null) {
      console.log("RabbitMQ process is already null");
      resolve();
      return;
    }

    console.log("Terminating RabbitMQ service...");

    try {
      // 在Windows下，由于使用批处理文件启动，需要使用特殊方法终止
      if (process.platform === "win32" && rabbitmqProcess.pid) {
        // 使用taskkill终止进程树
        exec(`taskkill /F /T /PID ${rabbitmqProcess.pid}`, (err) => {
          if (err) {
            console.error("Error terminating RabbitMQ process:", err);
          } else {
            console.log("RabbitMQ service terminated successfully.");
          }
          rabbitmqProcess = null;
          resolve();
        });
      } else {
        rabbitmqProcess.kill();
        console.log("RabbitMQ service terminated successfully.");
        rabbitmqProcess = null;
        resolve();
      }
    } catch (error) {
      console.error("Error terminating RabbitMQ service:", error);
      rabbitmqProcess = null;
      resolve();
    }
  });
}

app.whenReady().then(async () => {
  console.log("App ready, starting services...");
  const BackendEnabled = import.meta.env.VITE_START_BACKEND;

  if (BackendEnabled === "true") {
    // 在启动前检查并清理可能的残留进程
    console.log("Checking for residual processes from previous runs...");

    try {
      // 清理所有可能的残留后端进程
      const existingBackendPids = await checkExistingBackendProcesses();
      if (existingBackendPids.length > 0) {
        console.log(
          `Found ${existingBackendPids.length} residual backend processes, cleaning up...`,
        );
        for (const pid of existingBackendPids) {
          await killProcess(pid);
        }
      }

      // 清理所有可能的残留RabbitMQ进程
      const existingRabbitMQPids = await checkExistingRabbitMQProcesses();
      if (existingRabbitMQPids.length > 0) {
        console.log(
          `Found ${existingRabbitMQPids.length} residual RabbitMQ processes, cleaning up...`,
        );
        for (const pid of existingRabbitMQPids) {
          await killProcess(pid);
        }
        // 给进程一些时间完全终止
        await new Promise((resolve) => setTimeout(resolve, 2000));
      }

      console.log("Residual process cleanup completed");
    } catch (error) {
      console.error("Error during residual process cleanup:", error);
    }

    // 首先启动RabbitMQ服务
    console.log("Starting RabbitMQ...");
    const rabbitmqStarted = await startRabbitMQ();

    if (!rabbitmqStarted) {
      console.error("Failed to start RabbitMQ, aborting startup");
      dialog.showErrorBox(
        "启动失败",
        "RabbitMQ服务启动失败，请检查配置并重试。",
      );
      app.quit();
      return;
    }

    // 等待一段时间让RabbitMQ完全初始化
    console.log("Waiting for RabbitMQ to fully initialize...");
    await new Promise((resolve) => setTimeout(resolve, 5000));

    // 然后启动后端服务
    console.log("Starting backend service...");
    const backendStarted = await startBackendService();

    if (!backendStarted) {
      console.error("Failed to start backend service, aborting startup");
      dialog.showErrorBox("启动失败", "后端服务启动失败，请检查配置并重试。");
      await terminateRabbitMQ();
      app.quit();
      return;
    }

    // 等待后端服务完全初始化
    console.log("Waiting for backend service to fully initialize...");
    await new Promise((resolve) => setTimeout(resolve, 3000));
  }
  // 最后创建启动屏幕
  console.log("All services started successfully, creating UI...");
  createMainWindow("/welcome");
});

app.on("window-all-closed", () => {
  // Splash window might be closed before main window, ensure 'win' refers to mainWindow
  if (BrowserWindow.getAllWindows().length === 0) {
    // Check if all windows are closed
    app.quit();
  }
});

// 在应用退出前关闭后端服务
app.on("before-quit", async (event) => {
  const BackendEnabled = import.meta.env.VITE_START_BACKEND;
  if (!isShuttingDown && BackendEnabled === "true") {
    event.preventDefault();
    isShuttingDown = true;

    console.log("Application is quitting, terminating all services...");

    // 并行终止服务，但设置超时防止卡死
    const terminateWithTimeout = async (
      terminateFunc: () => Promise<void>,
      name: string,
      timeout: number,
    ) => {
      return Promise.race([
        terminateFunc(),
        new Promise<void>((resolve) => {
          setTimeout(() => {
            console.warn(`${name} termination timeout after ${timeout}ms`);
            resolve();
          }, timeout);
        }),
      ]);
    };

    try {
      await Promise.all([
        terminateWithTimeout(
          async () => await terminateAllRabbitMQProcesses(),
          "RabbitMQ",
          5000,
        ),
        terminateWithTimeout(
          async () => await terminateAllBackendProcesses(),
          "Backend",
          5000,
        ),
      ]);
      console.log("All processes terminated successfully");
    } catch (error) {
      console.error("Error during service termination:", error);
    }

    // 最后一次检查是否还有残留进程
    try {
      const remainingBackendPids = await checkExistingBackendProcesses();
      const remainingRabbitMQPids = await checkExistingRabbitMQProcesses();

      const allRemainingPids = [
        ...remainingBackendPids,
        ...remainingRabbitMQPids,
      ];
      if (allRemainingPids.length > 0) {
        console.log(
          `Found ${allRemainingPids.length} remaining processes, attempting final cleanup...`,
        );
        await Promise.all(allRemainingPids.map((pid) => killProcess(pid)));
      }
    } catch (error) {
      console.error("Error during final process cleanup:", error);
    }

    // 确保所有服务都已终止后再退出
    setTimeout(() => {
      console.log("Clean exit completed, quitting application");
      app.exit(0);
    }, 1000);
  }
});

// 确保进程在退出时清理
process.on("exit", () => {
  const BackendEnabled = import.meta.env.VITE_START_BACKEND;
  if (BackendEnabled === "true") {
    console.log("Node process exiting, attempting final cleanup");

    // 在进程退出时只能同步操作
    if (process.platform === "win32") {
      const { execSync } = require("child_process");

      // 尝试终止后端进程
      if (backendPID) {
        try {
          execSync(`taskkill /pid ${backendPID} /T /F`, { stdio: "ignore" });
          console.log(`Terminated backend process ${backendPID} during exit`);
        } catch (e) {
          // 忽略错误
        }
      }

      // 尝试终止RabbitMQ进程
      if (rabbitmqProcess && rabbitmqProcess.pid) {
        try {
          execSync(`taskkill /pid ${rabbitmqProcess.pid} /T /F`, {
            stdio: "ignore",
          });
          console.log(
            `Terminated RabbitMQ process ${rabbitmqProcess.pid} during exit`,
          );
        } catch (e) {
          // 忽略错误
        }
      }

      // 额外检查所有可能残留的后端和RabbitMQ进程
      try {
        execSync("taskkill /F /IM runserver.exe /T 2>nul", { stdio: "ignore" });
        execSync("taskkill /F /IM erl.exe /T 2>nul", { stdio: "ignore" });
        execSync("taskkill /F /IM epmd.exe /T 2>nul", { stdio: "ignore" });
        execSync('taskkill /F /IM "rabbitmq-server.bat" /T 2>nul', {
          stdio: "ignore",
        });
        console.log("Terminated all remaining processes during exit");
      } catch (e) {
        // 忽略错误
      }
    } else {
      // 非Windows平台
      if (backendPID) {
        try {
          process.kill(backendPID, "SIGKILL");
          console.log(`Terminated backend process ${backendPID} during exit`);
        } catch (e) {
          // 忽略错误
        }
      }

      if (rabbitmqProcess && rabbitmqProcess.pid) {
        try {
          process.kill(rabbitmqProcess.pid, "SIGKILL");
          console.log(
            `Terminated RabbitMQ process ${rabbitmqProcess.pid} during exit`,
          );
        } catch (e) {
          // 忽略错误
        }
      }
    }
  }
});

// 处理未捕获的异常，确保在崩溃时也能清理进程
process.on("uncaughtException", async (error) => {
  console.error("Uncaught exception:", error);
  isShuttingDown = true;

  // 尝试快速清理
  const cleanup = async () => {
    console.log("Performing emergency cleanup due to uncaught exception");
    const promises = [];

    try {
      // 检查并终止所有后端进程
      const backendPids = await checkExistingBackendProcesses();
      for (const pid of backendPids) {
        promises.push(killProcess(pid));
      }

      // 检查并终止所有RabbitMQ进程
      const rabbitmqPids = await checkExistingRabbitMQProcesses();
      for (const pid of rabbitmqPids) {
        promises.push(killProcess(pid));
      }

      await Promise.race([
        Promise.all(promises),
        new Promise((resolve) => setTimeout(resolve, 3000)), // 3秒超时
      ]);
      console.log("Emergency cleanup completed");
    } catch (e) {
      console.error("Emergency cleanup error:", e);
    }
  };

  await cleanup();
  app.exit(1);
});

// 处理未处理的拒绝Promise，通常是异步错误
process.on("unhandledRejection", async (reason, promise) => {
  console.error("Unhandled Promise rejection:", reason);
  // 不退出应用，但记录错误
  // 如果是严重错误导致应用无法继续，将触发uncaughtException
});

app.on("second-instance", () => {
  // Focus on the main window if the user tried to open another
  if (win && !win.isDestroyed()) {
    if (win.isMinimized()) win.restore();
    win.focus();
  }
});

app.on("activate", () => {
  // On macOS it's common to re-create a window in the app when the
  // dock icon is clicked and there are no other windows open.
  const allWindows = BrowserWindow.getAllWindows();
  if (allWindows.length === 0) {
    createMainWindow("/welcome"); // Always start with welcome
  } else {
    // If windows exist, focus the main one
    if (win && !win.isDestroyed()) {
      if (win.isMinimized()) win.restore();
      win.focus();
    } else {
      // Fallback if recorded window is destroyed for some reason
      allWindows[0].focus();
    }
  }
});

// Listener to close splash and open main window
ipcMain.on(
  "APP_READY_TO_SHOW_MAIN_WINDOW",
  (
    event,
    args: {
      targetRoute?: string;
      openedFilePath?: string;
      singleFileMode?: boolean;
      clearCache?: boolean;
    } = {},
  ) => {
    createMainWindow(args.targetRoute); // Pass targetRoute to createMainWindow

    // If there's a file to open, send it to the main window
    if (args.openedFilePath) {
      console.log("Main process: Preparing to send file data:", {
        filePath: args.openedFilePath,
        targetRoute: args.targetRoute,
        singleFileMode: args.singleFileMode,
      });

      const sendFileData = () => {
        console.log("Main process: Sending file data events");

        if (args.targetRoute?.includes("/dataManagement/imandex")) {
          // For Excel files
          console.log("Main process: Sending excel-file-selected event");
          win?.webContents.send("excel-file-selected", args.openedFilePath);
        } else {
          // For other files
          console.log("Main process: Sending workspace-file-selected event");
          win?.webContents.send("workspace-file-selected", args.openedFilePath);
        }

        if (args.singleFileMode) {
          console.log("Main process: Sending set-single-file-mode event");
          win?.webContents.send("set-single-file-mode", args.openedFilePath);
        }
      };

      win?.webContents.once("did-finish-load", sendFileData);
      win?.webContents.once("dom-ready", sendFileData);
      setTimeout(sendFileData, 1000);
    }
  },
);

// 菜单栏 https://www.electronjs.org/zh/docs/latest/api/menu-item#%E8%8F%9C%E5%8D%95%E9%A1%B9
const appMenu = (fullscreenLabel: string) => {
  // 开发者工具菜单项（仅在开发模式下显示）
  const devMenuItems: MenuItemConstructorOptions[] = [];
  if (isDev) {
    devMenuItems.push(
      { label: "开发者工具", role: "toggleDevTools" },
      { label: "强制刷新", role: "forceReload" },
    );
  }

  const template: Array<MenuItemConstructorOptions | MenuItem> = [
    {
      label: "文件",
      submenu: [
        {
          label: "导入项目...",
          accelerator: "CmdOrCtrl+Shift+O",
          // 添加动效提示
          toolTip: "导入现有项目文件夹",
          click: async () => {
            if (win && !win.isDestroyed()) {
              win.focus();
              win.webContents.send("menu-triggered-import-project");
            } else {
              const dialogOptions: Electron.OpenDialogOptions = {
                properties: ["openDirectory"],
              };
              const directoryPathResult =
                await dialog.showOpenDialog(dialogOptions);

              if (
                !directoryPathResult.canceled &&
                directoryPathResult.filePaths.length > 0
              ) {
                const projectPath = directoryPathResult.filePaths[0];
                createMainWindow(
                  `/workspace/${encodeURIComponent(projectPath)}`,
                );
              }
            }
          },
        },
        {
          label: "打开文件...",
          accelerator: "CmdOrCtrl+O",
          toolTip: "打开单个文件进行编辑",
          click: async () => {
            if (win && !win.isDestroyed()) {
              win.focus();

              // 获取当前窗口URL，根据URL类型决定如何处理文件打开
              const currentURL = win.webContents.getURL();
              console.log("Current window URL:", currentURL);

              // 检查是否在数据导入导出页面
              const isDataImportPage = currentURL.includes(
                "/dataManagement/imandex",
              );

              // 如果当前页面是数据导入导出页面，直接发送到该页面处理
              if (isDataImportPage) {
                console.log(
                  "Sending menu-triggered-open-file to dataImandEx page",
                );
                win.webContents.send("menu-triggered-open-file");
              } else {
                // 其他页面使用标准文件选择对话框
                const dialogOptions: Electron.OpenDialogOptions = {
                  properties: ["openFile"],
                };
                const filePathResult =
                  await dialog.showOpenDialog(dialogOptions);

                if (
                  !filePathResult.canceled &&
                  filePathResult.filePaths.length > 0
                ) {
                  const filePath = filePathResult.filePaths[0];

                  // Check if it's an Excel file
                  const isExcelFile = /\.(xlsx|xls|csv)$/i.test(filePath);

                  if (isExcelFile) {
                    // For Excel files, navigate to dataImandEx page
                    createMainWindow(`/dataManagement/imandex`);
                    // Send the file path to be opened
                    const sendExcelFile = () => {
                      win?.webContents.send("excel-file-selected", filePath);
                      win?.webContents.send("set-single-file-mode", filePath);
                    };
                    win?.webContents.once("did-finish-load", sendExcelFile);
                    win?.webContents.once("dom-ready", sendExcelFile);
                    setTimeout(sendExcelFile, 1000);
                  } else {
                    // For other files, create a workspace with the file
                    const fileDir = filePath.substring(
                      0,
                      filePath.lastIndexOf("/") || filePath.lastIndexOf("\\"),
                    );
                    createMainWindow(
                      `/workspace/${encodeURIComponent(fileDir)}`,
                    );
                    // Use multiple event listeners to ensure the message is received
                    const sendWorkspaceFile = () => {
                      win?.webContents.send(
                        "workspace-file-selected",
                        filePath,
                      );
                      win?.webContents.send("set-single-file-mode", filePath);
                    };
                    win?.webContents.once("did-finish-load", sendWorkspaceFile);
                    win?.webContents.once("dom-ready", sendWorkspaceFile);
                    // Also send after a delay to ensure components are mounted
                    setTimeout(sendWorkspaceFile, 1000);
                  }
                }
              }
            } else {
              const dialogOptions: Electron.OpenDialogOptions = {
                properties: ["openFile"],
              };
              const filePathResult = await dialog.showOpenDialog(dialogOptions);

              if (
                !filePathResult.canceled &&
                filePathResult.filePaths.length > 0
              ) {
                const filePath = filePathResult.filePaths[0];

                // Check if it's an Excel file
                const isExcelFile = /\.(xlsx|xls|csv)$/i.test(filePath);

                if (isExcelFile) {
                  // For Excel files, navigate to dataImandEx page
                  createMainWindow(`/dataManagement/imandex`);
                  // Send the file path to be opened
                  const sendExcelFile = () => {
                    win?.webContents.send("excel-file-selected", filePath);
                    win?.webContents.send("set-single-file-mode", filePath);
                  };
                  win?.webContents.once("did-finish-load", sendExcelFile);
                  win?.webContents.once("dom-ready", sendExcelFile);
                  setTimeout(sendExcelFile, 1000);
                } else {
                  // For other files, create a workspace with the file
                  const fileDir = filePath.substring(
                    0,
                    filePath.lastIndexOf("/") || filePath.lastIndexOf("\\"),
                  );
                  createMainWindow(`/workspace/${encodeURIComponent(fileDir)}`);
                  // Use multiple event listeners to ensure the message is received
                  const sendWorkspaceFile = () => {
                    win?.webContents.send("workspace-file-selected", filePath);
                    win?.webContents.send("set-single-file-mode", filePath);
                  };
                  win?.webContents.once("did-finish-load", sendWorkspaceFile);
                  win?.webContents.once("dom-ready", sendWorkspaceFile);
                  // Also send after a delay to ensure components are mounted
                  setTimeout(sendWorkspaceFile, 1000);
                }
              }
            }
          },
        },
        { type: "separator" },
        {
          label: "退出",
          role: "quit",
          accelerator: "CmdOrCtrl+Q",
        },
      ],
    },
    {
      label: "编辑",
      submenu: [
        {
          label: "撤销",
          role: "undo",
          accelerator: "CmdOrCtrl+Z",
        },
        {
          label: "重做",
          role: "redo",
          accelerator: "CmdOrCtrl+Shift+Z",
        },
        { type: "separator" },
        {
          label: "剪切",
          role: "cut",
          accelerator: "CmdOrCtrl+X",
        },
        {
          label: "复制",
          role: "copy",
          accelerator: "CmdOrCtrl+C",
        },
        {
          label: "粘贴",
          role: "paste",
          accelerator: "CmdOrCtrl+V",
        },
        {
          label: "删除",
          role: "delete",
          accelerator: "Delete",
        },
        {
          label: "全选",
          role: "selectAll",
          accelerator: "CmdOrCtrl+A",
        },
      ],
    },
    {
      label: "显示",
      submenu: [
        {
          label: "放大",
          role: "zoomIn",
          accelerator: "CmdOrCtrl+Plus",
        },
        {
          label: "默认大小",
          role: "resetZoom",
          accelerator: "CmdOrCtrl+0",
        },
        {
          label: "缩小",
          role: "zoomOut",
          accelerator: "CmdOrCtrl+-",
        },
        { type: "separator" },
        {
          label: fullscreenLabel,
          role: "togglefullscreen",
          accelerator: "F11",
        },
      ],
    },
    // 将开发者工具菜单添加到这里（仅在开发模式下）
    ...(isDev
      ? [
          {
            label: "开发",
            submenu: devMenuItems,
          },
        ]
      : []),
    // 关于菜单放在最后
    {
      label: "关于",
      submenu: [
        {
          label: "关于应用",
          role: "about" as const,
          accelerator: "F1",
        },
      ],
    },
  ];

  return template;
};

// New window example arg: new windows url
ipcMain.handle("open-win", (_, arg) => {
  const childWindow = new BrowserWindow({
    webPreferences: {
      preload,
      nodeIntegration: false, // Then these should also be updated
      contextIsolation: true,
    },
  });

  if (process.env.VITE_DEV_SERVER_URL) {
    childWindow.loadURL(`${url}#${arg}`);
  } else {
    childWindow.loadFile(indexHtml, { hash: arg });
  }
});

// // 在文件末尾添加以下 IPC 事件监听器
// ipcMain.handle('select-directory', async () => {
//   const result = await dialog.showOpenDialog({
//     properties: ['openDirectory']
//   })
//   return result.filePaths[0]
// })

// ipcMain.handle('create-directory', (_, path: string) => {
//   if (!existsSync(path)) {
//     mkdirSync(path, { recursive: true })
//     return true
//   }
//   return false
// })

// ipcMain.handle('read-file', (_, filePath: string) => {
//   return readFileSync(filePath, 'utf-8')
// })

// ipcMain.handle('write-file', (_, filePath: string, content: string) => {
//   writeFileSync(filePath, content)
//   return true
// })

// ipcMain.handle('read-directory', (_, dirPath: string) => {
//   try {
//     return readdirSync(dirPath).map(file => {
//       const fullPath = join(dirPath, file)
//       const stats: Stats = statSync(fullPath)
//       return {
//         name: file,
//         path: fullPath,
//         isDirectory: stats.isDirectory(),
//         size: stats.size,
//         modified: stats.mtime
//       }
//     })
//   } catch (error) {
//     console.error('Error reading directory:', error)
//     return []
//   }
// })

// 在文件顶部导入所需模块
import fs from "fs";
import path from "path";
import { createRequire } from "node:module";

// Create require function for dynamic imports
const require = createRequire(import.meta.url);

// 在文件末尾（ipcMain.handle("open-win"...)之后）添加以下代码

// 选择目录对话框
ipcMain.handle("dialog:openDirectory", async () => {
  const result = await dialog.showOpenDialog({
    properties: ["openDirectory"],
  });
  return result.filePaths[0]; // 返回选择的第一个路径
});

// 选择文件对话框
ipcMain.handle("dialog:openFile", async () => {
  const result = await dialog.showOpenDialog({
    properties: ["openFile"],
    // You can add filters, e.g., for specific file types
    // filters: [
    //   { name: 'Text Files', extensions: ['txt', 'md'] },
    //   { name: 'All Files', extensions: ['*'] }
    // ]
  });
  if (result.canceled || result.filePaths.length === 0) {
    return null; // Or handle as preferred
  }
  return result.filePaths[0]; // 返回选择的第一个路径
});

// 读取目录内容
ipcMain.handle("fs:readDirectory", async (_, dirPath: string) => {
  const files = await fs.promises.readdir(dirPath, { withFileTypes: true });
  return files.map((dirent) => ({
    name: dirent.name,
    isDirectory: dirent.isDirectory(),
    path: path.join(dirPath, dirent.name),
  }));
});

// 创建新目录
ipcMain.handle("fs:createDirectory", async (_, targetPath: string) => {
  await fs.promises.mkdir(targetPath, { recursive: true });
  return { success: true };
});

// 创建新文件
ipcMain.handle("fs:createFile", async (_, filePath: string) => {
  await fs.promises.writeFile(filePath, "");
  return { success: true };
});

// 删除文件/目录
ipcMain.handle("fs:deletePath", async (_, targetPath: string) => {
  const stats = await fs.promises.stat(targetPath);
  if (stats.isDirectory()) {
    await fs.promises.rmdir(targetPath, { recursive: true });
  } else {
    await fs.promises.unlink(targetPath);
  }
  return { success: true };
});

// 检测文件类型
const getFileType = (
  filePath: string,
): { type: string; category: string; supported: boolean } => {
  const ext = path.extname(filePath).toLowerCase();

  // 文本文件
  const textExtensions = [
    ".txt",
    ".md",
    ".json",
    ".xml",
    ".html",
    ".css",
    ".js",
    ".ts",
    ".vue",
    ".py",
    ".java",
    ".cpp",
    ".c",
    ".h",
    ".sql",
    ".log",
    ".ini",
    ".cfg",
    ".conf",
    ".yaml",
    ".yml",
  ];

  // Office 文档
  const officeExtensions = [".doc", ".docx", ".xls", ".xlsx", ".ppt", ".pptx"];

  // PDF 文档
  const pdfExtensions = [".pdf"];

  // 图片文件
  const imageExtensions = [
    ".jpg",
    ".jpeg",
    ".png",
    ".gif",
    ".bmp",
    ".svg",
    ".ico",
  ];

  // 压缩文件
  const archiveExtensions = [".zip", ".rar", ".7z", ".tar", ".gz"];

  // 可执行文件
  const executableExtensions = [".exe", ".msi", ".dmg", ".app", ".deb", ".rpm"];

  // 音视频文件
  const mediaExtensions = [".mp3", ".mp4", ".avi", ".mov", ".wav", ".flac"];

  if (textExtensions.includes(ext)) {
    return { type: ext, category: "text", supported: true };
  } else if (officeExtensions.includes(ext)) {
    return { type: ext, category: "office", supported: true };
  } else if (pdfExtensions.includes(ext)) {
    return { type: ext, category: "pdf", supported: true };
  } else if (imageExtensions.includes(ext)) {
    return { type: ext, category: "image", supported: true };
  } else if (archiveExtensions.includes(ext)) {
    return { type: ext, category: "archive", supported: false };
  } else if (executableExtensions.includes(ext)) {
    return { type: ext, category: "executable", supported: false };
  } else if (mediaExtensions.includes(ext)) {
    return { type: ext, category: "media", supported: false };
  } else {
    return { type: ext, category: "unknown", supported: false };
  }
};

// 读取文件内容
ipcMain.handle("fs:readFile", async (_, filePath: string) => {
  try {
    const content = await fs.promises.readFile(filePath, "utf-8");
    return content;
  } catch (error) {
    console.error("Error reading file:", error);
    throw error;
  }
});

// 检测文件类型和读取内容
ipcMain.handle("fs:readFileWithType", async (_, filePath: string) => {
  try {
    const fileInfo = getFileType(filePath);

    if (!fileInfo.supported) {
      return {
        success: false,
        fileInfo,
        error: `不支持的文件类型: ${fileInfo.type}`,
        message: getUnsupportedMessage(fileInfo),
      };
    }

    let content = "";
    let imageData = null;

    if (fileInfo.category === "text") {
      // 直接读取文本文件
      content = await fs.promises.readFile(filePath, "utf-8");
    } else if (fileInfo.category === "office") {
      // Office 文档需要特殊处理
      if (fileInfo.type === ".docx") {
        content = await extractDocxText(filePath);
      } else if (fileInfo.type === ".doc") {
        content = "暂不支持 .doc 格式，请转换为 .docx 格式";
      } else {
        content = `不支持的Office 文档 (${fileInfo.type})，请使用Office工具进行编辑`;
      }
    } else if (fileInfo.category === "pdf") {
      content = "PDF 文档，暂不支持文本提取";
    } else if (fileInfo.category === "image") {
      // 读取图像文件为 base64
      const imageBuffer = await fs.promises.readFile(filePath);
      const base64Data = imageBuffer.toString("base64");
      const mimeType = getMimeType(fileInfo.type);
      imageData = `data:${mimeType};base64,${base64Data}`;
      content = ""; // 图像文件不需要文本内容
    }

    return {
      success: true,
      fileInfo,
      content,
      imageData,
    };
  } catch (error: any) {
    console.error("Error reading file with type:", error);
    return {
      success: false,
      fileInfo: getFileType(filePath),
      error: error?.message || "Unknown error",
      message: "读取文件时发生错误",
    };
  }
});

// 获取图像文件的 MIME 类型
const getMimeType = (extension: string): string => {
  const mimeTypes: Record<string, string> = {
    ".jpg": "image/jpeg",
    ".jpeg": "image/jpeg",
    ".png": "image/png",
    ".gif": "image/gif",
    ".bmp": "image/bmp",
    ".svg": "image/svg+xml",
    ".ico": "image/x-icon",
    ".webp": "image/webp",
  };
  return mimeTypes[extension.toLowerCase()] || "image/jpeg";
};

// 获取不支持文件类型的提示信息
const getUnsupportedMessage = (fileInfo: {
  type: string;
  category: string;
}) => {
  switch (fileInfo.category) {
    case "image":
      return "图片文件不支持文本编辑，请使用图片查看器打开";
    case "archive":
      return "压缩文件不支持直接编辑，请先解压缩";
    case "executable":
      return "可执行文件不支持编辑";
    case "media":
      return "音视频文件不支持文本编辑，请使用媒体播放器打开";
    default:
      return "不支持的文件类型，无法在文本编辑器中打开";
  }
};

// 提取 DOCX 文档的文本内容
const extractDocxText = async (filePath: string): Promise<string> => {
  try {
    // Try to use mammoth if available
    try {
      const mammoth = require("mammoth");
      const result = await mammoth.extractRawText({ path: filePath });
      return result.value || "无法提取文档内容";
    } catch (mammothError) {
      console.log("Mammoth not available, using fallback method");

      // Fallback: Try to read as zip and extract document.xml
      try {
        const AdmZip = require("adm-zip");
        const zip = new AdmZip(filePath);
        const documentXml = zip.readAsText("word/document.xml");

        if (documentXml) {
          // Simple XML text extraction (removes tags)
          const textContent = documentXml
            .replace(/<[^>]*>/g, " ") // Remove XML tags
            .replace(/\s+/g, " ") // Normalize whitespace
            .trim();

          return textContent || "文档内容为空";
        }
      } catch (zipError: any) {
        console.log(
          "ZIP extraction failed:",
          zipError?.message || "Unknown error",
        );
      }

      return `Word 文档预览\n\n文件路径: ${filePath}\n\n注意：无法提取文档内容，请使用 Microsoft Word 或其他兼容软件打开此文件。\n\n要完整支持 Word 文档，请安装 mammoth 库：npm install mammoth`;
    }
  } catch (error: any) {
    console.error("Error extracting DOCX text:", error);
    return `Word 文档预览\n\n文件路径: ${filePath}\n\n错误：无法读取文档内容 - ${error?.message || "Unknown error"}`;
  }
};

// 读取文件内容为Buffer (用于Excel等二进制文件)
ipcMain.handle("fs:readFileBuffer", async (_, filePath: string) => {
  try {
    const buffer = await fs.promises.readFile(filePath);
    return buffer;
  } catch (error) {
    console.error("Error reading file buffer:", error);
    throw error;
  }
});

// 写入文件内容
ipcMain.handle("fs:writeFile", async (_, filePath: string, content: string) => {
  try {
    await fs.promises.writeFile(filePath, content, "utf-8");
    return { success: true };
  } catch (error) {
    console.error("Error writing file:", error);
    throw error;
  }
});

// 写入文件内容 (Buffer)
ipcMain.handle(
  "fs:writeFileBuffer",
  async (_, filePath: string, buffer: Buffer) => {
    try {
      await fs.promises.writeFile(filePath, buffer);
      return { success: true };
    } catch (error) {
      console.error("Error writing file buffer:", error);
      throw error;
    }
  },
);

// Handle workspace file selection communication
ipcMain.on("workspace-file-selected", (event, filePath: string) => {
  // Forward the event to all renderer processes (in case there are multiple windows)
  if (win && !win.isDestroyed()) {
    win.webContents.send("workspace-file-selected", filePath);
  }
});

// Handle Excel file selection from workspace
ipcMain.on("excel-file-selected", (event, filePath: string) => {
  // Forward the event to all renderer processes
  if (win && !win.isDestroyed()) {
    win.webContents.send("excel-file-selected", filePath);
  }
});

// Handle app quit request
ipcMain.on("app-quit", () => {
  app.quit();
});

// Window control IPC handlers for main window
ipcMain.on("minimize-window", () => {
  if (win && !win.isDestroyed()) {
    win.minimize();
  }
});

ipcMain.on("maximize-window", () => {
  if (win && !win.isDestroyed()) {
    if (win.isMaximized()) {
      win.unmaximize();
    } else {
      win.maximize();
    }
  }
});

ipcMain.on("close-window", () => {
  if (win && !win.isDestroyed()) {
    win.close();
  }
});

// Window control IPC handlers for current window (child windows)
ipcMain.on("minimize-current-window", (event) => {
  const currentWindow = BrowserWindow.fromWebContents(event.sender);
  if (currentWindow && !currentWindow.isDestroyed()) {
    currentWindow.minimize();
  }
});

ipcMain.on("maximize-current-window", (event) => {
  const currentWindow = BrowserWindow.fromWebContents(event.sender);
  if (currentWindow && !currentWindow.isDestroyed()) {
    if (currentWindow.isMaximized()) {
      currentWindow.unmaximize();
    } else {
      currentWindow.maximize();
    }
  }
});

ipcMain.on("close-current-window", (event) => {
  const currentWindow = BrowserWindow.fromWebContents(event.sender);
  if (currentWindow && !currentWindow.isDestroyed()) {
    currentWindow.close();
  }
});

// Application menu handling
ipcMain.on("show-file-menu", (event, position) => {
  if (!win || win.isDestroyed()) return;

  const fileMenu = Menu.buildFromTemplate([
    {
      label: "导入项目...",
      click: () => {
        win?.webContents.send("menu-triggered-import-project");
      },
    },
    {
      label: "打开文件...",
      click: () => {
        win?.webContents.send("menu-triggered-open-file");
      },
    },
    { type: "separator" },
    {
      label: "退出",
      click: () => app.quit(),
    },
  ]);

  // Fix: Remove position parameter and just use window
  fileMenu.popup({ window: win });
});

ipcMain.on("show-edit-menu", (event, position) => {
  if (!win || win.isDestroyed()) return;

  const editMenu = Menu.buildFromTemplate([
    { label: "撤销", role: "undo" },
    { label: "重做", role: "redo" },
    { type: "separator" },
    { label: "剪切", role: "cut" },
    { label: "复制", role: "copy" },
    { label: "粘贴", role: "paste" },
    { type: "separator" },
    { label: "全选", role: "selectAll" },
  ]);

  // Fix: Remove position parameter and just use window
  editMenu.popup({ window: win });
});

ipcMain.on("show-view-menu", (event, position) => {
  if (!win || win.isDestroyed()) return;

  const viewMenu = Menu.buildFromTemplate([
    { label: "放大", role: "zoomIn" },
    { label: "默认大小", role: "resetZoom" },
    { label: "缩小", role: "zoomOut" },
    { type: "separator" },
    {
      label: win?.isFullScreen() ? "退出全屏" : "进入全屏",
      role: "togglefullscreen",
    },
  ]);

  // Fix: Remove position parameter and just use window
  viewMenu.popup({ window: win });
});

ipcMain.on("show-dev-menu", (event, position) => {
  if (!win || win.isDestroyed() || !isDev) return;

  const devMenu = Menu.buildFromTemplate([
    { label: "开发者工具", role: "toggleDevTools" },
    { label: "强制刷新", role: "forceReload" },
  ]);

  // Fix: Remove position parameter and just use window
  devMenu.popup({ window: win });
});

ipcMain.on("show-about-menu", (event, position) => {
  if (!win || win.isDestroyed()) return;

  const aboutMenu = Menu.buildFromTemplate([
    {
      label: "关于应用",
      click: () => {
        dialog.showMessageBox(win!, {
          title: "关于应用",
          message: "ML Desktop",
          detail: "版本 1.0.0\n一个机器学习数据处理和建模的桌面应用。",
          buttons: ["确定"],
          type: "info",
        });
      },
    },
  ]);

  // Fix: Remove position parameter and just use window
  aboutMenu.popup({ window: win });
});

// Handle showing open dialog
ipcMain.handle("dialog:showOpenDialog", async (event, options) => {
  if (!win) {
    return { canceled: true, filePaths: [] };
  }
  return await dialog.showOpenDialog(win, options);
});
