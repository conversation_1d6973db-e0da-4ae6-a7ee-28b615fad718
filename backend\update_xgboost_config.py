#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
动态更新PyInstaller配置中的XGBoost路径
"""

import os
import sys
import xgboost
import re
from pathlib import Path

def find_xgboost_dll():
    """查找XGBoost DLL文件"""
    xgb_path = Path(xgboost.__file__).parent
    dll_path = xgb_path / "lib" / "xgboost.dll"
    
    if dll_path.exists():
        return str(dll_path)
    else:
        # 搜索其他可能的DLL文件
        for root, dirs, files in os.walk(xgb_path):
            for file in files:
                if file.endswith('.dll') and 'xgboost' in file.lower():
                    return os.path.join(root, file)
    
    return None

def update_spec_file():
    """更新runapp.spec文件中的XGBoost路径"""
    spec_file = "runapp.spec"
    
    if not os.path.exists(spec_file):
        print(f"错误: 找不到 {spec_file} 文件")
        return False
    
    # 读取spec文件
    with open(spec_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 查找XGBoost DLL
    dll_path = find_xgboost_dll()
    if not dll_path:
        print("错误: 找不到XGBoost DLL文件")
        return False
    
    print(f"找到XGBoost DLL: {dll_path}")
    
    # 在spec文件中，路径字符串不需要额外转义，直接使用原始路径
    # 但需要确保路径使用正斜杠或双反斜杠
    spec_path = dll_path.replace('\\', '/')  # 使用正斜杠，更通用
    print(f"Spec文件中的路径: {spec_path}")
    
    # 更新binaries配置
    # 查找现有的binaries配置
    binaries_pattern = r'binaries=\[([^\]]*)\],'
    
    if re.search(binaries_pattern, content):
        # 如果已经有binaries配置，添加XGBoost
        new_binaries = f"binaries=[\n                 ('{spec_path}', 'xgboost/lib'),\n             ],"
        content = re.sub(binaries_pattern, new_binaries, content)
    else:
        # 如果没有binaries配置，添加新的
        new_binaries = f"             binaries=[\n                 ('{spec_path}', 'xgboost/lib'),\n             ],"
        # 在datas=[]之前插入
        content = content.replace("datas=[],", f"{new_binaries}\n             datas=[],")
    
    # 添加XGBoost相关的hiddenimports
    hiddenimports_pattern = r'hiddenimports=\[([^\]]*)\],'
    
    xgboost_imports = [
        "'xgboost'",
        "'xgboost.core'",
        "'xgboost.sklearn'",
        "'xgboost.training'",
        "'xgboost.callback'",
    ]
    
    if re.search(hiddenimports_pattern, content):
        # 检查是否已经包含XGBoost导入
        if "'xgboost'" not in content:
            # 在hiddenimports列表末尾添加XGBoost导入
            content = re.sub(
                r'(\s+),',
                r'\1,\n                ' + ',\n                '.join(xgboost_imports) + ',',
                content,
                count=1
            )
    else:
        # 如果没有hiddenimports配置，添加新的
        new_hiddenimports = f"             hiddenimports=[\n                " + ',\n                '.join(xgboost_imports) + ",\n            ],"
        # 在datas=[]之前插入
        content = content.replace("datas=[],", f"{new_hiddenimports}\n             datas=[],")
    
    # 写回文件
    with open(spec_file, 'w', encoding='utf-8') as f:
        f.write(content)
    
    print(f"✓ 已更新 {spec_file} 文件")
    return True

if __name__ == '__main__':
    success = update_spec_file()
    if success:
        print("XGBoost配置更新成功！")
    else:
        print("XGBoost配置更新失败！")
        sys.exit(1) 