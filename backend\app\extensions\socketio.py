from flask_socketio import <PERSON><PERSON><PERSON>, disconnect, emit
from flask import request, current_app
import logging
from typing import cast
from app.types.flaskapp import FlaskWithExecutor

logger = logging.getLogger("socketio")

# SocketIO 配置
socketio = SocketIO(
    cors_allowed_origins="*", 
    max_http_buffer_size=10 * 1024 * 1024,
    async_mode="threading",
    logger=True
)

# WebSocket 事件处理
@socketio.on('connect')
def handle_connect(sid=None):
    request_sid = sid or request.args.get('sid')  # 从请求参数中获取sid
    try:
        cast(FlaskWithExecutor, current_app).connected_sids[request_sid] = {}
        logger.info(f'User connected: {request_sid}')
    except Exception as err:
        logger.error(f'Connection Error: {err}')
        disconnect(request_sid)

@socketio.on('disconnect')
def handle_disconnect(sid):
    request_sid = sid or request.args.get('sid')  # 从请求参数中获取sid
    try:
        if request_sid in cast(FlaskWithExecutor, current_app).connected_sids:
            logger.info(f'User disconnected: {request_sid}')
            del cast(FlaskWithExecutor, current_app).connected_sids[request_sid]
    except Exception as err:
        logger.error(f'Disconnect Error: {err}')

@socketio.on('test')
def handle_test(data):
    logger.info(f'Test: {data}')
    emit('test_response', {'data': 'Hello, World!'})

# 添加错误处理
@socketio.on_error()
def error_handler(e):
    import traceback
    error_msg = f'SocketIO error: {e}'
    stack_trace = traceback.format_exc()
    
    # 记录详细错误信息
    logger.error(error_msg)
    logger.error(f'Error type: {type(e).__name__}')
    logger.error(f'Stack trace: {stack_trace}')
    
    # 如果是函数签名错误，记录特殊信息
    if "takes 0 positional arguments but 1 was given" in str(e):
        logger.error("检测到 SocketIO 事件处理函数签名错误，请检查函数定义")