<template>
  <el-card class="model-info-card model-card">
    <template #header>
      <div class="card-header">
        <span>模型基本信息</span>
        <el-tag
          v-if="modelResult?.status"
          :type="getStatusTagType(modelResult.status)"
        >
          {{ getStatusText(modelResult.status) }}
        </el-tag>
      </div>
    </template>
    <el-descriptions :column="columnCount" border>
      <!-- 基本信息 -->
      <el-descriptions-item label="模型类型">
        {{ getModelDisplayName() }}
      </el-descriptions-item>

      <el-descriptions-item
        label="算法"
        v-if="modelResult?.alg || modelResult?.algorithm"
      >
        {{ modelResult?.alg || modelResult?.algorithm }}
      </el-descriptions-item>

      <el-descriptions-item label="评估方法" v-if="showEvaluationMethod">
        {{
          modelResult?.cv_fold ? `${modelResult.cv_fold}折交叉验证` : "普通验证"
        }}
      </el-descriptions-item>

      <!-- 任务信息 -->
      <el-descriptions-item label="任务ID" v-if="modelResult?.taskUid">
        {{ modelResult.taskUid }}
      </el-descriptions-item>

      <el-descriptions-item label="创建时间" v-if="modelResult?.timestamp">
        {{ formatTimestamp(modelResult.timestamp) }}
      </el-descriptions-item>

      <!-- 自定义字段 -->
      <template v-for="field in customFields" :key="field.key">
        <el-descriptions-item v-if="hasValue(field.key)" :label="field.label">
          {{
            field.formatter
              ? field.formatter(modelResult?.[field.key])
              : modelResult?.[field.key]
          }}
        </el-descriptions-item>
      </template>
    </el-descriptions>
  </el-card>
</template>

<script setup lang="ts">
import { computed } from "vue";

interface CustomField {
  key: string;
  label: string;
  formatter?: (value: any) => string;
}

interface Props {
  modelResult: any;
  columnCount?: number;
  showEvaluationMethod?: boolean;
  customFields?: CustomField[];
}

const props = withDefaults(defineProps<Props>(), {
  columnCount: 3,
  showEvaluationMethod: true,
  customFields: () => [],
});

// 模型类型映射
const modelTypeMap: Record<string, string> = {
  DecisionTreeRegressor: "决策树",
  RandomForestRegressor: "随机森林",
  XGBoost: "XGBoost",
  GradientBoostingRegressor: "梯度提升回归",
  SVR: "支持向量机",
  MLPRegressor: "人工神经网络",
  LinearRegression: "线性回归",
  Ridge: "岭回归",
  Lasso: "Lasso回归",
  ElasticNet: "弹性网络回归",
};

// 状态映射
const statusMap: Record<string, string> = {
  completed: "已完成",
  running: "运行中",
  failed: "失败",
  pending: "等待中",
  success: "成功",
};

// 状态标签类型映射
const statusTagTypeMap: Record<string, string> = {
  completed: "success",
  success: "success",
  running: "warning",
  failed: "danger",
  pending: "info",
};

const getModelDisplayName = () => {
  const modelType = props.modelResult?.modelType || props.modelResult?.alg;
  return modelTypeMap[modelType] || modelType || "未知模型";
};

const getStatusText = (status: string) => {
  return statusMap[status] || status || "未知状态";
};

const getStatusTagType = (status: string) => {
  return statusTagTypeMap[status] || "info";
};

const formatTimestamp = (timestamp: string | number) => {
  try {
    const date = new Date(timestamp);
    return date.toLocaleString("zh-CN", {
      year: "numeric",
      month: "2-digit",
      day: "2-digit",
      hour: "2-digit",
      minute: "2-digit",
      second: "2-digit",
    });
  } catch (error) {
    return timestamp;
  }
};

const formatDuration = (duration: number) => {
  if (!duration) return "-";

  if (duration < 60) {
    return `${duration.toFixed(2)}秒`;
  } else if (duration < 3600) {
    return `${(duration / 60).toFixed(2)}分钟`;
  } else {
    return `${(duration / 3600).toFixed(2)}小时`;
  }
};

const formatDatasetSize = (size: number | string) => {
  if (typeof size === "number") {
    return `${size.toLocaleString()} 条记录`;
  }
  return size;
};

// 判断自定义字段是否存在有效值
const hasValue = (key: string) => {
  const val = props.modelResult?.[key];
  return val !== undefined && val !== null && val !== "";
};
</script>

<style lang="scss" scoped>
.model-info-card {
  margin-bottom: 16px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

:deep(.el-descriptions) {
  .el-descriptions__body {
    .el-descriptions__table {
      border-radius: 8px;
      overflow: hidden;

      .el-descriptions__cell {
        padding: 12px 16px;

        &.el-descriptions__label {
          background: #fafafa;
          font-weight: 600;
          color: #606266;
        }

        &.el-descriptions__content {
          background: white;
          color: #303133;
          font-weight: 500;
        }
      }
    }
  }
}

:deep(.el-tag) {
  border-radius: 6px;
  font-weight: 500;
  padding: 4px 8px;
}
</style>
