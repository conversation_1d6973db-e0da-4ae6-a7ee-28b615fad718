<template>
  <el-tooltip content="数据预处理" placement="bottom">
    <el-button
      circle
      :disabled="props.disabled"
      class="menu-button"
      @click="openPreprocessDialog"
    >
      <PreprocessIcon class="menu-icon normal" />
      <PreprocessSelectedIcon class="menu-icon hover" />
    </el-button>
  </el-tooltip>
</template>

<script lang="ts" setup>
import PreprocessIcon from "@/assets/svg/preprocess.svg?component";
import PreprocessSelectedIcon from "@/assets/svg/preprocess_selected.svg?component";

const props = defineProps<{
  disabled?: boolean;
}>();

const emit = defineEmits<{
  (e: "openDialog"): void;
}>();

function openPreprocessDialog() {
  emit("openDialog");
}
</script>

<style scoped>
.menu-button {
  width: 36px;
  height: 36px;
  border-radius: 2px;
  background: #FFFFFF;
  box-shadow: 0px 2px 8px 0px rgba(0, 0, 0, 0.08);

  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;

  &:hover {

    .menu-icon.normal {
      opacity: 0;
    }

    .menu-icon.hover {
      opacity: 1;
    }
  }

  .menu-icon {
    width: 22px;
    height: 22px;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    transition: opacity 0.2s ease;
  }

  .normal {
    opacity: 1;
  }

  .hover,
  .selected {
    opacity: 0;
  }

}
</style>