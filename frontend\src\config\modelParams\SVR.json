{"name": "SVR", "displayName": "支持向量回归", "description": "通过寻找最优分离划分超平面来进行回归预测", "category": "ml", "type": "regression", "defaultParams": {"C": {"value": 1.0, "type": "number", "optimizeParam": true, "description": "错误项的惩罚参数，控制模型复杂度", "displayName": "正则化参数", "min": 0.1, "max": 1, "step": 0.1}, "kernel": {"value": "rbf", "type": "select", "description": "算法中使用的核函数类型", "displayName": "核函数", "options": [{"label": "线性核", "value": "linear"}, {"label": "RBF核", "value": "rbf"}, {"label": "多项式核", "value": "poly"}, {"label": "Sigmoid核", "value": "sigmoid"}]}, "gamma": {"value": "scale", "type": "select", "description": "RBF、poly和sigmoid核的核系数", "displayName": "Gamma参数", "options": [{"label": "自动", "value": "auto"}, {"label": "缩放", "value": "scale"}]}, "degree": {"value": 3, "type": "number", "optimizeParam": true, "description": "多项式核函数的度数", "displayName": "多项式度数", "min": 2, "max": 5, "step": 1}, "coef0": {"value": 0.0, "type": "number", "optimizeParam": true, "description": "核函数中的独立项", "displayName": "核函数常数项", "min": 0.0, "max": 0.5, "step": 0.1}}, "evaluation": {"splitDataset": {"value": false, "description": "是否划分训练/测试集"}, "trainRatio": {"value": 70, "min": 50, "max": 90, "step": 5, "description": "训练集比例(%)"}, "randomState": {"value": 42, "min": 0, "max": 1000, "description": "随机种子"}, "useModelValidation": {"value": false, "description": "是否使用交叉验证"}, "validationType": {"value": "k-fold", "options": ["k-fold", "leave-one-out"], "description": "验证方法"}, "kFolds": {"value": 5, "min": 2, "max": 10, "description": "k折交叉验证的k值"}}, "tips": ["SVM在高维数据上表现优异，适合特征数量较多的情况", "RBF核适用于非线性问题，线性核适用于线性可分问题", "C参数控制过拟合：值越大越容易过拟合，值越小越容易欠拟合", "建议在使用前对数据进行标准化处理"], "introduction": {"detailedDescription": "支持向量机通过寻找能够最大化分类间隔的超平面来进行分类。对于非线性问题，SVM使用核技巧将数据映射到高维空间。支持向量是距离分离超平面最近的数据点，它们决定了分离超平面的位置。", "usageTips": ["适用于高维数据和小样本问题", "对特征缩放敏感，建议进行标准化", "核函数的选择对性能影响很大", "训练时间随样本数量增长较快"], "scenarios": "适用于高维数据和小样本问题，在文本分类和图像识别中表现优异。", "mainParams": [{"name": "C", "description": "正则化参数，控制对误分类的惩罚"}, {"name": "kernel", "description": "核函数类型（linear, rbf, poly等）"}, {"name": "gamma", "description": "RBF核的参数，控制单个样本的影响范围"}]}}