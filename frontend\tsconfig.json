{"compilerOptions": {"target": "ESNext", "module": "ESNext", "moduleResolution": "bundler", "strict": true, "jsx": "preserve", "importHelpers": true, "experimentalDecorators": true, "strictFunctionTypes": false, "skipLibCheck": true, "esModuleInterop": true, "isolatedModules": true, "allowSyntheticDefaultImports": true, "forceConsistentCasingInFileNames": true, "sourceMap": true, "baseUrl": ".", "allowJs": true, "resolveJsonModule": true, "lib": ["ESNext", "DOM"], "paths": {"@/*": ["src/*"], "@build/*": ["build/*"]}, "types": ["node", "vite/client", "element-plus/global", "@pureadmin/table/volar", "unplugin-icons/types/vue", "@pureadmin/descriptions/volar"]}, "include": ["mock/*.ts", "build/*.ts", "src/**/*.ts", "src/**/*.tsx", "src/**/*.vue", "types/*.d.ts", "vite.config.ts", "electron/**/*.ts"], "exclude": ["dist", "**/*.js", "node_modules"], "references": [{"path": "./tsconfig.node.json"}]}