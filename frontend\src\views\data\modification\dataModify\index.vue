<template>
  <div>
    <el-button type="primary" @click="selectDirectory">选择目录</el-button>
    <!-- <el-button type="success" @click="createFolder" :disabled="!currentPath"
      >新建文件夹</el-button
    > -->

    <el-tree
      v-if="treeData.length > 0"
      style="width: 100%; max-width: 600px; margin-top: 16px"
      :props="defaultProps"
      :load="loadNode"
      lazy
      highlight-current
      @node-click="handleNodeClick"
    >
      <template #default="{ node, data }">
        <span class="custom-tree-node">
          <span>{{ node.label }}</span>
          <span v-if="data.isDirectory">
            <a @click="append(data)" style="color: green"> 新建 </a>
          </span>
        </span>
      </template>
    </el-tree>
  </div>
</template>

<script setup lang="ts">
import { ref } from "vue";
import { ElMessageBox, ElMessage } from "element-plus";
import type Node from "element-plus/es/components/tree/src/model/node";

interface TreeNode {
  label: string;
  path: string;
  isDirectory: boolean;
  isLeaf?: boolean;
  children?: TreeNode[];
}

const defaultProps = {
  children: "children",
  label: "label",
  isLeaf: "isLeaf"
};

const treeData = ref<TreeNode[]>([]);
const currentPath = ref("");

const append = async (data: TreeNode) => {
  try {
    const { value, action } = await ElMessageBox.prompt(
      "请输入新建文件夹名称",
      "新建文件夹",
      {
        confirmButtonText: "确认",
        cancelButtonText: "取消",
        inputPattern: /^[^\\/:*?"<>|]+$/, // 禁止非法字符
        inputErrorMessage: '文件夹名不能包含 \\ / : * ? " < > |'
      }
    );

    if (action === "confirm" && value.trim()) {
      const newFolderPath = `${data.path}\\${value.trim()}`;
      console.log("我看看新文件夹", newFolderPath);
      await window.ipcRenderer.invoke("fs:createDirectory", newFolderPath);
      ElMessage.success("创建成功");

      // 如果该目录已经加载过，则手动刷新节点
      if (data.children) {
        const fileList = await window.ipcRenderer.invoke(
          "fs:readDirectory",
          data.path
        );
        data.children = fileList.map((file: any) => ({
          label: file.name,
          path: `${data.path}\\${file.name}`,
          isDirectory: file.isDirectory,
          isLeaf: !file.isDirectory
        }));
      }

      // 刷新整个树（可选，防止懒加载节点不更新）
      await reloadCurrentPath();
    }
  } catch (err) {
    if (err !== "cancel") {
      console.error("新建文件夹失败:", err);
      ElMessage.error("创建失败");
    }
  }
};

const remove = () => {
  console.log("都给我去死");
  // const parent = node.parent
  // const children: Tree[] = parent.data.children || parent.data
  // const index = children.findIndex((d) => d.id === data.id)
  // children.splice(index, 1)
  // dataSource.value = [...dataSource.value]
};

// 选择目录
const selectDirectory = async () => {
  const path = await window.ipcRenderer.invoke("dialog:openDirectory");
  if (path) {
    currentPath.value = path;
    await reloadCurrentPath();
  }
};

// 新建文件夹
const createFolder = async () => {
  try {
    const { value, action } = await ElMessageBox.prompt(
      "请输入新建文件夹名称",
      "新建文件夹",
      {
        confirmButtonText: "确认",
        cancelButtonText: "取消",
        inputPattern: /^[^\\/:*?"<>|]+$/, // 禁止非法字符
        inputErrorMessage: '文件夹名不能包含 \\ / : * ? " < > |'
      }
    );

    if (action === "confirm" && value.trim()) {
      const newFolderPath = `${currentPath.value}\\${value.trim()}`;
      await window.ipcRenderer.invoke("fs:createDirectory", newFolderPath);
      ElMessage.success("创建成功");
      await reloadCurrentPath();
    }
  } catch (err) {
    if (err !== "cancel") {
      console.error("新建文件夹失败:", err);
      ElMessage.error("创建失败");
    }
  }
};

// 刷新当前路径下的文件
const reloadCurrentPath = async () => {
  const fileList = await window.ipcRenderer.invoke(
    "fs:readDirectory",
    currentPath.value
  );
  treeData.value = fileList.map((file: any) => ({
    label: file.name,
    path: `${currentPath.value}\\${file.name}`,
    isDirectory: file.isDirectory,
    isLeaf: !file.isDirectory
  }));
};

// 懒加载子节点
const loadNode = async (node: Node, resolve: (data: TreeNode[]) => void) => {
  if (node.level === 0) {
    return resolve(treeData.value);
  }

  const parent = node.data as TreeNode;
  if (!parent.isDirectory) return resolve([]);

  try {
    const fileList = await window.ipcRenderer.invoke(
      "fs:readDirectory",
      parent.path
    );
    const children: TreeNode[] = fileList.map((file: any) => ({
      label: file.name,
      path: `${parent.path}\\${file.name}`,
      isDirectory: file.isDirectory,
      isLeaf: !file.isDirectory
    }));
    resolve(children);
  } catch (err) {
    console.error("加载子目录失败:", err);
    resolve([]);
  }
};

// 点击节点
const handleNodeClick = (data: TreeNode) => {
  console.log("点击节点:", data);
};
</script>

<style lang="css" scoped>
.custom-tree-node {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 14px;
  padding-right: 8px;
}
</style>
