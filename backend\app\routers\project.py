from flask import Blueprint, request, make_response, current_app, jsonify, send_file, send_from_directory
import traceback, uuid, numpy as np, pandas as pd, os, json, datetime, zipfile, shutil
from app.utils.file import allowed_file
from werkzeug.utils import secure_filename
project_bp = Blueprint("project", __name__)

def create_project_structure(project_id):
    """创建项目目录结构"""
    project_dir = current_app.projects_folder.joinpath(project_id)
    project_dir.mkdir(parents=True, exist_ok=True)
    project_dir.joinpath('datasets').mkdir(parents=True, exist_ok=True)
    project_dir.joinpath('models').mkdir(parents=True, exist_ok=True)
    project_dir.joinpath('outputs').mkdir(parents=True, exist_ok=True)
    project_dir.joinpath('logs').mkdir(parents=True, exist_ok=True)
    return project_dir

@project_bp.route('/create', methods=['POST'])
def create_project():
    """创建新项目"""
    try:
        data = request.get_json()
        project_name = data.get('name', '').strip()
        project_desc = data.get('description', '').strip()
        
        if not project_name:
            return jsonify({'error': '项目名称不能为空'}), 400
            
        project_id = str(uuid.uuid4())
        project_dir = create_project_structure(project_id)
        
        # 创建项目配置文件
        config = {
            'id': project_id,
            'name': project_name,
            'description': project_desc,
            'created_at': datetime.now().isoformat(),
            'updated_at': datetime.now().isoformat()
        }
        
        with open(project_dir.joinpath('config.json'), 'w', encoding='utf-8') as f:
            json.dump(config, f, ensure_ascii=False, indent=2)
            
        current_app.logger.info(f'创建项目成功: {project_name} (ID: {project_id})')
        return jsonify(config)
        
    except Exception as e:
        error_msg = f'创建项目失败: {str(e)}'
        current_app.logger.exception(error_msg)
        return jsonify({'error': error_msg}), 500

@project_bp.route('/list', methods=['GET'])
def list_projects():
    """获取项目列表"""
    try:
        projects = []
        for project_id in os.listdir(current_app.projects_folder):
            config_path = current_app.projects_folder.joinpath(project_id, 'config.json')
            if config_path.exists():
                with open(config_path, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                projects.append(config)
        
        return jsonify(projects)
    except Exception as e:
        error_msg = f'获取项目列表失败: {str(e)}'
        current_app.logger.exception(error_msg)
        return jsonify({'error': error_msg}), 500

@project_bp.route('/<project_id>/upload/<path:folder>', methods=['POST'])
def upload_project_file(project_id, folder):
    """上传项目文件到指定文件夹（datasets/models/outputs）"""
    try:
        if folder not in ['datasets', 'models', 'outputs']:
            return jsonify({'error': '无效的目标文件夹'}), 400
            
        if 'file' not in request.files:
            return jsonify({'error': '没有文件部分'}), 400
            
        file = request.files['file']
        if file.filename == '':
            return jsonify({'error': '没有选择文件'}), 400
            
        if file and allowed_file(file.filename):
            filename = secure_filename(file.filename)
            folder_path = current_app.projects_folder.joinpath(project_id, folder)
            file_path = folder_path.joinpath(filename)
            
            os.makedirs(folder_path, exist_ok=True)
            file.save(file_path)
            
            # 更新项目的最后修改时间
            config_path = current_app.projects_folder.joinpath(project_id, 'config.json')
            with open(config_path, 'r+', encoding='utf-8') as f:
                config = json.load(f)
                config['updated_at'] = datetime.now().isoformat()
                f.seek(0)
                json.dump(config, f, ensure_ascii=False, indent=2)
                f.truncate()
            
            return jsonify({
                'message': '文件上传成功',
                'filename': filename,
                'size': os.path.getsize(file_path)
            })
            
        return jsonify({'error': '不支持的文件类型'}), 400
        
    except Exception as e:
        error_msg = f'文件上传失败: {str(e)}'
        current_app.logger.exception(error_msg)
        return jsonify({'error': error_msg}), 500


@project_bp.route('/<project_id>/export', methods=['GET'])
def export_project(project_id):
    """导出项目为zip文件"""
    try:
        project_dir = current_app.projects_folder.joinpath(project_id)
        if not project_dir.exists():
            return jsonify({'error': '项目不存在'}), 404
            
        # 创建临时zip文件
        zip_filename = f'project_{project_id}.zip'
        zip_path = current_app.projects_folder.joinpath(zip_filename)
        
        with zipfile.ZipFile(zip_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
            for root, dirs, files in os.walk(project_dir):
                for file in files:
                    file_path = os.path.join(root, file)
                    arcname = os.path.relpath(file_path, project_dir)
                    zipf.write(file_path, arcname)
        
        current_app.logger.info(f'项目导出成功: {project_id}')
        return send_file(
            zip_path,
            as_attachment=True,
            download_name=zip_filename,
            mimetype='application/zip'
        )
        
    except Exception as e:
        error_msg = f'项目导出失败: {str(e)}'
        current_app.logger.exception(error_msg)
        return jsonify({'error': error_msg}), 500
    finally:
        # 清理临时zip文件
        if zip_path.exists():
            zip_path.unlink()


@project_bp.route('/import', methods=['POST'])
def import_project():
    """导入项目"""
    try:
        if 'file' not in request.files:
            return jsonify({'error': '没有文件部分'}), 400
            
        file = request.files['file']
        if file.filename == '' or not file.filename.endswith('.zip'):
            return jsonify({'error': '请选择有效的项目文件(.zip)'}), 400
            
        # 创建临时目录解压文件
        temp_dir = current_app.projects_folder.joinpath('temp_import')
        temp_dir.mkdir(parents=True, exist_ok=True)
        
        try:
            # 保存并解压zip文件
            zip_path = temp_dir.joinpath('temp.zip')
            file.save(zip_path)
            
            with zipfile.ZipFile(zip_path, 'r') as zipf:
                zipf.extractall(temp_dir)
            
            # 查找并验证项目配置文件
            config_file = None
            for root, dirs, files in os.walk(temp_dir):
                if 'config.json' in files:
                    config_file = root.joinpath('config.json')
                    break
            
            if not config_file:
                return jsonify({'error': '无效的项目文件：未找到配置文件'}), 400
                
            # 读取项目配置
            with open(config_file, 'r', encoding='utf-8') as f:
                config = json.load(f)
            
            project_id = config['id']
            project_dir = current_app.projects_folder.joinpath(project_id)
            
            # 如果项目已存在，返回错误
            if project_dir.exists():
                return jsonify({'error': '项目已存在'}), 400
                
            # 移动项目文件到正确位置
            shutil.move(config_file.parent, project_dir)
            
            current_app.logger.info(f'项目导入成功: {project_id}')
            return jsonify({
                'message': '项目导入成功',
                'project': config
            })
            
        finally:
            # 清理临时文件
            shutil.rmtree(temp_dir, ignore_errors=True)
        
    except Exception as e:
        error_msg = f'项目导入失败: {str(e)}'
        current_app.logger.exception(error_msg)
        return jsonify({'error': error_msg}), 500


@project_bp.route('/upload', methods=['POST'])
def upload_file():
    try:
        # record request information
        current_app.logger.info(f'收到文件上传请求 - Content-Type: {request.content_type}')
        current_app.logger.info(f'header: {dict(request.headers)}')
        
        if 'file' not in request.files:
            error_msg = '没有文件部分'
            current_app.logger.error(f'文件上传失败: {error_msg}')
            return jsonify({'error': error_msg}), 400
        
        file = request.files['file']
        current_app.logger.info(f'接收到文件: {file.filename}')
        
        if file.filename == '':
            error_msg = '没有选择文件'
            current_app.logger.error(f'file upload failed: {error_msg}')
            return jsonify({'error': error_msg}), 400
        
        if file and allowed_file(file.filename):
            filename = secure_filename(file.filename)
            file_path = current_app.upload_folder.joinpath(filename)
            
            # ensure upload directory exists
            current_app.upload_folder.mkdir(parents=True, exist_ok=True)
            
            current_app.logger.info(f'准备保存文件到: {file_path}')
            file.save(file_path)
            
            file_size = os.path.getsize(file_path)
            current_app.logger.info(f'file upload success: {filename}, size: {file_size} bytes')
            
            return jsonify({
                'message': 'file upload success',
                'filename': filename,
                'size': file_size
            })
        
        error_msg = f'unsupported file type: {file.filename}'
        current_app.logger.error(f'file upload failed: {error_msg}')
        return jsonify({'error': error_msg}), 400
        
    except Exception as e:
        error_msg = f'error during file upload: {str(e)}'
        current_app.logger.exception(error_msg)  # this will record the full stack trace
        return jsonify({'error': error_msg}), 500


@project_bp.route('/files')
def list_files():
    try:
        files = []
        for filename in os.listdir(current_app.upload_folder):
            file_path = current_app.upload_folder.joinpath(filename)
            files.append({
                'name': filename,
                'path': f'/api/files/{filename}',
                'size': os.path.getsize(file_path)
            })
        current_app.logger.info(f'get file list success, {len(files)} files')
        return jsonify(files)
    except Exception as e:
        error_msg = f'get file list failed: {str(e)}'
        current_app.logger.exception(error_msg)
        return jsonify({'error': error_msg}), 500

@project_bp.route('/files/<filename>')
def download_file(filename):
    try:
        file_path = current_app.upload_folder.joinpath(filename)
        if not os.path.exists(file_path):
            error_msg = f'file not found: {filename}'
            current_app.logger.error(error_msg)
            return jsonify({'error': error_msg}), 404
            
        current_app.logger.info(f'下载文件: {filename}')
        return send_from_directory(current_app.upload_folder, filename)
    except Exception as e:
        error_msg = f'download file failed: {filename}: {str(e)}'
        current_app.logger.exception(error_msg)
        return jsonify({'error': error_msg}), 500