import { defineStore } from "pinia";
import {
  type appType,
  store,
  getConfig,
  storageLocal,
  deviceDetection,
  responsiveStorageNameSpace
} from "../utils";
import { emitter } from "@/utils/mitt";

export const useAppStore = defineStore({
  id: "pure-app",
  state: (): appType => ({
    sidebar: {
      opened:
        storageLocal().getItem<StorageConfigs>(
          `${responsiveStorageNameSpace()}layout`
        )?.sidebarStatus ?? getConfig().SidebarStatus,
      withoutAnimation: false,
      isClickCollapse: false
    },
    showStartupModal: true,
    // 这里的layout用于监听容器拖拉后恢复对应的导航模式
    layout:
      storageLocal().getItem<StorageConfigs>(
        `${responsiveStorageNameSpace()}layout`
      )?.layout ?? getConfig().Layout,
    device: deviceDetection() ? "mobile" : "desktop",
    // 浏览器窗口的可视区域大小
    viewportSize: {
      width: document.documentElement.clientWidth,
      height: document.documentElement.clientHeight
    }
  }),
  getters: {
    getSidebarStatus(state) {
      return state.sidebar.opened;
    },
    getDevice(state) {
      return state.device;
    },
    getViewportWidth(state) {
      return state.viewportSize.width;
    },
    getViewportHeight(state) {
      return state.viewportSize.height;
    },
    getShowStartupModal(state) {
      return state.showStartupModal;
    }
  },
  actions: {
    setShowStartupModal(show: boolean) {
      this.showStartupModal = show;
    },
    TOGGLE_SIDEBAR(opened?: boolean, resize?: string) {
      const layout = storageLocal().getItem<StorageConfigs>(
        `${responsiveStorageNameSpace()}layout`
      );
      
      const newLayout = layout ?? {
        sidebarStatus: getConfig().SidebarStatus,
        layout: getConfig().Layout
      }

      if (opened && resize) {
        this.sidebar.withoutAnimation = true;
        this.sidebar.opened = true;
        newLayout.sidebarStatus = true;
      } else if (!opened && resize) {
        this.sidebar.withoutAnimation = true;
        this.sidebar.opened = false;
        newLayout.sidebarStatus = false;
      } else if (!opened && !resize) {
        this.sidebar.withoutAnimation = false;
        this.sidebar.opened = !this.sidebar.opened;
        this.sidebar.isClickCollapse = !this.sidebar.opened;
        newLayout.sidebarStatus = this.sidebar.opened;
      }
      storageLocal().setItem(`${responsiveStorageNameSpace()}layout`, newLayout);
    },
    async toggleSideBar(opened?: boolean, resize?: string) {
      // 在侧边栏动画开始前暂停表格resize
      emitter.emit("pauseTableResize" as never);

      await this.TOGGLE_SIDEBAR(opened, resize);

      // 侧边栏动画完成后恢复表格resize
      setTimeout(() => {
        emitter.emit("resumeTableResize" as never);
      }, 50); // 瞬时恢复，50ms
    },
    toggleDevice(device: string) {
      this.device = device;
    },
    setLayout(layout: string) {
      this.layout = layout;
    },
    setViewportSize(size: { width: number; height: number }) {
      this.viewportSize = size;
    }
  }
});

export function useAppStoreHook() {
  return useAppStore(store);
}
