/**
 * 表格性能优化工具
 * 提供各种表格性能优化的实用函数
 */

import { nextTick } from 'vue';
import { debounce } from 'lodash-es';

/**
 * 数据分片处理器
 * 用于处理大数据集的分片加载
 */
export class DataChunkProcessor {
  private chunkSize: number;
  private processingQueue: Array<() => Promise<void>> = [];
  private isProcessing = false;

  constructor(chunkSize = 1000) {
    this.chunkSize = chunkSize;
  }

  /**
   * 分片处理数据
   * @param data 原始数据
   * @param processor 处理函数
   * @param onProgress 进度回调
   */
  async processInChunks<T>(
    data: T[],
    processor: (chunk: T[], startIndex: number) => Promise<void>,
    onProgress?: (progress: number) => void
  ): Promise<void> {
    const totalChunks = Math.ceil(data.length / this.chunkSize);
    
    for (let i = 0; i < totalChunks; i++) {
      const start = i * this.chunkSize;
      const end = Math.min(start + this.chunkSize, data.length);
      const chunk = data.slice(start, end);
      
      await processor(chunk, start);
      
      if (onProgress) {
        onProgress((i + 1) / totalChunks);
      }
      
      // 让出控制权，避免阻塞UI
      await new Promise(resolve => setTimeout(resolve, 0));
    }
  }

  /**
   * 队列化处理
   * @param task 任务函数
   */
  async queueTask(task: () => Promise<void>): Promise<void> {
    return new Promise((resolve, reject) => {
      this.processingQueue.push(async () => {
        try {
          await task();
          resolve();
        } catch (error) {
          reject(error);
        }
      });
      
      this.processQueue();
    });
  }

  private async processQueue(): Promise<void> {
    if (this.isProcessing || this.processingQueue.length === 0) {
      return;
    }
    
    this.isProcessing = true;
    
    while (this.processingQueue.length > 0) {
      const task = this.processingQueue.shift();
      if (task) {
        await task();
      }
    }
    
    this.isProcessing = false;
  }
}

/**
 * 表格渲染优化器
 */
export class TableRenderOptimizer {
  private renderQueue: Array<() => void> = [];
  private isRendering = false;
  private frameId: number | null = null;

  /**
   * 批量渲染操作
   * @param operations 渲染操作数组
   */
  batchRender(operations: Array<() => void>): void {
    this.renderQueue.push(...operations);
    this.scheduleRender();
  }

  /**
   * 调度渲染
   */
  private scheduleRender(): void {
    if (this.frameId !== null) {
      return;
    }

    this.frameId = requestAnimationFrame(() => {
      this.executeRender();
      this.frameId = null;
    });
  }

  /**
   * 执行渲染
   */
  private executeRender(): void {
    if (this.isRendering) {
      return;
    }

    this.isRendering = true;

    try {
      while (this.renderQueue.length > 0) {
        const operation = this.renderQueue.shift();
        if (operation) {
          operation();
        }
      }
    } finally {
      this.isRendering = false;
    }
  }

  /**
   * 清空渲染队列
   */
  clearQueue(): void {
    this.renderQueue.length = 0;
    if (this.frameId !== null) {
      cancelAnimationFrame(this.frameId);
      this.frameId = null;
    }
  }
}

/**
 * 内存管理器
 */
export class MemoryManager {
  private caches = new Map<string, Map<string, any>>();
  private maxCacheSize = 50;

  /**
   * 设置缓存
   * @param namespace 命名空间
   * @param key 键
   * @param value 值
   */
  setCache(namespace: string, key: string, value: any): void {
    if (!this.caches.has(namespace)) {
      this.caches.set(namespace, new Map());
    }

    const cache = this.caches.get(namespace)!;
    
    // 限制缓存大小
    if (cache.size >= this.maxCacheSize) {
      const firstKey = cache.keys().next().value;
      cache.delete(firstKey);
    }

    cache.set(key, value);
  }

  /**
   * 获取缓存
   * @param namespace 命名空间
   * @param key 键
   */
  getCache(namespace: string, key: string): any {
    return this.caches.get(namespace)?.get(key);
  }

  /**
   * 清空缓存
   * @param namespace 命名空间（可选）
   */
  clearCache(namespace?: string): void {
    if (namespace) {
      this.caches.delete(namespace);
    } else {
      this.caches.clear();
    }
  }

  /**
   * 强制垃圾回收（开发环境）
   */
  forceGC(): void {
    if (process.env.NODE_ENV === 'development' && 'gc' in window) {
      (window as any).gc();
      console.log('🗑️ Forced garbage collection');
    }
  }
}

/**
 * 防抖渲染函数
 */
export const debouncedRender = debounce((callback: () => void) => {
  requestAnimationFrame(callback);
}, 16); // 约1帧时间

/**
 * 节流渲染函数
 */
export const throttledRender = (() => {
  let isThrottled = false;
  
  return (callback: () => void) => {
    if (isThrottled) return;
    
    isThrottled = true;
    requestAnimationFrame(() => {
      callback();
      isThrottled = false;
    });
  };
})();

/**
 * 异步批量更新
 */
export async function batchUpdate(updates: Array<() => void | Promise<void>>): Promise<void> {
  for (const update of updates) {
    await update();
    await nextTick();
  }
}

/**
 * 检查是否为大数据集
 */
export function isLargeDataset(data: any[][], threshold = 5000): boolean {
  return data.length > threshold;
}

/**
 * 优化数据结构
 */
export function optimizeDataStructure(data: any[][]): any[][] {
  // 移除完全空的行
  return data.filter(row => 
    row.some(cell => cell !== null && cell !== undefined && cell !== '')
  );
}

/**
 * 创建全局实例
 */
export const dataChunkProcessor = new DataChunkProcessor();
export const tableRenderOptimizer = new TableRenderOptimizer();
export const memoryManager = new MemoryManager();
