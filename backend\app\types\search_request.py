from dataclasses import dataclass
import json
from typing import Literal, TypedDict, Union

class GAParams(TypedDict):
    population_size: int
    generations: int
    crossover: float
    mutation: float
    max_iter: int
    max_time: int
    criterion: float

class SearchTarget(TypedDict):
    name: str
    value: float

class SearchAlgorithm(TypedDict):
    algorithm: Literal["GeneticAlgorithm"]
    params: Union[GAParams]

class SearchRequest(TypedDict):
    task_uid: str
    target: SearchTarget
    search: SearchAlgorithm

@dataclass
class SearchConfig(object):
    task_id: Union[int, None]
    task_uid: str
    target_name: str
    target_value: float
    alg_name: str
    alg_param: dict
    asynchronous: bool
    is_rabbitmq_ready: bool

    def to_dict(self) -> dict:
        return {
            "task_id": self.task_id if self.task_id is not None else 'None',
            "task_uid": self.task_uid,
            "target_name": self.target_name,
            "target_value": self.target_value,
            "alg_name": self.alg_name,
            "alg_param": self.alg_param,
            "asynchronous": self.asynchronous,
            "is_rabbitmq_ready": self.is_rabbitmq_ready
        }
    
    def to_json(self) -> str:
        return json.dumps(self.to_dict(), ensure_ascii=False)