// import "@/utils/sso";
import { getConfig } from "@/config";
import NProgress from "@/utils/progress";
import { buildHierarchyTree } from "@/utils/tree";
import remainingRouter from "./modules/remaining";
import { useMultiTagsStoreHook } from "@/store/modules/multiTags";
import { usePermissionStoreHook } from "@/store/modules/permission";
import { useWorkspaceStoreHook } from "@/store/modules/workspace";
import { isUrl, openLink, storageLocal, isAllEmpty, isEqual } from "@pureadmin/utils";
import { ElMessageBox } from "element-plus";
import {
  ascending,
  getTopMenu,
  initRouter,
  isOneOfArray,
  getHistoryMode,
  findRouteByPath,
  handleAliveRoute,
  formatTwoStageRoutes,
  formatFlatteningRoutes
} from "./utils";
import {
  type Router,
  createRouter,
  type RouteRecordRaw,
  type RouteComponent
} from "vue-router";
import { type DataInfo, userKey } from "@/utils/auth";

/** 自动导入全部静态路由，无需再手动引入！匹配 src/router/modules 目录（任何嵌套级别）中具有 .ts 扩展名的所有文件，除了 remaining.ts 文件
 * 如何匹配所有文件请看：https://github.com/mrmlnc/fast-glob#basic-syntax
 * 如何排除文件请看：https://cn.vitejs.dev/guide/features.html#negative-patterns
 */
const modules: Record<string, any> = import.meta.glob(
  ["./modules/**/*.ts", "!./modules/**/remaining.ts", "!./modules/**/start.ts"],
  {
    eager: true
  }
);

/** 原始静态路由（未做任何处理） */
const routes: Array<RouteRecordRaw> = [];

Object.keys(modules).forEach(key => {
  routes.push(modules[key].default as RouteRecordRaw);
});

/** 导出处理后的静态路由（三级及以上的路由全部拍成二级） */
export const constantRoutes: Array<RouteRecordRaw> = formatTwoStageRoutes(
  formatFlatteningRoutes(buildHierarchyTree(ascending(routes)))
);

/** 用于渲染菜单，保持原始层级 */
export const constantMenus: Array<RouteComponent> = ascending(
  routes.flat(Infinity) as RouteComponent[]
).concat(...remainingRouter as RouteComponent[]);

/** 不参与菜单的路由 */
export const remainingPaths = (remainingRouter as RouteRecordRaw[]).map(route => {
  return route.path;
});

/** 创建路由实例 */
export const router: Router = createRouter({
  history: getHistoryMode(import.meta.env.VITE_ROUTER_HISTORY),
  routes: (
    constantRoutes as RouteRecordRaw[]
  ).concat(
    remainingRouter as RouteRecordRaw[], 
  ),
  strict: true,
  scrollBehavior(to, from, savedPosition) {
    return new Promise(resolve => {
      if (savedPosition) {
        return savedPosition;
      } else {
        if (from.meta.saveSrollTop) {
          const top: number =
            document.documentElement.scrollTop || document.body.scrollTop;
          resolve({ left: 0, top });
        }
      }
    });
  }
});

/** 重置路由 */
export function resetRouter() {
  router.getRoutes().forEach(route => {
    const { name, meta } = route;
    if (name && router.hasRoute(name) && meta?.backstage) {
      router.removeRoute(name);
      router.options.routes = formatTwoStageRoutes(
        formatFlatteningRoutes(
          buildHierarchyTree(ascending(routes as RouteRecordRaw[]))
        )
      );
    }
  });
  usePermissionStoreHook().clearAllCachePage();
}

/** 路由白名单 */
const whiteList = ["/login"];

const { VITE_HIDE_HOME } = import.meta.env;

router.beforeEach(async (to: ToRouteType, _from, next) => {
  if (to.meta?.keepAlive) {
    handleAliveRoute(to, "add");
    if (_from.name === undefined || _from.name === "Redirect") {
      handleAliveRoute(to);
    }
  }

  // 检查是否需要保存确认
  const workspaceStore = useWorkspaceStoreHook();
  const currentFilePath = workspaceStore.getCurrentFilePath;

  // 如果从dataImandEx页面离开且有修改的Excel文件，提示保存
  if (_from.path === '/dataManagement/imandex' &&
      currentFilePath &&
      workspaceStore.isDataModified(currentFilePath) &&
      workspaceStore.isExcelFile(currentFilePath) &&
      to.path !== '/dataManagement/imandex') {
    try {
      await ElMessageBox.confirm(
        `文件 "${currentFilePath.split(/[/\\]/).pop()}" 已被修改，是否要保存更改？`,
        '确认离开页面',
        {
          confirmButtonText: '保存并离开',
          cancelButtonText: '不保存',
          distinguishCancelAndClose: true,
          type: 'warning'
        }
      );

      // 用户选择保存，触发导出操作
      window.dispatchEvent(new CustomEvent('export-current-file'));
      // 等待导出完成后再导航
      setTimeout(() => {
        next();
      }, 500);
      return;
    } catch (action) {
      if (action === 'cancel') {
        // 用户选择不保存，直接离开
        next();
        return;
      } else {
        // 用户点击了X或按了ESC，取消导航
        next(false);
        return;
      }
    }
  }

  // Auto login to bypass authentication - create default user if none exists
  let userInfo = storageLocal().getItem<DataInfo<number>>(userKey);
  if (!userInfo) {
    const defaultUserData = {
      accessToken: "default-token",
      refreshToken: "default-refresh-token",
      expires: Date.now() + 24 * 60 * 60 * 1000, // 24 hours from now
      username: "user",
      nickname: "用户",
      avatar: "",
      roles: ["admin"],
      permissions: ["*:*:*"]
    };
    // Set the user info in localStorage
    storageLocal().setItem(userKey, defaultUserData);
    userInfo = defaultUserData;
    console.log("Auto login: Default user created in router guard");
  }

  NProgress.start();
  const externalLink = isUrl(to?.name as string);
  if (!externalLink) {
    to.matched.some(item => {
      if (!item.meta.title) return "";
      const Title = getConfig().Title;
      if (Title) document.title = `${item.meta.title} | ${Title}`;
      else document.title = item.meta.title as string;
    });
  }
  function toCorrectRoute() {
    whiteList.includes(to.fullPath) ? next(_from.fullPath) : next();
  }
  if (userInfo) {
    if (to.meta?.roles && !isOneOfArray(to.meta?.roles, userInfo?.roles || [])) {
      next({ path: "/error/403" });
    }
    if (VITE_HIDE_HOME === "true" && to.fullPath === "/welcome") {
      next({ path: "/error/404" });
    }
    if (_from?.name) {
      if (externalLink) {
        openLink(to?.name as string);
        NProgress.done();
      } else {
        // 在导航时也需要创建标签页，但要避免重复添加
        const { path, name, meta, params, query } = to;
        if (meta?.title) {
          // 检查是否已经存在相同的标签页
          const existingTags = useMultiTagsStoreHook().multiTags;
          const isDuplicate = existingTags.some(tag => {
            if (path === "/welcome" || meta?.fixedTag) {
              return tag.path === path;
            }
            return tag.path === path &&
                   isEqual(tag?.query, query) &&
                   isEqual(tag?.params, params);
          });

          if (!isDuplicate) {
            useMultiTagsStoreHook().handleTags("push", {
              path,
              name,
              meta,
              params,
              query
            });
          }
        }
        toCorrectRoute();
      }
    } else {
      if (
        usePermissionStoreHook().wholeMenus.length === 0 &&
        to.path !== "/login"
      ) {
        initRouter().then((currentRouter: Router) => {
          if (!useMultiTagsStoreHook().getMultiTagsCache) {
            const { path, name, meta, params, query } = to;
            let route = findRouteByPath(
              path,
              currentRouter.options.routes[0]?.children || []
            );

            // 如果找不到精确匹配的路由，尝试通过路由名称查找（适用于动态路由）
            if (!route && name) {
              const allRoutes = currentRouter.getRoutes();
              route = allRoutes.find(r => r.name === name);
            }

            getTopMenu(true);
            if (route && route.meta?.title) {
              if (isAllEmpty(route.parentId) && route.meta?.backstage) {
                const { path: routePath, name: routeName, meta: routeMeta } = route.children[0];
                useMultiTagsStoreHook().handleTags("push", {
                  path: routePath,
                  name: routeName,
                  meta: routeMeta,
                  params,
                  query
                });
              } else {
                useMultiTagsStoreHook().handleTags("push", {
                  path: to.path, // 使用实际的路径而不是路由定义中的路径
                  name: route.name,
                  meta: route.meta,
                  params,
                  query
                });
              }
            } else if (meta?.title) {
              // 如果路由定义中没有找到，但是当前路由有meta信息，直接使用当前路由信息
              useMultiTagsStoreHook().handleTags("push", {
                path,
                name,
                meta,
                params,
                query
              });
            }
          }
          if (isAllEmpty(to.name)) currentRouter.push(to.fullPath);
        });
      }
      toCorrectRoute();
    }
  } else {
    if (to.path !== "/login") {
      if (whiteList.indexOf(to.path) !== -1) {
        next();
      } else {
        next({ path: "/login" });
      }
    } else {
      next();
    }
  }
});

router.afterEach(() => {
  NProgress.done();
});

export default router;
