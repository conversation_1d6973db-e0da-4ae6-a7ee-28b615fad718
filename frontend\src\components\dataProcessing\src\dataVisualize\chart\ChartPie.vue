<template>
  <div class="chart-content" ref="chartRef"></div>
</template>

<script setup lang="ts">
import { ref, onMounted, onBeforeUnmount, watch } from "vue";
import * as echarts from "echarts";
import type { ECharts } from "echarts";

const props = defineProps<{
  chartData: any[][];
  columns: string[];
  selectedColumns: string[];
}>();

const chartRef = ref<HTMLElement | null>(null);
let chartInstance: ECharts | null = null;

// 导出函数，供父组件调用
const getChartInstance = () => chartInstance;
const getChartOptions = () => createPieOption();
const getChartData = () => {
  if (props.selectedColumns.length === 0) return null;
  
  const labelIndex = props.columns.indexOf(props.selectedColumns[0]);
  
  // 统计值的频率
  const valueMap = new Map();
  props.chartData.forEach(row => {
    const value = row[labelIndex];
    if (value !== null && value !== undefined) {
      if (!valueMap.has(value)) {
        valueMap.set(value, 0);
      }
      valueMap.set(value, valueMap.get(value) + 1);
    }
  });
  
  // 取频率最高的10个值
  const sortedEntries = [...valueMap.entries()]
    .sort((a, b) => b[1] - a[1])
    .slice(0, 10);
  
  return {
    columns: [props.selectedColumns[0], '数量'],
    data: sortedEntries.map(entry => [entry[0], entry[1]])
  };
};
defineExpose({ getChartInstance, getChartOptions, getChartData });

// 创建饼图
const createPieOption = () => {
  if (props.selectedColumns.length === 0) return {};
  
  // 始终使用第一个选择的列作为数据源
  const labelIndex = props.columns.indexOf(props.selectedColumns[0]);
  
  // 统计值的频率，无论选择了几个列
  const valueMap = new Map();
  props.chartData.forEach(row => {
    const value = row[labelIndex];
    if (value !== null && value !== undefined) {
      if (!valueMap.has(value)) {
        valueMap.set(value, 0);
      }
      valueMap.set(value, valueMap.get(value) + 1);
    }
  });
  
  // 取频率最高的10个值
  const sortedEntries = [...valueMap.entries()]
    .sort((a, b) => b[1] - a[1])
    .slice(0, 10);
  
  const data = sortedEntries.map(entry => ({
    name: entry[0],
    value: entry[1]
  }));
  
  return {
    tooltip: {
      trigger: 'item',
      formatter: '{a}<br/>特征值{b}<br/>共{c}个<br/>占比({d}%)'
    },
    legend: {
      orient: 'horizontal', // 改为水平布局
      top: 10,             // 放置在顶部
      left: 'center',      // 水平居中
      type: 'scroll',
      pageButtonItemGap: 5, // 按钮间距
      textStyle: {
        fontSize: 10 // 减小字体大小
      }
    },
    series: [{
      name: props.selectedColumns[0],
      type: 'pie',
      radius: ['40%', '70%'],
      center: ['50%', '60%'], // 将图表中心点下移，为图例腾出空间
      avoidLabelOverlap: false,
      itemStyle: {
        borderRadius: 10,
        borderColor: '#fff',
        borderWidth: 2
      },
      label: {
        show: false,
        position: 'center'
      },
      emphasis: {
        label: {
          show: true,
          fontSize: '12',
          fontWeight: 'bold'
        }
      },
      labelLine: {
        show: false
      },
      data: data
    }]
  };
};

// 初始化图表
const initChart = () => {
  if (chartRef.value) {
    chartInstance = echarts.init(chartRef.value);
    updateChart();
  }
};

// 更新图表
const updateChart = () => {
  if (chartInstance) {
    const option = createPieOption();
    chartInstance.setOption(option);
  }
};

// 监听数据变化，更新图表
watch(() => props.chartData, updateChart, { deep: true });
watch(() => props.selectedColumns, updateChart, { deep: true });

// 窗口大小变化时，重新调整图表大小
const handleResize = () => {
  if (chartInstance) {
    chartInstance.resize();
  }
};

onMounted(() => {
  initChart();
  window.addEventListener('resize', handleResize);
});

onBeforeUnmount(() => {
  if (chartInstance) {
    chartInstance.dispose();
    chartInstance = null;
  }
  window.removeEventListener('resize', handleResize);
});
</script>

<style scoped>
.chart-content {
  width: 100%;
  height: 100%;
}
</style> 