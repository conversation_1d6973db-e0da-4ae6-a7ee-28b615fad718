from app.types.model_request import ModelConfig
from flask import current_app
from typing import cast
from app.types.flaskapp import FlaskWithExecutor
from app.services.DBService import DBService
from app.services.FileService import FileService
from app.services.RabbitmqService import ProgressReporter, setup_rabbitmq
from app.services.RegressionService import RegressionService
from datetime import datetime
from typing import Optional, Dict, Any
import json
from app.utils.Regression import Regression
import time
import logging
from app.types.search_request import SearchConfig
from app.services.SearchService import SearchService

logger = logging.getLogger()

def build_model_task(model_config: ModelConfig) -> None:
    is_rabbitmq_ready = model_config.is_rabbitmq_ready
    DBService.update_model_task(model_config.task_uid, status="running", updated_at=datetime.now())
    if is_rabbitmq_ready:
        progress_reporter = ProgressReporter(model_config.task_uid)
        report = progress_reporter.report_model_build_task
    else:
        progress_reporter = None
        report = None
    try:
        regression_service = RegressionService(
            model_config, 
            report=report,
            update=DBService.update_model_task
        )
        model, info = regression_service.fit()
        DBService.update_model_task(model_config.task_uid, status="completed", result=info, updated_at=datetime.now(), progress="completed")
        FileService.save_model({"model": model, "info": info}, str(model_config.task_uid))
        if is_rabbitmq_ready and progress_reporter is not None:
            progress_reporter.report_model_build_task(info, "100", 'completed', "model build completed")
            progress_reporter.close()
    except Exception as e:
        DBService.update_model_task(model_config.task_uid, status="failed", updated_at=datetime.now(), progress="failed")
        logger.error(f"build_model_task: model build failed: {e}")
        if is_rabbitmq_ready and progress_reporter is not None:
            progress_reporter.report_model_build_task(info, "100", 'failed', "model build failed")
            progress_reporter.close()
    
def build_search_task(search_config: SearchConfig, model_config: ModelConfig, model: Dict[str, Any]) -> None:
    rabbitmq_ready = search_config.is_rabbitmq_ready
    DBService.update_search_task(search_config.task_id, status="running", updated_at=datetime.now())
    if rabbitmq_ready:
        progress_reporter = ProgressReporter(str(search_config.task_id))
        report = progress_reporter.report_search_task
    else:
        progress_reporter = None
        report = None
    try:
        search_service = SearchService(
            search_config,
            model_config=model_config,
            model=model,
            report=report,
            update=DBService.update_search_task
        )
        search_service.fit()
        DBService.update_search_task(search_config.task_id, status="completed", updated_at=datetime.now(), progress="completed")
        if rabbitmq_ready and progress_reporter is not None:
            progress_reporter.report_search_task('', "100", 'completed', "search task completed")
            progress_reporter.close()
    except Exception as e:
        DBService.update_search_task(search_config.task_id, status="failed", updated_at=datetime.now(), progress="failed")
        logger.error(f"build_search_task: search task failed: {e}")
        if rabbitmq_ready and progress_reporter is not None:
            progress_reporter.report_search_task('', "100", 'failed', "search task failed")
            progress_reporter.close()

class TaskService:

    @staticmethod
    def submit_model_task(model_config: ModelConfig) -> None:
        """
        提交模型任务
        model_config:
            task_uid: str
            x_train: Union[pd.DataFrame, pd.Series]
            y_train: Union[pd.DataFrame, pd.Series]
            x_test: Optional[Union[pd.DataFrame, pd.Series]]
            y_test: Optional[Union[pd.DataFrame, pd.Series]]
            cv: Optional[Union[bool, int]]
            loocv: Optional[bool]
            test: Optional[bool]
            asynchronous: Optional[bool]
            alg_name: str
            alg_param: Optional[Dict[str, Any]]
        """
        if model_config.asynchronous:
            cast(FlaskWithExecutor, current_app).executor.submit(build_model_task, model_config)
        else:
            build_model_task(model_config)
        
    @staticmethod
    def submit_search_task(search_config: SearchConfig, model_config: ModelConfig, model: Dict[str, Any]) -> None:
        """
        提交搜索任务
        search_config:
            task_uid: str
            target_name: str
            target_value: float
            alg_name: str
            alg_param: Optional[Dict[str, Any]]
        """
        cast(FlaskWithExecutor, current_app).executor.submit(build_search_task, search_config, model_config, model)