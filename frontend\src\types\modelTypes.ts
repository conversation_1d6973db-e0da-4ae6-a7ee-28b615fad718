/**
 * 模型配置接口
 */
export interface ModelConfig {
  name: string;
  displayName: string;
  description: string;
  category: string;
  type: string;
  introduction?: {
    detailedDescription?: string;
    usageTips?: string[];
    scenarios?: string;
    mainParams?: Array<{
      name: string;
      displayName: string;
      description: string;
      value: any;
      type: string;
    }>;
  };
  defaultParams?: Record<string, any>;
  evaluation?: Record<string, any>;
}

/**
 * 模型元数据接口
 */
export interface ModelMetadata {
  id: string;
  fileName: string;
  config: ModelConfig;
  sourcePath: string;
}

/**
 * 模型分类配置
 */
export interface ModelCategory {
  key: string;
  label: string;
  description?: string;
  models: ModelMetadata[];
} 