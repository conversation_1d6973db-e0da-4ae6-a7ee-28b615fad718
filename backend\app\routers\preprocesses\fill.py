from flask import Blueprint, request, make_response, current_app, jsonify
import traceback, uuid, numpy as np, pandas as pd
from app.utils.Regression import Regression
from app.utils.params import camel_to_snake, snake_to_camel
from sklearn.model_selection import train_test_split
from app.utils.Preprocess import Preprocess
fill_bp = Blueprint("fill", __name__)

@fill_bp.route("/", methods=["POST"])
def fill_build():
    post_data = request.json
    post_data = camel_to_snake(post_data)

    preprocess_info = post_data.get("preprocess")
    dataset_info = post_data.get("dataset")

    algorithm = preprocess_info.get("algorithm", {})

    dataset_meta = dataset_info.get("meta", {})
    data = dataset_info.get("data", [])
    
    data = pd.DataFrame(data, columns=dataset_meta.get("headers"))
    params = algorithm.get("params", {})

    preprocess = Preprocess(data, None)
    data = preprocess.fill(algorithm.get("name"), params)

    return_data = {
        "data": data.values.tolist(),
        "meta": {
            "headers": {
                "all": data.columns.tolist()
            }
        }
    }
    return make_response(jsonify({"code": 200, "message": "success", "data": return_data}), 200)

