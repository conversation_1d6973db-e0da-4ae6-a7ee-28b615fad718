<template>
  <div class="re-table-wrapper">
    <slot :data="paginatedData" />
    <div v-if="showPagination" class="re-table-pagination">
      <el-pagination
        v-model:currentPage="currentPage"
        v-model:page-size="internalPageSize"
        :page-sizes="[20, 50, 100, 200]"
        :total="data.length"
        layout="total, prev, pager, next, sizes"
        background
        @size-change="handleSizeChange"
      />
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, computed } from "vue";
import { useEpThemeStoreHook } from "@/store/modules/epTheme";

const props = withDefaults(
  defineProps<{
    data: any[];
    pageSize?: number;
  }>(),
  {
    pageSize: 20,
  },
);

const epThemeColor = useEpThemeStoreHook().getEpThemeColor;
const currentPage = ref(1);
const internalPageSize = ref(props.pageSize);

const showPagination = computed(() => props.data.length > internalPageSize.value);

const paginatedData = computed(() => {
  if (!showPagination.value) {
    return props.data;
  }
  const startIndex = (currentPage.value - 1) * internalPageSize.value;
  const endIndex = startIndex + internalPageSize.value;
  return props.data.slice(startIndex, endIndex);
});

const handleSizeChange = () => {
  currentPage.value = 1;
};
</script>

<style scoped>
.re-table-wrapper :deep(.el-table) {
  background-color: transparent;
}

/* Remove default top and bottom borders of el-table */
.re-table-wrapper :deep(.el-table::before),
.re-table-wrapper :deep(.el-table::after) {
  display: none;
}

.re-table-wrapper :deep(.el-table__header-wrapper th.el-table__cell) {
  background-color: transparent !important; /* Make header transparent */
  color: #999999;
  font-weight: 400;
  border: none;
}

.re-table-wrapper :deep(.el-table__body) {
  border-collapse: separate;
  border-spacing: 0 8px; /* Vertical spacing between rows */
}

.re-table-wrapper :deep(.el-table__body tr) {
  border-radius: 80px;
  background: #f9fbff;
}

.re-table-wrapper :deep(.el-table__body td.el-table__cell) {
  border: none;
  background: transparent; /* Cells should be transparent to show tr background */
}

.re-table-wrapper :deep(.el-table__body tr:hover) {
  background-color: #f0f4ff !important; /* Hover effect on whole row */
}

.re-table-wrapper :deep(.el-table__body tr:hover > td.el-table__cell) {
  background-color: transparent !important;
}

.re-table-wrapper :deep(.el-table__body tr td:first-child) {
  border-top-left-radius: 80px;
  border-bottom-left-radius: 80px;
}

.re-table-wrapper :deep(.el-table__body tr td:last-child) {
  border-top-right-radius: 80px;
  border-bottom-right-radius: 80px;
}

.re-table-pagination {
  display: flex;
  justify-content: flex-end;
  margin-top: 16px;
}

/* Pagination Styles */
.re-table-pagination :deep(.el-pagination.is-background .btn-prev),
.re-table-pagination :deep(.el-pagination.is-background .btn-next) {
  border-radius: 50%; /* Circular prev/next buttons */
  background: #F9FBFF;
}

.re-table-pagination :deep(.el-pagination.is-background .el-pager li) {
  border-radius: 6px; /* Rounded corners for page numbers */
  background: #F9FBFF;
}

.re-table-pagination :deep(.el-pagination.is-background .el-pager li:not(.disabled).is-active) {
  background-color: v-bind(epThemeColor); /* Use theme color for active page */
}

</style> 