<template>
  <div class="empty-state">
    <div class="empty-state-content">
      <div v-if="image" class="empty-state-image">
        <img :src="image" alt="empty" />
      </div>
      <el-icon v-else class="empty-state-icon" :size="80" color="#909399">
        <DocumentAdd />
      </el-icon>
      <p class="empty-state-description">{{ description }}</p>
      <div v-if="$slots.default" class="empty-state-action">
        <slot />
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { DocumentAdd } from "@element-plus/icons-vue";

defineProps<{
  description?: string;
  image?: string;
}>();
</script>

<style lang="scss" scoped>
.empty-state {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  width: 100%;

  &-content {
    text-align: center;
    padding: 40px;
  }

  &-image {
    margin-bottom: 20px;
    img {
      max-width: 200px;
      max-height: 200px;
    }
  }

  &-icon {
    margin-bottom: 20px;
  }

  &-description {
    color: #909399;
    font-size: 14px;
    margin-bottom: 20px;
  }

  &-action {
    margin-top: 20px;
  }
}
</style>
