from app.services.DBService import DBService
from app.types.services import ModelTask, SearchTask
from app.services.RabbitmqService import ProgressConsumer
from typing import Union
import logging, json, time
from flask_socketio import emit
from flask import current_app
from app.types.flaskapp import FlaskWithExecutor
from typing import cast
from app.utils.params import snake_to_camel

logger_rmq = logging.getLogger("rabbitmq")
logger_socketio = logging.getLogger("socketio")

class ProgressService(object):

    @staticmethod
    def report(task_uid):
        model_task: Union[ModelTask, None] = None
        max_retry = 4
        retry_count = 0
        while True:
            model_task = DBService.get_model_info(task_uid)
            if model_task is None:
                retry_count += 1
                time.sleep(1)
                if retry_count >= max_retry:
                    break
            else:
                break
        if model_task is not None:
            if model_task.status == "running":
                if model_task.is_rabbitmq_ready and cast(FlaskWithExecutor, current_app).is_rabbitmq_ready:
                    def _callback(ch, method, properties, message):
                        logger_rmq.info(f"handle_model_info: [x] Received {task_uid}")
                        emit("model_info", json.dumps({"code": 200, "msg": "success", "data": snake_to_camel(message)}))
                        if message.get("progress") == "100":
                            consumer.stop_consuming()
                    consumer = ProgressConsumer(task_uid=task_uid, callback=_callback)
                    consumer.start_consuming()
                else:
                    emit("model_info", json.dumps({"code": 200, "msg": "success", "data": {"status": "running"}}))
            elif model_task.status == "completed":
                message = {
                        "task_uid": task_uid,
                        "model_info": model_task.result,
                        "progress": "100",
                        "status": "completed",
                    }
                emit("model_info", json.dumps({
                    "code": 200, 
                    "msg": "success", 
                    "data": snake_to_camel(message)
                }))
            elif model_task.status == "failed":
                message = {
                    "task_uid": task_uid,
                    "model_info": '',
                    "progress": "100",
                    "status": "failed",
                }
                emit("model_info", json.dumps({"code": 500, "msg": "failed", "data": snake_to_camel(message)}))
        else:
            raise ValueError(f"task_uid: {task_uid} not found")
    
    @staticmethod
    def report_search(task_id):
        search_task: Union[SearchTask, None] = None
        max_retry = 4
        retry_count = 0
        while True:
            search_task = DBService.get_search_info(task_id)
            if search_task is None:
                retry_count += 1
                time.sleep(1)
                if retry_count >= max_retry:
                    break
            else:
                break
        if search_task is not None:
            if search_task.status == "running":
                print("running")
                if search_task.is_rabbitmq_ready and cast(FlaskWithExecutor, current_app).is_rabbitmq_ready:
                    def _callback(ch, method, properties, message):
                        logger_rmq.info(f"handle_search_info: [x] Received {task_id}")
                        emit("search_info", json.dumps({"code": 200, "msg": "success", "data": snake_to_camel(message)}))
                        if message.get("progress") == "100":
                            consumer.stop_consuming()
                    consumer = ProgressConsumer(task_id=task_id, callback=_callback)
                    consumer.start_consuming()
                else:
                    emit("search_info", json.dumps({"code": 200, "msg": "success", "data": {"status": "running"}}))
            elif search_task.status == "completed":
                message = {
                    "task_id": task_id,
                    "search_info": search_task.result,
                    "progress": "100",
                    "status": "completed",
                }
                emit("search_info", json.dumps({"code": 200, "msg": "success", "data": snake_to_camel(message)}))
            elif search_task.status == "failed":
                message = {
                    "task_id": task_id,
                    "search_info": '',
                    "progress": "100",
                    "status": "failed",
                }
                emit("search_info", json.dumps({"code": 500, "msg": "failed", "data": snake_to_camel(message)}))
        else:
            raise ValueError(f"task_id: {task_id} not found")
