<template>
  <div class="table-content-wrapper" ref="wrapperRef">
    <div v-if="loading" class="loading-mask">
      <el-icon class="is-loading" :size="32">
        <Loading />
      </el-icon>
    </div>
    <hot-table
      ref="hotTableRef"
      :settings="settings"
      @after-change="handleAfterChange"
      @after-selection="handleAfterSelection"
    />
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted, watch, computed, onBeforeUnmount } from "vue";
import { HotTable } from "@handsontable/vue3";
import { registerAllModules } from "handsontable/registry";
import "handsontable/dist/handsontable.full.css";
import "handsontable/languages/zh-CN";
import { Loading } from "@element-plus/icons-vue";
import { debounce } from "lodash-es";

registerAllModules();

const props = defineProps<{
  settings: any;
  loading?: boolean;
}>();

const emit = defineEmits<{
  ready: [instance: any];
  change: [changes: any];
  selection: [selection: any];
}>();

const hotTableRef = ref();
const wrapperRef = ref<HTMLElement | null>(null);
let hotInstance: any = null;
let resizeObserver: ResizeObserver | null = null;

const handleAfterChange = (changes: any, source: string) => {
  if (source !== "loadData") {
    emit("change", { changes, source });
  }
};

const handleAfterSelection = (
  row: number,
  col: number,
  row2: number,
  col2: number,
) => {
  emit("selection", { row, col, row2, col2 });
};

const updateHotHeight = debounce(() => {
  if (hotInstance && wrapperRef.value) {
    hotInstance.updateSettings({
      height: wrapperRef.value.clientHeight,
    });
  }
}, 50);

onMounted(() => {
  if (hotTableRef.value?.hotInstance) {
    hotInstance = hotTableRef.value.hotInstance;
    emit("ready", hotInstance);
    updateHotHeight();

    if (wrapperRef.value) {
      resizeObserver = new ResizeObserver(() => {
        updateHotHeight();
      });
      resizeObserver.observe(wrapperRef.value);
    }
  }
});

watch(
  () => hotTableRef.value?.hotInstance,
  (instance) => {
    if (instance) {
      hotInstance = instance;
      emit("ready", instance);
      updateHotHeight();

      if (wrapperRef.value && !resizeObserver) {
        resizeObserver = new ResizeObserver(() => {
          updateHotHeight();
        });
        resizeObserver.observe(wrapperRef.value);
      }
    }
  },
);

onBeforeUnmount(() => {
  if (resizeObserver) {
    resizeObserver.disconnect();
  }
  hotInstance = null;
});

defineExpose({
  hotInstance: computed(() => hotTableRef.value?.hotInstance),
});
</script>

<style scoped>
.table-content-wrapper {
  height: 100%;
  width: 100%;
  overflow: hidden;
}
</style>
