import numpy as np
import pandas as pd
from pymoo.core.problem import Problem
from pymoo.algorithms.soo.nonconvex.ga import GA
from pymoo.operators.crossover.sbx import SBX
from pymoo.operators.mutation.pm import PM
from pymoo.operators.sampling.rnd import FloatRandomSampling
from pymoo.optimize import minimize
from app.types.model_request import ModelConfig
from app.services.RegressionService import RegressionService
from pymoo.core.callback import Callback
from typing import Dict, Any

class CustomCallback(Callback):
    def __init__(self, criterion: float, model_config: ModelConfig, model: Dict[str, Any], report: None, db_update: None, task_id: str, generations: int):
        super().__init__()
        self.codes = []
        self.criterion = criterion
        self.data = pd.DataFrame([])
        self.model_config = model_config
        self.model = model
        self.report = report
        self.db_update = db_update
        self.task_id = task_id
        self.generations = generations

    def notify(self, algorithm):
        current_data = np.concatenate([algorithm.pop.get("F"), algorithm.pop.get("X")], axis=1)
        codes = [ "-".join(i.tolist()) for i in current_data.round(2).astype(str) ]
        current_data = pd.DataFrame(current_data, index=codes)
        current_data = current_data[current_data.iloc[:, 0] < self.criterion]
        if not current_data.empty:
            new_codes = list(set(current_data.index).difference(self.codes))
            if len(new_codes) > 0:
                self.codes.extend(new_codes)
                current_data = current_data.loc[new_codes].iloc[:, 1:]
                current_data.columns = self.model['feature_names']
                scaler = self.model['scaler']
                x = scaler.transform(current_data)
                model = self.model['model']
                preds = model.predict(x)
                preds = pd.Series(preds).to_frame(self.model['y_train_name']).round(2)
                current_data.reset_index(drop=True, inplace=True)
                current_data = pd.concat([preds, current_data], axis=1).round(2)
                self.data = pd.concat([self.data, current_data], axis=0)
                if self.report is not None:
                    self.report(current_data.round(2).astype(str).to_dict(orient="records"), f"{(algorithm.n_gen/self.generations)*100:.2f}", "running", "搜索中")
                if self.db_update is not None:
                    self.db_update(self.task_id, result=self.data.to_dict(orient="records"))

class CustomBoundsProblem(Problem):
    def __init__(self, xl: np.array, xu: np.array, model, target_value: float, feature_names):
        super().__init__(n_var=xl.shape[0], n_obj=1, n_constr=0, xl=xl, xu=xu)
        self.xl = xl
        self.xu = xu
        self.target_value = target_value
        self.model = model
        self.feature_names = feature_names

    def _evaluate(self, x, out, *args, **kwargs):
        model = self.model['model']
        scaler = self.model['scaler']
        x = pd.DataFrame(x, columns=self.feature_names)
        x = scaler.transform(x)
        y = model.predict(x)
        out["F"] = np.abs(y - self.target_value)

class GeneticAlgorithm(object):

    def __init__(self, 
        population_size: int, 
        generations: int, 
        crossover: float, 
        mutation: float, 
        max_iter: int, 
        max_time: int,
        criterion: float
    ):
        self.population_size = population_size
        self.generations = generations
        self.crossover = crossover
        self.mutation = mutation
        self.max_iter = max_iter
        self.max_time = max_time
        self.criterion = criterion
    def fit(self, 
        xl: np.array, 
        xu: np.array, 
        model_config: ModelConfig, 
        model: Dict[str, Any], 
        target_value: float, 
        feature_names, 
        report: None, update: None, task_id: str
    ) -> None:
        self.problem = CustomBoundsProblem(xl, xu, model, target_value, feature_names)
        self.ga = GA(
            pop_size=self.population_size,                 # 种群大小
            sampling=FloatRandomSampling(),  # 随机采样初始种群
            crossover=SBX(prob=self.crossover, eta=15), # 模拟二进制交叉
            mutation=PM(eta=self.mutation),          # 多项式变异
            eliminate_duplicates=True     # 避免重复个体
        )

        minimize(
            self.problem, 
            self.ga, 
            ('n_gen', self.generations), 
            ('time_limit', self.max_time), 
            ('n_eval', self.max_iter), 
            verbose=True,
            callback=CustomCallback(target_value*self.criterion, model_config, model, report, update, task_id, self.generations)
        )
