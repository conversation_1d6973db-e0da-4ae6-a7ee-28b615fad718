import { type Ref, nextTick } from "vue";
import { debounce } from "lodash-es";

/**
 * 表格尺寸自适应组合式函数
 * 
 * 该组件整合了dataImandEx中的表格缩放优化逻辑，包括：
 * 1. 使用ResizeObserver监听容器尺寸变化
 * 2. 使用requestAnimationFrame优化渲染性能
 * 3. 实现节流处理，避免频繁刷新
 * 4. 支持暂停/恢复resize监听（用于侧边栏动画等场景）
 * 5. 优化了资源清理和内存管理
 * 
 * 使用方式：
 * 1. 在组件中引入 useTableResize
 * 2. 传入表格内容引用和autoResize参数
 * 3. 在onMounted中调用startObserving
 * 4. 在onBeforeUnmount中调用stopObserving
 * 
 * @param tableContentRef 表格内容引用
 * @param autoResize 是否自动调整尺寸
 * @returns 尺寸调整相关方法
 */
export function useTableResize(
  containerRef: Ref<HTMLElement | null>,
  getHotInstance: () => any | undefined,
  autoResize: boolean = true,
) {
  let resizeObserver: ResizeObserver | null = null;
  let isResizePaused = false;
  let rafId: number | null = null;

  const updateDimensions = () => {
    if (rafId) {
      cancelAnimationFrame(rafId);
    }

    rafId = requestAnimationFrame(() => {
      const hotInstance = getHotInstance();
      if (!hotInstance || isResizePaused || hotInstance.isDestroyed) {
        return;
      }
      hotInstance.refreshDimensions();
    });
  };

  const handleResize = debounce(() => {
    if (!autoResize || isResizePaused) return;
    updateDimensions();
  }, 150);

  const startObserving = () => {
    if (!autoResize || !containerRef.value) return;

    resizeObserver = new ResizeObserver(handleResize);
    resizeObserver.observe(containerRef.value);
    
    window.addEventListener('resize', handleResize, { passive: true });
    
    updateDimensions();
  };

  const stopObserving = () => {
    if (rafId) {
      cancelAnimationFrame(rafId);
      rafId = null;
    }
    
    resizeObserver?.disconnect();
    resizeObserver = null;
    
    window.removeEventListener('resize', handleResize);
  };

  const pauseResize = () => {
    isResizePaused = true;
  };

  const resumeResize = () => {
    isResizePaused = false;
    updateDimensions();
  };

  return {
    updateDimensions,
    startObserving,
    stopObserving,
    pauseResize,
    resumeResize,
  };
}
