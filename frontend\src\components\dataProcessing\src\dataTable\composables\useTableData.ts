import { ElMessage } from "element-plus";
import * as XLSX from "xlsx";

export function useTableData() {
  // 优化的 Excel 文件解析 - 支持懒加载
  const parseExcelFile = async (
    file: File,
    options: {
      lazyLoad?: boolean; // 是否懒加载工作表数据
      maxRows?: number; // 最大行数限制
      maxCols?: number; // 最大列数限制
    } = {}
  ): Promise<{
    sheets: string[];
    data: Record<string, any[][]>;
    workbook?: XLSX.WorkBook; // 返回workbook用于懒加载
  }> => {
    const { lazyLoad = false, maxRows = 10000, maxCols = 100 } = options;

    return new Promise((resolve, reject) => {
      const reader = new FileReader();

      reader.onload = (e) => {
        try {
          const data = new Uint8Array(e.target?.result as ArrayBuffer);
          const workbook = XLSX.read(data, {
            type: "array",
            // 优化读取选项
            cellDates: false, // 禁用日期解析以提升性能
            cellNF: false, // 禁用数字格式解析
            cellStyles: false, // 禁用样式解析
            sheetStubs: false, // 禁用空单元格占位符
          });

          const sheets = workbook.SheetNames;
          const sheetData: Record<string, any[][]> = {};

          if (lazyLoad) {
            // 懒加载模式：只返回工作表名称，不解析数据
            resolve({ sheets, data: sheetData, workbook });
          } else {
            // 立即加载模式：解析所有工作表数据
            sheets.forEach((sheetName) => {
              const worksheet = workbook.Sheets[sheetName];
              const jsonData = XLSX.utils.sheet_to_json(worksheet, {
                header: 1,
                raw: false,
                defval: null,
                range: maxRows > 0 ? `A1:${XLSX.utils.encode_col(maxCols - 1)}${maxRows}` : undefined,
              });

              // 过滤空行并限制数据大小
              const filteredData = (jsonData as any[][])
                .filter((row) =>
                  row.some(
                    (cell) => cell !== null && cell !== undefined && cell !== "",
                  )
                )
                .slice(0, maxRows); // 确保不超过最大行数

              sheetData[sheetName] = filteredData;
            });

            resolve({ sheets, data: sheetData });
          }
        } catch (error) {
          reject(error);
        }
      };

      reader.onerror = () => reject(new Error("文件读取失败"));
      reader.readAsArrayBuffer(file);
    });
  };

  // 懒加载单个工作表数据
  const parseSheetData = (
    workbook: XLSX.WorkBook,
    sheetName: string,
    options: {
      maxRows?: number;
      maxCols?: number;
    } = {}
  ): any[][] => {
    const { maxRows = 10000, maxCols = 100 } = options;

    try {
      const worksheet = workbook.Sheets[sheetName];
      if (!worksheet) {
        console.warn(`工作表 "${sheetName}" 不存在`);
        return [];
      }

      const jsonData = XLSX.utils.sheet_to_json(worksheet, {
        header: 1,
        raw: false,
        defval: null,
        range: maxRows > 0 ? `A1:${XLSX.utils.encode_col(maxCols - 1)}${maxRows}` : undefined,
      });

      // 过滤空行并限制数据大小
      return (jsonData as any[][])
        .filter((row) =>
          row.some(
            (cell) => cell !== null && cell !== undefined && cell !== "",
          )
        )
        .slice(0, maxRows);
    } catch (error) {
      console.error(`解析工作表 "${sheetName}" 失败:`, error);
      return [];
    }
  };

  // 验证数据
  const validateData = (data: any[][]): boolean => {
    if (!Array.isArray(data) || data.length === 0) {
      ElMessage.warning("数据为空");
      return false;
    }

    // 检查是否所有行的列数一致
    const columnCount = data[0]?.length;
    const isConsistent = data.every((row) => row.length === columnCount);

    if (!isConsistent) {
      ElMessage.warning("数据格式不一致");
      return false;
    }

    return true;
  };

  // 转换数据格式
  const transformData = (
    data: any[][],
    hasHeader: boolean = true,
  ): { headers: string[]; content: any[][] } => {
    if (!data.length) {
      return { headers: [], content: [] };
    }

    if (hasHeader) {
      const headers = data[0].map((h, i) => h?.toString() || `列${i + 1}`);
      const content = data.slice(1);
      return { headers, content };
    } else {
      const headers = data[0].map((_, i) => `列${i + 1}`);
      return { headers, content: data };
    }
  };

  // 清洗数据
  const cleanData = (
    data: any[][],
    options?: {
      removeEmpty?: boolean;
      trimStrings?: boolean;
      convertNumbers?: boolean;
    },
  ) => {
    const {
      removeEmpty = true,
      trimStrings = true,
      convertNumbers = true,
    } = options || {};

    return data
      .filter((row) => {
        if (!removeEmpty) return true;
        return row.some(
          (cell) => cell !== null && cell !== undefined && cell !== "",
        );
      })
      .map((row) =>
        row.map((cell) => {
          if (cell === null || cell === undefined) return null;

          let value = cell;

          if (trimStrings && typeof value === "string") {
            value = value.trim();
          }

          if (
            convertNumbers &&
            typeof value === "string" &&
            !isNaN(Number(value))
          ) {
            value = Number(value);
          }

          return value;
        }),
      );
  };

  return {
    parseExcelFile,
    parseSheetData,
    validateData,
    transformData,
    cleanData,
  };
}
