/**
 * 模型参数加载工具
 * 用于加载和管理各种模型的预设参数配置
 */

// 参数配置接口定义
export interface ParamConfig {
  value: any;
  type: 'number' | 'boolean' | 'select' | 'string';
  description: string;
  displayName: string;
  min?: number;
  max?: number;
  step?: number;
  nullable?: boolean;
  options?: Array<{value: any; label: string}>;
}

export interface ModelParamConfig {
  name: string;
  displayName: string;
  description: string;
  defaultParams: Record<string, ParamConfig>;
  evaluation: Record<string, any>;
  tips: string[];
  introduction?: {
    detailedDescription?: string;
    usageTips?: string[];
    scenarios?: string;
    mainParams?: Array<{
      name: string;
      description: string;
    }>;
  };
}

// 支持的模型类型
export const SUPPORTED_MODELS = [
  'LinearRegression',
  'Ridge', 
  'Lasso',
  'ElasticNet',
  'GeneticAlgorithm' // 添加 GeneticAlgorithm
] as const;

export type SupportedModel = typeof SUPPORTED_MODELS[number];

/**
 * 模型参数加载器类
 */
class ModelParamsLoader {
  private cache: Map<string, ModelParamConfig> = new Map();

  /**
   * 加载指定模型的参数配置
   * @param modelName 模型名称
   * @returns 参数配置对象
   */
  async loadModelParams(modelName: SupportedModel): Promise<ModelParamConfig> {
    // 检查缓存
    if (this.cache.has(modelName)) {
      return this.cache.get(modelName)!;
    }

    let configModule;
    try {
      // 尝试从 modelParams 目录加载
      configModule = await import(`@/config/modelParams/${modelName}.json`);
    } catch (error) {
      try {
        // 如果 modelParams 失败，尝试从 searchParams 目录加载
        configModule = await import(`@/config/searchParams/${modelName}.json`);
      } catch (err) {
        // 返回默认配置
        return this.getDefaultConfig(modelName);
      }
    }

    const config: ModelParamConfig = configModule.default || configModule;

    // 验证配置格式
    this.validateConfig(config);

    // 缓存配置
    this.cache.set(modelName, config);

    return config;
  }

  /**
   * 获取模型的默认参数值
   * @param modelName 模型名称
   * @returns 默认参数对象
   */
  async getDefaultParams(modelName: SupportedModel): Promise<Record<string, any>> {
    const config = await this.loadModelParams(modelName);
    const params: Record<string, any> = {};
    
    Object.entries(config.defaultParams).forEach(([key, paramConfig]) => {
      params[key] = paramConfig.value;
    });
    
    return params;
  }

  /**
   * 获取模型的评估配置
   * @param modelName 模型名称
   * @returns 评估配置对象
   */
  async getEvaluationConfig(modelName: SupportedModel): Promise<Record<string, any>> {
    const config = await this.loadModelParams(modelName);
    return config.evaluation;
  }

  /**
   * 验证参数值是否符合配置要求
   * @param modelName 模型名称
   * @param params 参数对象
   * @returns 验证结果和错误信息
   */
  async validateParams(
    modelName: SupportedModel, 
    params: Record<string, any>
  ): Promise<{valid: boolean; errors: string[]}> {
    const config = await this.loadModelParams(modelName);
    const errors: string[] = [];

    Object.entries(params).forEach(([key, value]) => {
      const paramConfig = config.defaultParams[key];
      if (!paramConfig) {
        errors.push(`未知参数: ${key}`);
        return;
      }

      // 类型验证
      if (!this.validateParamType(value, paramConfig)) {
        errors.push(`参数 ${paramConfig.displayName} 类型错误`);
      }

      // 范围验证
      if (paramConfig.type === 'number' && typeof value === 'number') {
        if (paramConfig.min !== undefined && value < paramConfig.min) {
          errors.push(`参数 ${paramConfig.displayName} 不能小于 ${paramConfig.min}`);
        }
        if (paramConfig.max !== undefined && value > paramConfig.max) {
          errors.push(`参数 ${paramConfig.displayName} 不能大于 ${paramConfig.max}`);
        }
      }

      // 选项验证
      if (paramConfig.type === 'select' && paramConfig.options) {
        const validValues = paramConfig.options.map(opt => opt.value);
        if (!validValues.includes(value)) {
          errors.push(`参数 ${paramConfig.displayName} 值无效`);
        }
      }
    });

    return {
      valid: errors.length === 0,
      errors
    };
  }

  /**
   * 格式化参数为后端API格式
   * @param modelName 模型名称
   * @param params 前端参数对象
   * @returns 后端API格式的参数
   */
  async formatParamsForAPI(
    modelName: SupportedModel,
    params: Record<string, any>
  ): Promise<Record<string, any>> {
    const config = await this.loadModelParams(modelName);
    const formattedParams: Record<string, any> = {};

    Object.entries(params).forEach(([key, value]) => {
      const paramConfig = config.defaultParams[key];
      if (paramConfig && value !== null && value !== undefined) {
        // 处理nullable参数
        if (paramConfig.nullable && value === null) {
          formattedParams[key] = null;
        } else {
          formattedParams[key] = value;
        }
      }
    });

    return formattedParams;
  }

  /**
   * 清除缓存
   */
  clearCache(): void {
    this.cache.clear();
  }

  /**
   * 验证配置格式
   */
  private validateConfig(config: any): void {
    if (!config.name || !config.displayName || !config.defaultParams) {
      throw new Error('Invalid model config format');
    }
  }

  /**
   * 验证参数类型
   */
  private validateParamType(value: any, paramConfig: ParamConfig): boolean {
    if (paramConfig.nullable && value === null) {
      return true;
    }

    switch (paramConfig.type) {
      case 'number':
        return typeof value === 'number' && !isNaN(value);
      case 'boolean':
        return typeof value === 'boolean';
      case 'string':
      case 'select':
        return typeof value === 'string';
      default:
        return true;
    }
  }

  /**
   * 获取默认配置（当加载失败时使用）
   */
  private getDefaultConfig(modelName: SupportedModel): ModelParamConfig {
    return {
      name: modelName,
      displayName: modelName,
      description: `${modelName} 模型`,
      defaultParams: {},
      evaluation: {
        splitDataset: { value: false },
        trainRatio: { value: 70 },
        randomState: { value: 42 },
        useModelValidation: { value: false },
        validationType: { value: 'k-fold' },
        kFolds: { value: 5 }
      },
      tips: []
    };
  }
}

// 创建单例实例
export const modelParamsLoader = new ModelParamsLoader();

// 导出便捷方法
export const loadModelParams = (modelName: SupportedModel) => 
  modelParamsLoader.loadModelParams(modelName);

export const getDefaultParams = (modelName: SupportedModel) => 
  modelParamsLoader.getDefaultParams(modelName);

export const validateModelParams = (modelName: SupportedModel, params: Record<string, any>) => 
  modelParamsLoader.validateParams(modelName, params);

export const formatParamsForAPI = (modelName: SupportedModel, params: Record<string, any>) => 
  modelParamsLoader.formatParamsForAPI(modelName, params);

export default modelParamsLoader;
