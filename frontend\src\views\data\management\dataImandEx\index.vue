<template>
  <div class="flex flex-col grow h-full">
    <!-- 将操作按钮移到表格外层容器 -->
    <div class="toolbar-container">
      <div class="flex items-center gap-2">
        <transition-group name="fade" tag="div" class="flex items-center gap-2">
          <!-- 导出文件 -->
          <div key="export">
            <el-tooltip content="导出文件" placement="bottom">
              <el-button circle :disabled="!hasData" @click="handleExport" class="menu-button">
                <DownloadIcon class="menu-icon normal" />
                <DownloadSelectedIcon class="menu-icon hover" />
              </el-button>
            </el-tooltip>
          </div>
          <!-- 分析数据集 -->
          <div key="analyze">
            <el-tooltip content="分析数据集" placement="bottom">
              <el-button circle :disabled="!hasData" @click="analyzeData" class="menu-button">
                <AnalyzeIcon class="menu-icon normal" />
                <AnalyzeSelectedIcon class="menu-icon hover" />
              </el-button>
            </el-tooltip>
          </div>
          <!-- 统计分析 -->
          <div key="visualize">
            <el-tooltip content="统计分析" placement="bottom">
              <el-button circle :disabled="!hasData" @click="toOpenVisualize" class="menu-button">
                <StatIcon class="menu-icon normal" />
                <StatSelectedIcon class="menu-icon hover" />
              </el-button>
            </el-tooltip>
          </div>
          <!-- 高级组件: 模型选择器 -->
          <div :key="'modelSelector'">
            <ModelSelector :disabled="!hasData" @buildModel="handleBuildModel" />
          </div>
          <!-- 高级组件: 数据预处理 -->
          <div :key="'dataPreprocess'">
            <DataPreprocessing :disabled="!hasData" @openDialog="openPreprocessDialog" />
          </div>
        </transition-group>
      </div>
      <!-- 保存和关闭按钮 -->
      <div class="file-actions">
        <button class="action-button save-button" :disabled="!isFileModified" :class="{ disabled: !isFileModified }"
          @click="handleSave">
          保存
        </button>
        <button class="action-button close-button" @click="handleClose">
          关闭
        </button>
      </div>
    </div>

    <!-- 数据分析模块 -->
    <DataAnalyze :table-instance="tableInstance" :visible="isAnalyzeVisible" />

    <!-- 可视化模块 -->
    <DataVisualize :table-instance="tableInstance" :visible="isVisualizeVisible" />

    <div id="data-table-container" class="flex-1 relative table-outer-container" ref="containerRef">
      <DataTable ref="dataTableRef" :data="tableData" :columns="tableColumns" :loading="loading"
        :bottom-offset="sheetTabBarHeight" empty-text="请上传数据文件，并选择正确工作表" :settings="customSettings"
        @ready="handleTableReady" @change="handleDataChange" />
      <!-- 局部 loading -->
      <div v-if="localLoading" class="table-loading-mask">
        <div class="loading-spinner">
          <el-icon class="is-loading">
            <Loading />
          </el-icon>
          <span>加载中...</span>
        </div>
      </div>
    </div>

    <!-- 底部工作表标签栏 -->
    <div v-if="sheetNames.length > 0" class="sheet-tab-bar" ref="sheetTabBarRef">
      <el-dropdown v-for="name in sheetNames" :key="name" trigger="contextmenu"
        @command="(cmd) => handleSheetCommand(name, cmd)">
        <div :class="['sheet-tab', { active: name === selectedSheet }]" @click="handleSheetChange(name)">
          {{ name }}
        </div>
        <template #dropdown>
          <el-dropdown-menu>
            <el-dropdown-item command="delete">删除工作表</el-dropdown-item>
          </el-dropdown-menu>
        </template>
      </el-dropdown>
      <!-- 新建sheet按钮 -->
      <div class="sheet-tab sheet-add" @click="addNewSheet">
        <Plus style="width: 18px; color: #005dff" />
      </div>
    </div>

    <!-- 对话框组件 -->
    <ModelDialog dialogType="linear" :model-type="selectedModelType" @confirm="handleModelConfirm" />

    <ModelDialog dialogType="ml" :model-type="selectedModelType" @confirm="handleMLModelConfirm" />

    <DataPreprocessDialog
      @confirm="handlePreprocessingConfirm"
      @start-outlier-detection="handleOutlierDetection"
      :detection-result="detectionResult"
      @delete-outliers="handleDeleteOutliers"
    />
  </div>
</template>

<script lang="ts" setup>
import {
  ref,
  computed,
  watch,
  watchEffect,
  onMounted,
  onUnmounted,
  onBeforeUnmount,
  nextTick,
} from "vue";
import { useRouter, useRoute } from "vue-router";
import { ElMessage, ElMessageBox, ElLoading } from "element-plus";
import * as XLSX from "xlsx";
import { exportMultiSheet, exportSingleSheet } from "@/utils/exportUtils";
import {
  Download,
  Histogram,
  PieChart,
  Plus,
  Loading,
} from "@element-plus/icons-vue";
import DownloadIcon from "@/assets/svg/download.svg?component";
import DownloadSelectedIcon from "@/assets/svg/download_selected.svg?component";
import AnalyzeIcon from "@/assets/svg/analyze.svg?component";
import AnalyzeSelectedIcon from "@/assets/svg/analyze_selected.svg?component";
import StatIcon from "@/assets/svg/stat.svg?component";
import StatSelectedIcon from "@/assets/svg/stat_selected.svg?component";

// 组件导入
import { DataTable } from "@/components/dataProcessing/src/dataTable";
import type {
  TableInstance,
  TableColumn,
} from "@/components/dataProcessing/src/dataTable";
import ModelSelector from "./menu/modelSelector.vue";
import DataPreprocessing from "./menu/dataPreprocessing.vue";
import DataAnalyze from "@/components/dataProcessing/src/dataAnalyze/dataAnalyze.vue";
import DataVisualize from "@/components/dataProcessing/src/dataVisualize/dataVisualize.vue";
import { ModelDialog } from "@/components/modelManagement";
import DataPreprocessDialog from "@/components/dataProcessing/src/dataPreprocessDialog/index.vue";

// API 导入
import { buildLinearModel } from "@/api/models/model";
import { buildMLModel, buildTreeModel } from "@/api/models/model";
import { reqDataFill, reqOutlierDetection } from "@/api/models/dataPreprocess";
import { getSocket } from "@/utils/socket";
import type { ModelConfig } from "@/types/models";
import type { LinearModelResponse } from "@/api/models/model";
import type { MLModelResponse } from "@/api/models/model";
import type { ApiResponse } from "@/types/request";
import type { PreprocessConfig } from "@/types/preprocess";

// Store 导入
import { useTableDataStore } from "@/store/modules/tableData";
import { useDialogStore } from "@/store/modules/dialog";
import { useWorkspaceStoreHook } from "@/store/modules/workspace";
import { useMultiTagsStoreHook } from "@/store/modules/multiTags";
import { usePermissionStoreHook } from "@/store/modules/permission";

// 性能监控导入
import { tablePerformance, memoryMonitor } from "@/utils/performance";

// 状态
const detectionResult = ref(null);

// 初始化
const dialogStore = useDialogStore();
const tableDataStore = useTableDataStore();
const workspaceStore = useWorkspaceStoreHook();
const router = useRouter();
const route = useRoute();

// 添加局部 loading 状态
const localLoading = ref(false);

// 添加加载控制变量
const currentLoadingController = ref<AbortController | null>(null);
const loadingCanceled = ref(false);
const pendingFilePath = ref<string | null>(null);

// 当前正在加载的文件路径
const currentLoadingFilePath = ref<string | null>(null);

// 添加工作表切换控制变量
const currentSheetController = ref<AbortController | null>(null);
const sheetLoadingMap = ref<Record<string, boolean>>({});

// Refs
const dataTableRef = ref<InstanceType<typeof DataTable>>();
const tableInstance = ref<TableInstance>();
const teleportKey = ref(0);
const containerRef = ref<HTMLElement | null>(null);

// 状态
const loading = ref(false);
const isAnalyzeVisible = ref(false);
const isVisualizeVisible = ref(false);
const tableData = ref<any[][]>([]);
const tableColumns = ref<TableColumn[]>([]);
const sheetNames = ref<string[]>([]);
const selectedSheet = ref<string>("");
const sheetsData = ref<Record<string, { headers: string[]; content: any[][] }>>(
  {},
);
const currentModelType = ref<string>("");
const currentMLModelType = ref<string>("");
const selectedModelType = ref<string>("");

// 表格准备状态
const isTableReady = ref(false);

// 性能优化：数据缓存
const processedDataCache = new Map<
  string,
  { columns: TableColumn[]; data: any[][] }
>();
const lastProcessedSheet = ref<string>("");
// 缓存workbook用于懒加载
const cachedWorkbook = ref<XLSX.WorkBook | null>(null);

// 自定义 Handsontable 设置
const customSettings = {
  columnSorting: true,
  rowHeights: 30,
  manualRowMove: true,
  manualColumnMove: true,
  readOnly: false,
  allowInsertRow: true,
  allowInsertColumn: true,
  contextMenu: true,
  renderAllRows: false, // 启用虚拟滚动
  viewportRowRenderingOffset: 10, // 视口外渲染行数
  viewportColumnRenderingOffset: 10, // 视口外渲染列数
  afterChange: (changes, source) => {
    if (!changes || source === "loadData") return; // 忽略初始加载
    handleTableModification();
  },
  afterCreateRow: () => {
    handleTableModification();
  },
  afterCreateCol: () => {
    handleTableModification();
  },
  afterRemoveRow: () => {
    handleTableModification();
  },
  afterRemoveCol: () => {
    handleTableModification();
  },
  afterColumnMove: () => {
    handleTableModification();
  },
  afterRowMove: () => {
    handleTableModification();
  },
  beforePaste: (data) => {
    // 在粘贴前标记即将发生变化
    nextTick(() => {
      handleTableModification();
    });
    return data;
  },
};

// 计算属性
const hasData = computed(() => tableData.value.length > 0);
const filePath = computed(() =>
  decodeURIComponent(route.params.filePath || ""),
);
const isFileModified = computed(() =>
  filePath.value ? workspaceStore.isDataModified(filePath.value) : false,
);

// 新增：用于存储原始数据的引用
const originalTableState = ref({
  data: [],
  columns: [],
});

// 新增：处理表格修改的函数
const handleTableModification = () => {
  if (!tableInstance.value || !filePath.value) return;

  const currentData = tableInstance.value.getData();
  const currentColumns = tableInstance.value.getColumns();

  // 检查数据是否真的发生了变化
  const hasDataChanged =
    JSON.stringify(currentData) !==
    JSON.stringify(originalTableState.value.data);
  const hasColumnsChanged =
    JSON.stringify(currentColumns) !==
    JSON.stringify(originalTableState.value.columns);

  if (hasDataChanged || hasColumnsChanged) {
    workspaceStore.markDataAsModified(filePath.value);
    console.log("Table modified, enabling save button");
  }
};

// 判断当前模型是否为树模型
const isCurrentModelTreeModel = computed(() => {
  if (!currentMLModelType.value) return false;

  const treeModels = [
    "DecisionTreeRegressor",
    "RandomForestRegressor",
    "GradientBoostingRegressor",
    "XGBoost",
  ];

  return treeModels.includes(currentMLModelType.value);
});

// 底部工作表标签栏
const sheetTabBarRef = ref<HTMLElement | null>(null);
const sheetTabBarHeight = ref(0); // 默认高度

// 计算属性 - 优化表格高度计算
const dataTableStyle = computed(() => {
  return { height: "100%" };
});

// Listen for Excel file selection from workspace
onMounted(() => {
  console.log("DataImandEx mounted");

  // 确保初始化表格准备状态
  isTableReady.value = false;

  // 先移除可能存在的其他监听器，确保不会触发重复弹窗
  window.ipcRenderer.removeAllListeners("menu-triggered-open-file");
  console.log(
    "DataImandEx: Removed existing menu-triggered-open-file listeners",
  );

  // 监听从主进程菜单触发的打开文件事件
  window.ipcRenderer.on("menu-triggered-open-file", handleOpenFileAsSingle);
  console.log("DataImandEx: Registered menu-triggered-open-file listener");

  // 监听开始加载 Excel 文件事件
  window.addEventListener("start-loading-excel", handleStartLoadingExcel);
  // 监听导出当前文件事件
  window.addEventListener("export-current-file", handleExportCurrentFile);

  // 检查workspace store中是否有当前文件路径
  const currentFilePath = workspaceStore.getCurrentFilePath;
  if (currentFilePath && /\.(xlsx|xls|csv)$/i.test(currentFilePath)) {
    console.log(
      "Found existing Excel file in workspace store:",
      currentFilePath,
    );
    // 延迟处理，确保组件完全挂载
    setTimeout(() => {
      handleExcelFileFromWorkspace(currentFilePath);
    }, 200);
  }

  // 添加路由导航守卫，处理文件切换
  const removeRouterGuard = router.beforeEach((to, from, next) => {
    // 如果导航到新的Excel文件
    if (
      to.path.startsWith("/dataManagement/imandex/") &&
      to.params.filePath &&
      to.params.filePath !== from.params.filePath
    ) {
      const newFilePath = decodeURIComponent(to.params.filePath as string);
      const currentPath = workspaceStore.getCurrentFilePath;

      // 如果是不同的文件，则取消当前加载并准备加载新文件
      if (newFilePath !== currentPath) {
        console.log("Router navigation to new file:", newFilePath);
        cancelCurrentLoading();
      } else {
        console.log(
          "Router navigation to same file, skipping reload:",
          newFilePath,
        );
      }
    }
    next();
  });

  // 在组件卸载时移除路由守卫
  onBeforeUnmount(() => {
    // 移除路由守卫
    if (typeof removeRouterGuard === "function") {
      removeRouterGuard();
    }

    // 清理事件监听
    window.ipcRenderer.off("menu-triggered-open-file", handleOpenFileAsSingle);
    window.removeEventListener("start-loading-excel", handleStartLoadingExcel);
    window.removeEventListener("export-current-file", handleExportCurrentFile);

    // 确保取消任何正在进行的加载
    cancelCurrentLoading();

    // 清空数据
    tableData.value = [];
    tableColumns.value = [];
    sheetNames.value = [];
    selectedSheet.value = "";
    sheetsData.value = {};

    // 清理缓存以释放内存
    processedDataCache.clear();
    lastProcessedSheet.value = "";
  });

  nextTick(() => {
    if (sheetTabBarRef.value) {
      sheetTabBarHeight.value = sheetTabBarRef.value.offsetHeight;
    } else {
      sheetTabBarHeight.value = 0;
    }
  });
});

// 取消当前正在进行的加载操作
const cancelCurrentLoading = () => {
  if (currentLoadingController.value) {
    console.log("Canceling current loading operation");
    loadingCanceled.value = true;
    currentLoadingController.value.abort();
    currentLoadingController.value = null;

    // 如果有加载中的指示器，隐藏它
    if (localLoading.value) {
      localLoading.value = false;
    }
  }
};

// 处理从主菜单打开单个文件
const handleOpenFileAsSingle = async () => {
  const result = await window.ipcRenderer.invoke("dialog:showOpenDialog", {
    properties: ["openFile"],
    filters: [
      { name: "Excel and CSV files", extensions: ["xlsx", "xls", "csv"] },
    ],
  });

  if (!result.canceled && result.filePaths.length > 0) {
    const filePath = result.filePaths[0];

    // 设置为单文件模式
    workspaceStore.setSingleFileMode(filePath);

    // 直接加载文件
    await handleExcelFileFromWorkspace(filePath);
  }
};

// 处理开始加载 Excel 文件事件
const handleStartLoadingExcel = async (event: CustomEvent) => {
  const filePath = event.detail;
  if (!filePath) return;
  await handleExcelFileFromWorkspace(filePath);
};

// 处理导出当前文件事件
const handleExportCurrentFile = () => {
  console.log("Export current file triggered");
  handleExport();
};

// Handle Excel file from workspace - 优化版本
const handleExcelFileFromWorkspace = async (
  filePath: string,
  retryCount = 0,
) => {
  console.log(
    "Received Excel file from workspace:",
    filePath,
    "retry:",
    retryCount,
  );

  // 检查是否是当前已加载的文件路径
  if (currentLoadingFilePath.value === filePath) {
    console.log("File already being loaded, skipping:", filePath);
    return;
  }

  // 检查是否是当前已显示的文件（且已加载完成）
  if (
    !loading.value &&
    !localLoading.value &&
    currentLoadingFilePath.value === null &&
    workspaceStore.getCurrentFilePath === filePath &&
    tableData.value.length > 0
  ) {
    console.log("File already loaded and displayed, skipping:", filePath);
    return;
  }

  cancelCurrentLoading();

  currentLoadingController.value = new AbortController();
  const signal = currentLoadingController.value.signal;
  currentLoadingFilePath.value = filePath;
  loadingCanceled.value = false;

  if (!isTableReady.value) {
    if (retryCount < 5) {
      console.log("Table not ready, retrying...", retryCount);
      setTimeout(() => {
        handleExcelFileFromWorkspace(filePath, retryCount + 1);
      }, 200);
      return;
    } else {
      console.error("Max retry attempts reached, table still not ready");
      ElMessage.error("表格组件未准备就绪，请稍后重试");
      return;
    }
  }

  try {
    // 避免重复显示加载动画
    if (!localLoading.value) {
      localLoading.value = true;
    }

    if (!workspaceStore.hasWorkspace) {
      workspaceStore.setSingleFileMode(filePath);
    } else {
      workspaceStore.setCurrentFile(filePath);
    }

    const fileName = filePath.split(/[/\\]/).pop() || filePath;
    document.title = `${fileName} - ML Desktop`;

    // 清空数据前暂停渲染
    if (tableInstance.value?.hotInstance) {
      tableInstance.value.hotInstance.suspendRender();
    }

    tableData.value = [];
    tableColumns.value = [];

    // 使用微任务处理文件加载
    await Promise.resolve();

    try {
      if (signal.aborted) return;

      const fileBuffer = await window.ipcRenderer.invoke(
        "fs:readFileBuffer",
        filePath,
      );

      if (signal.aborted) return;

      if (fileBuffer) {
        workspaceStore.markDataAsSaved(filePath);
        await handleUploadDataFile(fileBuffer, signal);
      }
    } catch (error) {
      if (!loadingCanceled.value) {
        console.error("Error loading Excel file from workspace:", error);
        ElMessage.error("加载Excel文件失败");
      }
    } finally {
      if (currentLoadingFilePath.value === filePath) {
        localLoading.value = false;
        currentLoadingController.value = null;
        currentLoadingFilePath.value = null;

        // 恢复渲染
        if (tableInstance.value?.hotInstance) {
          tableInstance.value.hotInstance.resumeRender();
        }
      }
    }
  } catch (error) {
    console.error("Error in synchronous part of loading Excel file:", error);
    ElMessage.error("准备加载Excel文件失败");
    localLoading.value = false;
    currentLoadingController.value = null;
    currentLoadingFilePath.value = null;
  }
};

// 处理文件上传 - 优化版本，支持取消
const handleUploadDataFile = async (
  dataBinary: ArrayBuffer,
  signal?: AbortSignal,
) => {
  // 避免重复显示加载动画
  if (!loading.value) {
    loading.value = true;
  }

  // 性能监控
  memoryMonitor.logMemoryUsage("Before File Upload");

  try {
    // 清空旧数据
    tableData.value = [];
    tableColumns.value = [];
    sheetNames.value = [];
    selectedSheet.value = "";
    sheetsData.value = {};
    cachedWorkbook.value = null; // 清空缓存的workbook

    // 强制更新表格
    if (dataTableRef.value) {
      dataTableRef.value.forceUpdate();
    }

    await nextTick();

    // 检查是否已取消
    if (signal?.aborted) {
      console.log("File upload canceled before parsing");
      return;
    }

    // 使用性能监控包装文件解析
    await tablePerformance.measureFileParse(async () => {
      // 优化的Excel读取配置
      const workbook = XLSX.read(dataBinary, {
        type: "array",
        // 性能优化选项
        cellDates: false, // 禁用日期解析
        cellNF: false, // 禁用数字格式解析
        cellStyles: false, // 禁用样式解析
        sheetStubs: false, // 禁用空单元格占位符
      });

      // 检查是否已取消
      if (signal?.aborted) {
        console.log("File upload canceled after parsing");
        return;
      }

      // 缓存workbook用于懒加载
      cachedWorkbook.value = workbook;
      sheetNames.value = workbook.SheetNames;

      if (sheetNames.value.length === 0) {
        ElMessage.error("文件没有工作表");
        return;
      }

      // 懒加载模式：只预处理第一个工作表，其他工作表按需加载
      const firstSheetName = sheetNames.value[0];
      const firstSheetData = parseSheetDataOptimized(workbook, firstSheetName);

      // 检查是否已取消
      if (signal?.aborted) {
        console.log("File upload canceled before sheet processing");
        return;
      }

      if (firstSheetData) {
        sheetsData.value[firstSheetName] = firstSheetData;

        // 保存原始数据状态
        originalTableState.value = {
          data: [...firstSheetData.content],
          columns: firstSheetData.headers.map((header, index) => ({
            data: index.toString(),
            title: header,
            type: "text",
            width: 120,
          })),
        };
      }

      // 默认选择第一个工作表
      selectedSheet.value = firstSheetName;
      await handleSheetChange(firstSheetName);
    });
  } catch (error) {
    // 只有在未被取消的情况下才显示错误
    if (!signal?.aborted) {
      ElMessage.error("文件解析失败：" + error.message);
      console.error(error);
    }
  } finally {
    // 只有在未被取消的情况下才更新状态
    if (!signal?.aborted) {
      loading.value = false;
      memoryMonitor.logMemoryUsage("After File Upload");
    }
  }
};

// 优化的工作表数据解析函数
const parseSheetDataOptimized = (
  workbook: XLSX.WorkBook,
  sheetName: string,
) => {
  try {
    const worksheet = workbook.Sheets[sheetName];
    if (!worksheet) return null;

    // 限制数据大小以提升性能
    const maxRows = 10000;
    const maxCols = 100;

    const jsonData = XLSX.utils.sheet_to_json(worksheet, {
      header: 1,
      raw: false,
      defval: null,
      range: `A1:${XLSX.utils.encode_col(maxCols - 1)}${maxRows}`,
    }) as any[][];

    // 清洗数据 - 移除完全空的行
    const cleanedData = jsonData.filter((row) =>
      row.some((cell) => cell !== null && cell !== undefined && cell !== ""),
    );

    if (cleanedData.length > 0) {
      // 找到实际有数据的最大列数
      let maxCols = 0;
      cleanedData.forEach((row) => {
        for (let i = row.length - 1; i >= 0; i--) {
          if (row[i] !== null && row[i] !== undefined && row[i] !== "") {
            maxCols = Math.max(maxCols, i + 1);
            break;
          }
        }
      });

      // 如果没有找到有效数据，至少保留一列
      if (maxCols === 0) {
        maxCols = 1;
      }

      // 截取有效的列数据
      const trimmedData = cleanedData.map((row) => row.slice(0, maxCols));

      // 生成列标题，只为有效列生成
      const headers = trimmedData[0]
        .slice(0, maxCols)
        .map((h, i) => (h && h.toString().trim()) || `列${i + 1}`);

      // 内容数据（去除标题行）
      const content = trimmedData.slice(1);

      return { headers, content };
    }

    return null;
  } catch (error) {
    console.error(`解析工作表 "${sheetName}" 失败:`, error);
    return null;
  }
};

// 优化工作表切换性能 - 支持懒加载和取消
const handleSheetChange = async (sheetName: string) => {
  if (!sheetName) {
    console.warn("No sheet name provided");
    return;
  }

  // 取消当前正在进行的工作表加载
  if (currentSheetController.value) {
    currentSheetController.value.abort();
    currentSheetController.value = null;
  }

  // 创建新的中止控制器
  currentSheetController.value = new AbortController();
  const signal = currentSheetController.value.signal;

  // 记录当前工作表
  selectedSheet.value = sheetName;

  // 使用微任务确保UI更新优先
  await Promise.resolve();

  try {
    // 检查是否已被取消
    if (signal.aborted) return;

    // 检查是否有缓存的数据
    if (!sheetsData.value[sheetName]) {
      // 显示加载状态
      localLoading.value = true;

      if (cachedWorkbook.value) {
        const sheetData = parseSheetDataOptimized(
          cachedWorkbook.value,
          sheetName,
        );
        if (sheetData) {
          sheetsData.value[sheetName] = sheetData;
        } else {
          sheetsData.value[sheetName] = {
            headers: ["列1"],
            content: [],
          };
        }
      }
    }

    if (signal.aborted) return;

    const sheetData = sheetsData.value[sheetName];
    const isEmpty = !sheetData.content || sheetData.content.length === 0;

    // 准备数据
    const cacheKey = `${sheetName}_${sheetData.headers.join("_")}_${sheetData.content.length}`;
    let newColumns: TableColumn[];
    let newData: any[][];

    if (processedDataCache.has(cacheKey)) {
      const cached = processedDataCache.get(cacheKey)!;
      newColumns = cached.columns;
      newData = cached.data;
    } else {
      newColumns = sheetData.headers.map((header, index) => ({
        data: index.toString(),
        title: header,
        type: "text" as const,
        width: 120,
      }));

      if (isEmpty) {
        newData = [];
      } else {
        const expectedCols = sheetData.headers.length;
        newData = sheetData.content.map((row) => {
          const normalizedRow = [...row];
          while (normalizedRow.length < expectedCols) {
            normalizedRow.push("");
          }
          return normalizedRow.slice(0, expectedCols);
        });
      }

      processedDataCache.set(cacheKey, { columns: newColumns, data: newData });
    }

    if (signal.aborted) return;

    // 优化表格更新
    if (tableInstance.value?.hotInstance) {
      // 暂停渲染
      tableInstance.value.hotInstance.suspendRender();

      try {
        // 批量更新设置
        tableInstance.value.hotInstance.updateSettings({
          data: newData,
          colHeaders: sheetData.headers,
          columns: newColumns.map((col) => ({
            data: col.data,
            type: col.type,
            width: col.width,
          })),
          minSpareRows: 0,
          minSpareCols: 0,
        });

        // 更新响应式数据
        tableColumns.value = newColumns;
        tableData.value = newData;
      } finally {
        // 恢复渲染
        tableInstance.value.hotInstance.resumeRender();

        // 使用微任务延迟尺寸调整
        queueMicrotask(() => {
          if (!signal.aborted && tableInstance.value?.hotInstance) {
            // updateTableDimensions(); // 移除此行，由dataTable组件内部处理
          }
        });
      }
    }

    if (isEmpty && !signal.aborted) {
      ElMessage.info(`工作表 "${sheetName}" 为空`);
    }
  } catch (error) {
    if (!signal.aborted) {
      console.error("Error in sheet change:", error);
      ElMessage.error(`切换工作表失败: ${error.message}`);
    }
  } finally {
    if (selectedSheet.value === sheetName && !signal.aborted) {
      localLoading.value = false;
      currentSheetController.value = null;
    }
  }
};

// 处理表格准备就绪
const handleTableReady = (instance: TableInstance) => {
  console.log("Table ready, instance:", instance);
  tableInstance.value = instance;
  isTableReady.value = true; // 设置表格准备就绪状态

  // 如果有待处理的工作表，立即加载
  if (selectedSheet.value && sheetsData.value[selectedSheet.value]) {
    processSheetData(selectedSheet.value);
  }
};

// 处理工作表数据
const processSheetData = (sheetName: string) => {
  console.log("Processing sheet data for:", sheetName);
  if (!isTableReady.value || !tableInstance.value) {
    console.warn("Table not ready, cannot process sheet data");
    return;
  }

  // 调用工作表切换处理函数
  handleSheetChange(sheetName);
};

// 处理数据变化
const handleDataChange = (changes: any) => {
  // 可以在这里处理数据变化
  console.log("Data changed:", changes);

  // 如果有当前文件路径，标记数据为已修改
  if (workspaceStore.getCurrentFilePath && changes && changes.length > 0) {
    workspaceStore.markDataAsModified(workspaceStore.getCurrentFilePath);
  }
};

// 导出文件
const handleExport = async () => {
  if (!tableInstance.value) return;

  // 如果当前表格有修改，则在导出前同步最新数据到 sheetsData
  const currentSheetName = selectedSheet.value;
  if (currentSheetName && sheetsData.value[currentSheetName]) {
    const currentTableData = tableInstance.value.getData();
    const currentTableColumns = tableInstance.value.getColumns();

    sheetsData.value[currentSheetName] = {
      headers: currentTableColumns.map((col) => col.title || col.data),
      content: currentTableData,
    };
    console.log(`Updated sheetsData for ${currentSheetName} before export.`);
  }

  try {
    // 使用导出工具
    await exportMultiSheet(sheetsData.value, {
      suggestedName: `数据导出_${Date.now()}`,
      sheetName: currentSheetName
    });


    // 重置表格修改状态
    if (dataTableRef.value?.resetModifiedState) {
      dataTableRef.value.resetModifiedState();
    }

    // 标记数据为已保存（如果有当前文件路径）
    if (workspaceStore.getCurrentFilePath) {
      workspaceStore.markDataAsSaved(workspaceStore.getCurrentFilePath);
    }
  } catch (error) {
    ElMessage.error("导出文件失败：" + error.message);
    console.error(error);
  }
};

// 保存文件
const handleSave = async () => {
  if (!tableInstance.value || !filePath.value) return;

  const loadingInstance = ElLoading.service({
    lock: true,
    text: "正在保存文件...",
    background: "rgba(0, 0, 0, 0.7)",
  });

  try {
    // 1. 同步当前表格数据
    const currentSheetName = selectedSheet.value;
    if (currentSheetName && sheetsData.value[currentSheetName]) {
      const currentTableData = tableInstance.value.getData();
      const currentTableColumns = tableInstance.value.getColumns();

      sheetsData.value[currentSheetName] = {
        headers: currentTableColumns.map((col) => col.title || col.data),
        content: currentTableData,
      };
    }

    // 2. 创建 workbook
    const wb = XLSX.utils.book_new();
    sheetNames.value.forEach((sheetName) => {
      const sheetData = sheetsData.value[sheetName];
      if (sheetData) {
        const dataForSheet = [sheetData.headers, ...sheetData.content];
        const ws = XLSX.utils.aoa_to_sheet(dataForSheet);
        XLSX.utils.book_append_sheet(wb, ws, sheetName);
      }
    });

    // 3. 将 workbook 写入 buffer
    const buffer = XLSX.write(wb, { bookType: "xlsx", type: "buffer" });

    // 4. 通过 IPC 将 buffer 写入文件
    await window.ipcRenderer.invoke(
      "fs:writeFileBuffer",
      filePath.value,
      buffer,
    );

    // 5. 更新状态
    workspaceStore.markDataAsSaved(filePath.value);
    ElMessage.success("文件保存成功");

    // 更新原始数据状态
    const currentData = tableInstance.value.getData();
    const currentColumns = tableInstance.value.getColumns();
    originalTableState.value = {
      data: currentData.map((row) => [...row]),
      columns: [...currentColumns],
    };
  } catch (error) {
    ElMessage.error("保存文件失败：" + error.message);
    console.error(error);
  } finally {
    loadingInstance.close();
  }
};

// 关闭文件
const handleClose = () => {
  const VITE_HIDE_HOME = import.meta.env.VITE_HIDE_HOME === "true";
  const multiTagsStore = useMultiTagsStoreHook();

  const index = multiTagsStore.multiTags.findIndex((item) => {
    if (item.params?.filePath) {
      return item.params.filePath === route.params.filePath;
    }
    return item.path === route.path;
  });

  const performClose = () => {
    if (index !== -1) {
      const tag = multiTagsStore.multiTags[index];
      if (tag.meta?.fixedTag) return;

      const { name, path } = tag;
      const permissionStore = usePermissionStoreHook();
      if (tag.meta?.keepAlive) {
        permissionStore.cacheOperate({ mode: "delete", name });
      }

      multiTagsStore.handleTags("splice", path);

      if (route.path === path) {
        const latestView = multiTagsStore.multiTags.slice(-1)[0];
        if (latestView) {
          router.push(latestView);
        } else {
          if (VITE_HIDE_HOME) {
            router.push("/");
          } else {
            router.push("/welcome");
          }
        }
      }
    }
  };

  if (isFileModified.value) {
    ElMessageBox.confirm("文件已修改，是否保存？", "确认关闭", {
      confirmButtonText: "保存并关闭",
      cancelButtonText: "不保存",
      distinguishCancelAndClose: true,
      type: "warning",
    })
      .then(() => {
        handleSave().then(() => {
          performClose();
        });
      })
      .catch((action) => {
        if (action === "cancel") {
          performClose();
        }
      });
  } else {
    performClose();
  }
};

// 监视 teleport 的重新渲染
watchEffect(() => {
  teleportKey.value++;
});

// 监听当前文件路径变化，自动加载新文件
// 修改watch逻辑，避免重复加载
watch(
  filePath,
  (newPath, oldPath) => {
    // 只有当路径真正改变且不是pendingFilePath时才加载
    if (newPath && newPath !== oldPath && !pendingFilePath.value) {
      console.log("File path changed, loading new file:", newPath);
      // 加载新文件
      handleExcelFileFromWorkspace(newPath);
    } else if (pendingFilePath.value) {
      // 处理待加载的文件
      console.log("Processing pending file:", pendingFilePath.value);
      nextTick(() => {
        handleExcelFileFromWorkspace(pendingFilePath.value!);
        pendingFilePath.value = null;
      });
    }
  },
  { immediate: false },
); // 改为false，避免初始加载时触发两次

// 监听 sheetNames 变化，重新计算 sheetTabBarHeight
watch(sheetNames, (newSheetNames) => {
  nextTick(() => {
    if (newSheetNames.length > 0 && sheetTabBarRef.value) {
      sheetTabBarHeight.value = sheetTabBarRef.value.offsetHeight;
    } else {
      sheetTabBarHeight.value = 0;
    }
  });
});

// 分析数据
const analyzeData = () => {
  if (!tableInstance.value) {
    ElMessage.warning("没有可分析的数据");
    return;
  }

  isAnalyzeVisible.value = !isAnalyzeVisible.value;
  // 如果打开分析，关闭可视化
  if (isAnalyzeVisible.value) {
    isVisualizeVisible.value = false;
  }
};

// 打开可视化页面
const toOpenVisualize = () => {
  if (!tableInstance.value) {
    ElMessage.warning("请先导入数据");
    return;
  }

  isVisualizeVisible.value = !isVisualizeVisible.value;
  // 如果打开可视化，关闭分析
  if (isVisualizeVisible.value) {
    isAnalyzeVisible.value = false;
  }
};

// 处理模型变化
const handleBuildModel = (model: { id: string; category: string }) => {
  if (!tableInstance.value) {
    ElMessage.warning("请先导入数据");
    return;
  }

  selectedModelType.value = model.id;

  const data = tableInstance.value.getData();
  const columns = tableInstance.value.getColumns();

  tableDataStore.setTableData(data);
  tableDataStore.setTableHeader(columns.map((col) => col.title || col.data));

  // 根据模型类别决定使用哪个对话框
  if (model.category === 'linear') {
    dialogStore.showDialog("linearModel");
  } else {
    dialogStore.showDialog("mlModel");
  }
};

// 打开数据预处理对话框
const openPreprocessDialog = () => {
  if (!tableInstance.value) {
    ElMessage.warning("请先导入数据");
    return;
  }

  detectionResult.value = null; // 在打开对话框前重置结果

  const data = tableInstance.value.getData();
  const columns = tableInstance.value.getColumns();

  tableDataStore.setTableData(data);
  tableDataStore.setTableHeader(columns.map((col) => col.title || col.data));
  dialogStore.showDialog("preprocessData");
};

const handleModelConfirm = async (modelConfig: ModelConfig) => {
  await unifiedModelBuild(modelConfig, 'linear');
};

const handleMLModelConfirm = async (modelConfig: ModelConfig) => {
  const isTree = ["DecisionTreeRegressor", "RandomForestRegressor", "GradientBoostingRegressor", "XGBoost"].includes(selectedModelType.value);
  await unifiedModelBuild(modelConfig, isTree ? 'tree' : 'ml');
};

const unifiedModelBuild = async (modelConfig: ModelConfig, modelCategory: 'linear' | 'tree' | 'ml') => {
  const dialogStore = useDialogStore();
  try {
    const taskUid = crypto.randomUUID();
    const modelConfigWithUid = {
      ...modelConfig,
      model: {
        ...modelConfig.model,
        taskUid: taskUid,
      },
    };

    const socket = getSocket();
    if (socket && socket.connected) {
      socket.emit("get_model_info", {
        taskUid
      });
    } else {
      console.warn("Socket not connected, cannot check RabbitMQ status.");
      ElMessage.error("程序服务出现异常，无法完成建模，请重新启动程序后重试。");
      return;
    }

    let buildPromise;
    if (modelCategory === 'linear') {
      buildPromise = buildLinearModel(modelConfigWithUid);
    } else if (modelCategory === 'tree') {
      buildPromise = buildTreeModel(modelConfigWithUid);
    } else {
      buildPromise = buildMLModel(modelConfigWithUid);
    }

    const res = await buildPromise;

    if (res.code === 200 && res.data.taskUid) {
      openResultWindow(res.data.taskUid, modelConfig.model.algorithm.name, modelCategory);
      ElMessage.success({
        message: `模型构建任务已开始，任务ID: ${res.data.taskUid}。正在监听进度...`,
        duration: 5000,
      });
    } else {
      ElMessage.error(res.msg || "模型构建任务启动失败");
    }
  } catch (err) {
    ElMessage.error("请求失败：" + (err as Error).message);
  } finally {
    if (modelCategory === 'linear') {
      dialogStore.hideDialog("linearModel");
    } else {
      dialogStore.hideDialog("mlModel");
    }
  }
};

const openResultWindow = (taskUid: string, modelType: string, modelCategory: 'linear' | 'tree' | 'ml') => {
  const routeName = modelCategory === 'linear' ? 'LMModelResult' : 'MLModelResult';
  const {
    href
  } = router.resolve({
    name: routeName,
    query: {
      taskUid: taskUid.toString(),
      modelType: modelType,
    },
  });

  const newWindow = window.open(href, "_blank");
  if (newWindow) {
    newWindow.name = `model_result_window_${taskUid}`;
  }

  if (window.ipcRenderer) {
    window.ipcRenderer.send("open_model_result_window", {
      taskUid: taskUid,
      modelType: modelType,
    });
  }
};

// 处理异常值检测
const handleOutlierDetection = async (config: PreprocessConfig) => {
  if (!tableInstance.value) return;

  try {
    const res = await reqOutlierDetection(config);
    if (res.code === 200) {
      const originalData = tableInstance.value.getData();
      detectionResult.value = {
        success: true,
        labels: res.data.labels,
        totalRows: originalData.length,
        algorithm: config.preprocess.algorithm.name
      };
    } else {
      detectionResult.value = {
        success: false,
        message: res.message || "未知错误"
      };
    }
  } catch (err) {
    detectionResult.value = {
      success: false,
      message: err.message || "请求失败"
    };
  }
};

const handleDeleteOutliers = (outlierRows: number[]) => {
  if (!tableInstance.value) return;
  const originalData = tableInstance.value.getData();

  const filteredData = originalData.filter(
    (_, index) => !outlierRows.includes(index)
  );

  if (tableInstance.value?.hotInstance) {
    tableInstance.value.hotInstance.loadData(filteredData);
    tableData.value = filteredData;
  }

  ElMessage.success(`已删除 ${outlierRows.length} 行异常数据`);
  dialogStore.hideDialog("preprocessData");
};

// 处理预处理确认
const handlePreprocessingConfirm = async (config: PreprocessConfig) => {
  if (!tableInstance.value) return;

  const originalData = tableInstance.value.getData();
  const originalRowCount = originalData.length;

  try {
    // 缺失值处理 (异常值检测已移至 handleOutlierDetection)
    if (
      config.preprocess.algorithm.name !== "LocalOutlierFactor" &&
      config.preprocess.algorithm.name !== "IsolationForest" &&
      config.preprocess.algorithm.name !== "OneClassSVM"
    ) {
      const res = await reqDataFill(config);

      if (res.code === 200) {
        const { data, meta } = res.data;

        // 更新列配置
        tableColumns.value = meta.headers.all.map((header, index) => ({
          data: index.toString(),
          title: header,
          type: "text",
          width: 120, // 保持固定列宽
        }));

        // 优化数据更新 - 使用批量操作
        if (tableInstance.value?.hotInstance) {
          try {
            tableInstance.value.hotInstance.suspendRender();
            tableInstance.value.hotInstance.updateSettings({
              colHeaders: meta.headers.all,
              columns: tableColumns.value.map((col) => ({
                data: col.data,
                type: col.type,
              })),
              data: data,
            });
            tableInstance.value.hotInstance.resumeRender();

            // 更新响应式数据
            tableData.value = data;
          } catch (error) {
            console.warn("Batch update failed, using fallback:", error);
            // 回退方案
            tableData.value = [...data];
            await nextTick();
            if (dataTableRef.value) {
              dataTableRef.value.forceUpdate();
            }
          }
        } else {
          // 表格实例不存在时的回退方案
          tableData.value = [...data];
          await nextTick();
          if (dataTableRef.value) {
            dataTableRef.value.forceUpdate();
          }
        }

        // 提示信息
        if (config.preprocess.algorithm.name === "DropNA") {
          const deletedRows = originalRowCount - data.length;
          ElMessage.success(`已删除 ${deletedRows} 行缺失数据`);
        } else {
          ElMessage.success("数据预处理成功");
        }
      } else {
        ElMessage.error("数据预处理失败");
      }
    }
  } catch (err) {
    ElMessage.error("请求失败：" + (err?.message || err));
  }
};

// 新建工作表
const addNewSheet = async () => {
  // 生成唯一名称 Sheet1, Sheet2 ...
  let index = sheetNames.value.length + 1;
  let newName = `Sheet${index}`;
  while (sheetNames.value.includes(newName)) {
    index++;
    newName = `Sheet${index}`;
  }

  // 添加空数据
  sheetNames.value.push(newName);

  // 默认显示 20 行空白，8 列
  const defaultCols = 8;
  const headers = Array.from({ length: defaultCols }, (_, i) => `列${i + 1}`);
  const blankRows = Array.from({ length: 20 }, () =>
    Array.from({ length: defaultCols }, () => ""),
  );

  sheetsData.value[newName] = {
    headers,
    content: blankRows,
  };

  // 选中并刷新
  selectedSheet.value = newName;
  await handleSheetChange(newName);
};

// 处理右键命令
const handleSheetCommand = (
  sheetName: string,
  command: string | number | object,
) => {
  if (command === "delete") {
    deleteSheet(sheetName);
  }
};

// 删除工作表
const deleteSheet = (sheetName: string) => {
  if (sheetNames.value.length <= 1) {
    ElMessage.warning("至少保留一个工作表");
    return;
  }

  // 移除
  sheetNames.value = sheetNames.value.filter((n) => n !== sheetName);
  delete sheetsData.value[sheetName];

  // 若删除的是当前工作表，切换到第一个
  if (selectedSheet.value === sheetName) {
    const newActive = sheetNames.value[0];
    selectedSheet.value = newActive;
    handleSheetChange(newActive);
  }
};

const tableKey = ref(0);
const isDestroyed = ref(false);
const isModified = ref(false);
const originalData = ref<any[][]>([]);
const isSidebarAnimating = ref(false);
</script>

<style lang="scss" scoped>
// 卡片容器
.flex-1 {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  height: 100%;
}

// 工具栏容器样式
.toolbar-container {
  padding: 12px 0;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

// 表格外层容器样式
.table-outer-container {
  background: #fafafa;
  border-radius: 4px;
  padding: 6px 12px 0 12px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.03);
}

#data-table-container {
  flex: 1 1 auto;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  min-height: 0;
  box-sizing: border-box;
}

.sheet-tab-bar {
  display: flex;
  padding: 0;
  background: var(--el-bg-color-page);
  border-top: 1px solid var(--el-border-color-lighter);
  height: 36px;

  &::-webkit-scrollbar {
    height: 6px;
  }

  &::-webkit-scrollbar-thumb {
    background: rgba(0, 0, 0, 0.2);
    border-radius: 3px;
  }
}

// 按钮间距
:deep(.el-button + .el-button) {
  margin-left: 0px;
}

// 表格容器样式
:deep(.data-table-container) {
  height: 100%;
}

:deep(.data-table-body) {
  flex: 1;
  height: 100%;
  overflow: visible; // 允许显示滚动条
  margin-top: 0; // 移除顶部间距
}

:deep(.table-content-wrapper) {
  height: 100%;
}

// 表头样式修改 - 移除边框和背景色
:deep(.handsontable .ht_clone_top .htCore thead th),
:deep(.handsontable .htCore thead th) {
  background: #fafafa !important; // 与外层容器背景一致
  border: none !important; // 移除边框
  border-bottom: none !important; // 确保底部边框也被清除
  color: #606266; // 保持原有字体颜色
}

// 空状态样式
:deep(.empty-state) {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

:deep(.el-empty__image) {
  display: flex;
  justify-content: center;
  align-items: center;
}

// Handsontable 滚动条样式优化
:deep(.handsontable) {

  // 确保表格容器可以正常滚动
  .ht_master {
    // 主容器允许溢出
    overflow: visible !important;

    .wtHolder {
      // 主表格区域显示滚动条
      overflow: auto !important;
      // 确保滚动条可见且美观
      scrollbar-width: thin;

      // 亮色模式滚动条
      scrollbar-color: #c1c1c1 #f1f1f1;

      &::-webkit-scrollbar {
        width: 8px;
        height: 8px;
      }

      &::-webkit-scrollbar-track {
        background: #f8f9fa;
        border-radius: 4px;
      }

      &::-webkit-scrollbar-thumb {
        background: #dee2e6;
        border-radius: 4px;

        &:hover {
          background: #adb5bd;
        }

        &:active {
          background: #6c757d;
        }
      }

      // 滚动条角落
      &::-webkit-scrollbar-corner {
        background: #f8f9fa;
      }
    }

    // 确保水平滚动条区域可见
    .wtHider {
      overflow: visible !important;
    }
  }
}

// 深色模式下的滚动条样式覆盖
html.dark :deep(.handsontable) {
  .ht_master .wtHolder {
    // 深色模式滚动条
    scrollbar-color: rgb(63 64 66) transparent;

    &::-webkit-scrollbar-track {
      background: var(--el-fill-color-lighter);
    }

    &::-webkit-scrollbar-thumb {
      background: rgb(63 64 66);

      &:hover {
        background: rgb(92 93 96);
      }

      &:active {
        background: rgb(120 121 124);
      }
    }

    &::-webkit-scrollbar-corner {
      background: var(--el-fill-color-lighter);
    }
  }

  // 表头区域不显示滚动条，但保持同步滚动
  .ht_clone_top {
    .wtHolder {
      overflow-x: hidden !important; // 隐藏水平滚动条
      overflow-y: hidden !important; // 隐藏垂直滚动条
    }
  }

  // 左侧固定列区域
  .ht_clone_left {
    .wtHolder {
      overflow-x: hidden !important; // 隐藏水平滚动条
      overflow-y: hidden !important; // 隐藏垂直滚动条
    }
  }

  // 左上角区域
  .ht_clone_corner {
    .wtHolder {
      overflow: hidden !important; // 完全隐藏滚动条
    }
  }
}

/* 按钮组淡入淡出动画 */
.fade-enter-active,
.fade-leave-active {
  transition: all 0.3s cubic-bezier(0.55, 0, 0.1, 1);
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
  transform: translateY(-6px);
}

.file-actions {
  display: flex;
  gap: 8px;
}

.action-button {
  border-radius: 4px;
  background: #fafafa;
  padding: 0;
  font-size: 14px;
  transition: all 0.3s;
  border: none;
  width: 78px;
  height: 28px;
  line-height: 28px;
  text-align: center;
  cursor: pointer;
}

.save-button {
  color: var(--el-color-primary);
}

.save-button.disabled {
  color: #909399;
  background: #f2f2f2;
  cursor: not-allowed;
}

.close-button {
  color: #ff6060;
}

html.dark .action-button {
  background: #2a2a2a;
}

html.dark .save-button.disabled {
  color: #606266;
  background: #1a1a1a;
}

.sheet-tab {
  min-width: 100px;
  height: 100%;
  padding: 0 12px;
  font-size: 13px;
  text-align: center;
  background: #dee7f5;
  cursor: pointer;
  user-select: none;
  transition: all 0.2s;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0;
  border-right: 1px solid #f0f0f0;

  &:hover {
    background: #fff;
  }

  &.active {
    background: #fff;
    color: var(--el-color-primary);
    font-weight: bold;

    &::after {
      content: "";
      position: absolute;
      bottom: 0;
      left: 50%;
      transform: translateX(-50%);
      width: 24px;
      height: 2px;
      background-color: var(--el-color-primary);
      border-radius: 1px;
    }
  }
}

.sheet-add {
  width: 36px;
  min-width: 36px;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0;
  background: #dee7f5;

  &:hover {
    background: #fff;
  }
}

.table-loading-mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.7);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.loading-spinner {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  color: var(--el-text-color-regular);
}

.loading-spinner .el-icon {
  font-size: 24px;
}

/* 深色模式适配 */
html.dark .table-loading-mask {
  background: rgba(0, 0, 0, 0.7);
}

.menu-button {
  width: 36px;
  height: 36px;
  border-radius: 2px;
  background: #FFFFFF;
  box-shadow: 0px 2px 8px 0px rgba(0, 0, 0, 0.08);

  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;

  &:hover {

    .menu-icon.normal {
      opacity: 0;
    }

    .menu-icon.hover {
      opacity: 1;
    }
  }

  .menu-icon {
    width: 22px;
    height: 22px;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    transition: opacity 0.2s ease;
  }

  .normal {
    opacity: 1;
  }

  .hover,
  .selected {
    opacity: 0;
  }

}
</style>
