import request from "@/utils/request";
import type { ModelConfig, RegModelResult } from "@/types/models";
import { AxiosHeaders } from "axios";

export type MLModelResponse = {
  code: number;
  msg: string;
  data: {
    taskUid: string;
  };
};

export type PredictionResponse = {
  code: number;
  msg: string;
  data: {
    predictions: any[];
    label: string;
  };
};

export const buildTreeModel = (data?: ModelConfig) => {
  return request.post<MLModelResponse>("/tree/build", data, {
    headers: new AxiosHeaders({}),
    skipLoading: true,
  });
};

export const buildLinearModel = (data?: ModelConfig) => {
  return request.post<MLModelResponse>("/linear/build", data, {
    headers: new AxiosHeaders({}),
    skipLoading: true,
  });
};

export const buildMLModel = (data?: ModelConfig) => {
  return request.post<MLModelResponse>("/other/build", data, {
    headers: new AxiosHeaders({}),
    skipLoading: true,
  });
};

export const downloadModelFile = (taskUid: string) => {
  return request.post<Blob>(
    "/download_model",
    { taskUid },
    {
      responseType: "blob", // This is important for downloading binary data
      headers: new AxiosHeaders({
        "Content-Type": "application/json",
      }),
      skipLoading: true,
    },
  );
};

export const predictWithData = (taskUid: string, data: any[]) => {
  return request.post<PredictionResponse>(
    "/predict",
    { taskUid, data },
    {
      headers: new AxiosHeaders({
        "Content-Type": "application/json"
      }),
      skipLoading: true
    }
  );
};
