from typing import Literal, TypedDict, Union, Optional, Any, Dict, List
import json

<PERSON>er<PERSON>ey = Literal["index", "target", "features", "deletes", "all"]
HeaderMap = Dict[HeaderKey, List[str]]

class DatasetMeta(TypedDict):
    '''数据元信息'''
    headers: HeaderMap

class Dataset(TypedDict):
    '''数据集结构'''
    data: List[List[float]]
    meta: DatasetMeta

class ModelAlgorithm(TypedDict):
    '''模型算法'''
    name: str
    params: Dict[str, Any]

class CrossValidationConfig(TypedDict):
    """交叉验证配置"""
    k: int
    random_state: Optional[int]

class TestSplitConfig(TypedDict):
    """测试集分割配置"""
    size: float  # 测试集比例
    random_state: Optional[int]

class ModelMetaEvaluation(TypedDict):
    '''模型评估配置'''
    cv: Optional[CrossValidationConfig]
    loocv: Optional[bool]
    test: Optional[TestSplitConfig]

class ModelMeta(TypedDict):
    '''模型元信息'''
    headers: HeaderMap
    evaluation: ModelMetaEvaluation

class Model(TypedDict):
    '''模型配置'''
    algorithm: ModelAlgorithm
    meta: ModelMeta

class ModelRequest(TypedDict):
    '''模型request参数'''
    dataset: Dataset
    model: Model

from dataclasses import dataclass
import pandas as pd
@dataclass
class ModelConfig(object):
    task_uid: str
    x_train: Union[pd.DataFrame, pd.Series]
    y_train: Union[pd.DataFrame, pd.Series]
    x_test: Optional[Union[pd.DataFrame, pd.Series]]
    y_test: Optional[Union[pd.DataFrame, pd.Series]]
    cv: Optional[Union[bool, int]]
    loocv: Optional[bool]
    test: Optional[bool]
    asynchronous: Optional[bool]
    alg_name: str
    alg_param: Optional[Dict[str, Any]]
    is_rabbitmq_ready: bool
    optimize: Optional[bool]

    def to_dict(self) -> Dict[str, Any]:
        '''将ModelConfig对象转换为可JSON序列化的字典'''
        feature_names = self.x_train.columns.tolist()
        return {
            "task_uid": self.task_uid,
            "x_train": self.x_train.to_dict(),
            "y_train": { f"{self.y_train.name}": self.y_train.to_dict() },
            "x_test": self.x_test.to_dict() if self.x_test is not None else None,
            "y_test": { f"{self.y_test.name}": self.y_test.to_dict() } if self.y_test is not None else None,
            "cv": self.cv,
            "loocv": self.loocv,
            "test": self.test,
            "asynchronous": self.asynchronous,
            "alg_name": self.alg_name,
            "alg_param": self.alg_param,
            "is_rabbitmq_ready": self.is_rabbitmq_ready,
            "optimize": self.optimize,
            "feature_names": feature_names
        }
    
    def to_json(self) -> str:
        '''将ModelConfig对象转换为JSON字符串'''
        return json.dumps(self.to_dict(), ensure_ascii=False)
    
    def from_dict(data: dict, optimized_params: Optional[dict] = None) -> 'ModelConfig':
        if optimized_params is None:
            alg_param = data["alg_param"]
        else:
            alg_param = optimized_params
        y_train = pd.DataFrame.from_dict(data["y_train"])
        if y_train.shape[1] == 1:
            y_train = y_train.iloc[:, 0]
        if data["y_test"] is not None:
            y_test = pd.DataFrame.from_dict(data["y_test"])
            if y_test.shape[1] == 1:
                y_test = y_test.iloc[:, 0]
        else:
            y_test = None
        feature_names = data["feature_names"]
        return ModelConfig(
            task_uid=data["task_uid"],
            x_train=pd.DataFrame.from_dict(data["x_train"])[feature_names],
            y_train=y_train,
            x_test=pd.DataFrame.from_dict(data["x_test"])[feature_names] if data["x_test"] is not None else None,
            y_test=y_test,
            cv=data["cv"],
            loocv=data["loocv"],
            test=data["test"],
            asynchronous=data["asynchronous"],
            alg_name=data["alg_name"],
            alg_param=alg_param,
            is_rabbitmq_ready=data["is_rabbitmq_ready"],
            optimize=data["optimize"]
        )