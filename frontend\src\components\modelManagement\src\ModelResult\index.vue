<template>
  <div class="model-result-page">
    <!-- 自定义窗口标题栏 -->
    <div class="custom-titlebar" @dblclick="handleDoubleClick">
      <div class="title-left">
        <h3>{{ modelDisplayName }}</h3>
      </div>
      <div class="title-right">
        <div class="control-container" :style="{ backgroundColor: epThemeColor }">
          <div class="window-controls">
            <div class="control-btn" @click="minimizeWindow">
              <img :src="minimizeIcon" alt="minimize" class="control-icon" />
            </div>
            <div class="control-btn" @click="maximizeWindow">
              <img :src="maximizeIcon" alt="maximize" class="control-icon" />
            </div>
            <div class="control-btn close-btn" @click="closeWindow">
              <img :src="closeIcon" alt="close" class="control-icon" />
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 内容区域容器 - 添加内边距 -->
    <div class="content-container">
      <!-- 构建进度条 -->
      <div v-if="isLoading" class="loading-container">
        <div class="loading-content">
          <img :src="loadingIcon" alt="loading" class="loading-icon" />
          <h2 class="loading-title">
            {{
              buildStatus === "processing"
                ? "模型构建中"
                : buildStatus === "completed"
                  ? "加载模型结果"
                  : "准备中"
            }}
          </h2>
          <div class="progress-wrapper">
            <span class="progress-percentage">{{ progress.toFixed(0) }}%</span>
            <el-progress
              :percentage="progress"
              :show-text="false"
              :status="
                buildStatus === 'error'
                  ? 'exception'
                  : buildStatus === 'completed'
                    ? 'success'
                    : undefined
              "
              :indeterminate="false"
              :duration="5"
              class="progress-bar"
            />
          </div>
          <p class="build-status">
            {{ buildStatusMessage || "正在执行复杂计算..." }}
          </p>
        </div>
      </div>

      <div v-else-if="modelResult" class="model-result-layout">
        <div class="model-static-info">
          <ModelIntroduction
            :model-type="modelType"
            :model-display-name="modelDisplayName"
            :model-result="modelResult"
          />
        </div>

        <div class="results-scroll-container">
          <div
            class="result-card"
            v-for="(item, index) in entryList"
            :key="item.key"
          >
            <div
              class="result-card-header"
              :class="{ 'is-clickable': item.key === 'save' }"
              @click="item.key === 'save' ? toggleSection('save') : null"
            >
              <div class="header-left">
                <modelResultIcon class="header-icon" />
                <div class="header-text">
                  <h4 class="header-title">{{ item.title }}</h4>
                  <p class="header-desc">{{ item.desc }}</p>
                </div>
              </div>
              <el-button
                v-if="item.key !== 'save'"
                class="expand-btn"
                :class="{ 'is-active': visibleSections[item.key] }"
                @click="toggleSection(item.key)"
              >
                {{ visibleSections[item.key] ? "收起" : "展开" }}
                <el-icon
                  class="el-icon--right"
                  :class="{ 'is-active': visibleSections[item.key] }"
                >
                  <ArrowDown />
                </el-icon>
              </el-button>
            </div>
            <el-collapse-transition>
              <div v-show="visibleSections[item.key]">
                <div class="result-card-body">
                  <component
                    :is="getComponent(item.key)"
                    :model-result="modelResult"
                    :dataset-config="datasetConfig"
                    :task-uid="taskUid"
                  />
                </div>
              </div>
            </el-collapse-transition>
          </div>
        </div>
      </div>
      <div v-else class="empty-container">
        <el-empty class="model-empty" description="未找到模型结果数据" />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onBeforeUnmount, reactive } from "vue";
import { useRoute } from "vue-router";
import { ElMessage, ElMessageBox } from "element-plus";
import { ArrowDown } from "@element-plus/icons-vue";
import {
  ModelInfoCard,
  MetricsTable,
  ModelCharts,
  PredictionTable,
  DataSplitTable,
  OptimizationResultTable,
  ModelIntroduction,
  ModelPrediction
} from "@/components/modelManagement";
// 不再使用API方式获取线性模型结果，改为统一使用WebSocket
import { downloadModelFile } from "@/api/models/model"; // Import the new download function
import { getSocket } from "@/utils/socket";
// 导入窗口控制图标
import minimizeIcon from "@/assets/svg/minimize_btn.svg?url";
import maximizeIcon from "@/assets/svg/maximize_btn.svg?url";
import closeIcon from "@/assets/svg/close_btn.svg?url";
import loadingIcon from "@/assets/svg/loading.svg?url";
import modelResultIcon from "@/assets/svg/modelResult.svg?component";
import { useEpThemeStoreHook } from "@/store/modules/epTheme";
import { ElCollapseTransition } from "element-plus";

// 获取Element Plus主题色
const epThemeStore = useEpThemeStoreHook();
const epThemeColor = computed(() => {
  return epThemeStore.getEpThemeColor;
});

// 窗口控制函数
const minimizeWindow = () => {
  // 使用特殊的IPC事件来控制当前窗口
  window.ipcRenderer.send("minimize-current-window");
};

const maximizeWindow = () => {
  // 使用特殊的IPC事件来控制当前窗口
  window.ipcRenderer.send("maximize-current-window");
};

const closeWindow = () => {
  // 使用特殊的IPC事件来控制当前窗口
  window.ipcRenderer.send("close-current-window");
};

const getModelTypeLabel = (type: string | null) => {
  if (!type) return "模型结果";
  const typeMap = {
    LinearRegression: "多元线性回归",
    Ridge: "岭回归",
    Lasso: "Lasso回归",
    ElasticNet: "弹性网络回归",
    DecisionTreeRegressor: "决策树回归",
    RandomForestRegressor: "随机森林回归",
    XGBoost: "XGBoost回归",
    GradientBoostingRegressor: "梯度提升回归",
    SVR: "支持向量机回归",
    MLPRegressor: "人工神经网络回归"
  };
  return typeMap[type as keyof typeof typeMap] || type;
};

// 处理双击事件
const handleDoubleClick = (e: MouseEvent) => {
  // 检查是否点击在按钮或其他交互元素上
  const target = e.target as HTMLElement;
  
  // 如果点击的是窗口控制按钮，不触发最大化
  if (
    target.classList.contains('control-btn') ||
    target.closest('.control-btn')
  ) {
    return;
  }
  
  // 触发当前窗口的最大化/还原
  window.ipcRenderer.send("maximize-current-window");
};

const props = withDefaults(
  defineProps<{
    resultType?: "linear" | "ml";
  }>(),
  {
    resultType: "linear",
  },
);

const baseEntryList = [
  { key: "metrics", title: "评价指标", desc: "查看模型评价指标" },
  { key: "charts", title: "预测结果图表", desc: "查看预测结果的可视化" },
  { key: "predictions", title: "预测数据", desc: "查看预测数据明细" },
  { key: "splits", title: "数据集划分", desc: "查看训练/测试/交叉验证集" },
  { key: "predict", title: "模型预测", desc: "使用新数据进行预测" },
  { key: "save", title: "模型保存", desc: "下载已训练模型文件" },
];

const visibleSections = reactive({
  introduction: true,
  metrics: true,
  charts: true,
  predictions: false,
  splits: false,
  optimization: false,
  predict: false
});

const entryList = computed(() => {
  const list = [...baseEntryList];
  if (modelResult.value?.optimized_result || modelResult.value?.optimized_params) {
    list.push({
      key: "optimization",
      title: "超参数优化",
      desc: "查看超参数寻优过程",
    });
  }
  return list;
});


// 图标映射 - a new icon is used for all cards now, so this is no longer needed

type ViewKey =
  | "entry"
  | "metrics"
  | "charts"
  | "predictions"
  | "splits"
  | "optimization"
  | "predict";

async function handleSaveModel() {
  try {
    await ElMessageBox.confirm("确认下载该模型文件？", "模型保存", {
      confirmButtonText: "确认",
      cancelButtonText: "取消",
      type: "warning"
    });
    if (!taskUid.value) {
      ElMessage.error("无法获取模型任务ID，无法下载。");
      return;
    }

    const response = await downloadModelFile(taskUid.value);

    if (response instanceof Blob) {
      const url = window.URL.createObjectURL(response);
      const link = document.createElement("a");
      link.href = url;
      link.setAttribute("download", `${taskUid.value}.pkl`); // 使用taskUid作为文件名
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);
      ElMessage.success("模型已开始下载");
    } else {
      ElMessage.error("下载失败：服务器未返回有效文件。");
      console.error("Download failed:", response);
    }
  } catch (error) {
    // 用户取消或下载失败
    if (error === "cancel") {
      ElMessage.info("下载已取消。");
    } else if (error) {
      ElMessage.error(`下载模型失败: ${(error as Error).message}`);
      console.error("Error saving model:", error);
    }
  }
}

function goBack() {
  // To-do: Add logic to go back to the previous page or state
  console.log("Go back clicked");
}

const modelResult = ref<any>(null);
const route = useRoute();

// 模型构建状态和进度
const isLoading = ref(true);
// 明确设置进度条初始值为0，不要让浏览器默认值干扰
const progress = ref(0);
const buildStatus = ref<"waiting" | "processing" | "completed" | "error">(
  "waiting",
);
const buildStatusMessage = ref("");
const taskUid = ref<string | null>(null);
const modelType = ref<string | null>(null);
const modelDisplayName = computed(() => getModelTypeLabel(modelType.value));

// 工具：递归将对象的所有键转换为 snake_case 小写，便于统一前后端字段
// 若字符串全部为大写(可带数字)，直接整体转小写；否则在小写/数字与大写之间插入下划线
const toSnakeCase = (str: string) => {
  // 全大写(或数字)的情况，如 "RMSE"、"R2" → "rmse"、"r2"
  if (/^[A-Z0-9]+$/.test(str)) return str.toLowerCase();

  return str
    .replace(/([a-z0-9])([A-Z])/g, "$1_$2") // fooBar→foo_Bar  / abcD→abc_D
    .replace(/([A-Z]+)([A-Z][a-z])/g, "$1_$2") // HTTPServer→HTTP_Server
    .toLowerCase()
    .replace(/^_/, ""); // 去掉可能的前导下划线
};

const normalizeKeysDeep = (obj: any): any => {
  if (Array.isArray(obj)) {
    return obj.map((item) => normalizeKeysDeep(item));
  }
  if (obj !== null && typeof obj === "object") {
    return Object.keys(obj).reduce((acc, key) => {
      const newKey = toSnakeCase(key);
      if (newKey === "metrics") {
        // 保留 metrics 内部键的原样格式
        acc[newKey] = obj[key];
      } else {
        acc[newKey] = normalizeKeysDeep(obj[key]);
      }
      return acc;
    }, {} as Record<string, any>);
  }
  return obj;
};

// 处理模型进度更新
const handleModelProgress = (event: CustomEvent) => {
  const data = event.detail;
  console.log("Model progress update received:", data);

  // 统一使用 taskUid
  const event_taskUid = data.taskUid;

  if (event_taskUid == taskUid.value) {
    buildStatus.value = data.status;
    buildStatusMessage.value = data.message || "正在构建模型...";

    if (data.progress !== undefined) {
      progress.value = Math.min(99, data.progress); // 保留最后1%给加载结果
    }

    // 注意：不再在这里处理completed状态
    // completed状态现在由model_complete事件处理
    if (data.status === "error") {
      ElMessage.error(`模型构建失败: ${data.message || "发生未知错误"}`);
    }
  }
};

// 处理来自Electron IPC的模型进度更新
const handleIpcModelProgress = (_event: Event, data: any) => {
  console.log("IPC model progress update received:", data);

  // 统一使用 taskUid
  const event_taskUid = data.taskUid;

  if (event_taskUid == taskUid.value) {
    buildStatus.value = data.status;
    buildStatusMessage.value = data.message || "正在构建模型...";

    if (data.progress !== undefined) {
      progress.value = Math.min(99, data.progress);
    }

    // 注意：不再在这里处理completed状态
    // completed状态现在由model_complete事件处理
    if (data.status === "error") {
      ElMessage.error(`模型构建失败: ${data.message || "发生未知错误"}`);
    }
  }
};

// 通过IPC事件机制请求获取模型结果
const fetchModelResult = async () => {
  console.log("fetchModelResult called, taskUid:", taskUid.value);

  if (!taskUid.value) {
    console.warn("尝试获取模型结果，但taskUid不存在");
    isLoading.value = false;
    return;
  }

  try {
    console.log(
      `请求获取${props.resultType === "linear" ? "线性" : "机器学习"}模型结果, taskUid:`,
      taskUid.value,
    );

    // 通过IPC请求主窗口获取模型信息
    if (window.ipcRenderer) {
      // 设置一次性IPC监听器，等待结果返回
      const responseHandler = (_event: any, response: any) => {
        console.log("通过IPC接收到的模型信息响应:", response);

        if (response && response.code === 200) {
          // 尝试不同的数据位置获取结果
          if (response.data) {
            modelResult.value = normalizeKeysDeep(response.data);
          } else if (response.result) {
            modelResult.value = normalizeKeysDeep(response.result);
          }

          // 如果没有结果但有返回数据且是字符串，尝试解析
          if (!modelResult.value && typeof response.data === "string") {
            try {
              modelResult.value = JSON.parse(response.data);
            } catch (e) {
              console.error("Failed to parse result data:", e);
            }
          }

          isLoading.value = false;
          console.log("模型结果已加载:", modelResult.value);

          // 清理一次性监听器
          window.ipcRenderer.removeAllListeners("model_info_response");
        } else {
          ElMessage.error(`获取模型结果失败: ${response?.msg || "未知错误"}`);
          isLoading.value = false;
        }
      };

      // 注册接收响应的事件
      window.ipcRenderer.on("model_info_response", responseHandler);

      // 发送请求到主进程
      window.ipcRenderer.send("request_model_info", {
        taskUid: taskUid.value,
        modelType: modelType.value || props.resultType,
      });
    } else {
      console.error("IPC通信不可用");
      ElMessage.error("无法与主窗口通信，请尝试从主窗口打开结果页面");
      isLoading.value = false;
    }
  } catch (error) {
    console.error("Error fetching model result:", error);
    ElMessage.error(`获取模型结果失败: ${(error as Error).message}`);
    isLoading.value = false;
  }
};

// 处理模型完成事件，直接获取模型结果
const handleModelComplete = (event: CustomEvent) => {
  const data = event.detail;
  console.log("Model complete event received:", data);

  // 检查taskUid是否匹配
  if (data.taskUid === taskUid.value) {
    // 更新状态为完成
    progress.value = 100;
    buildStatus.value = "completed";
    buildStatusMessage.value = "模型构建完成";

    // 直接使用返回的结果数据
    if (data.result) {
      modelResult.value = normalizeKeysDeep(data.result);
      isLoading.value = false;
      console.log("从model_complete事件直接获取到模型结果:", modelResult.value);

      // 立即跳转到结果页面并提示用户
      ElMessage.success({
        message: "模型构建完成，显示结果",
        duration: 2000,
      });
    } else {
      console.warn("model_complete事件没有包含结果数据");
      // 如果没有结果数据，仍然尝试获取
      fetchModelResult();
    }
  }
};

// 处理来自Electron IPC的模型完成事件
const handleIpcModelComplete = (_event: Event, data: any) => {
  console.log("IPC model complete event received:", data);

  if (data.taskUid === taskUid.value) {
    // 更新状态为完成
    progress.value = 100;
    buildStatus.value = "completed";
    buildStatusMessage.value = "模型构建完成";

    // 直接使用返回的结果数据
    if (data.result) {
      modelResult.value = normalizeKeysDeep(data.result);
      isLoading.value = false;
      console.log(
        "从IPC model_complete事件直接获取到模型结果:",
        modelResult.value,
      );

      // 提示用户模型构建完成
      ElMessage.success({
        message: "模型构建完成",
        duration: 2000,
      });

      // 保持在主界面，但确保isLoading设置为false使结果可见
    } else {
      console.warn("IPC model_complete事件没有包含结果数据");
      // 如果没有结果数据，仍然尝试获取
      fetchModelResult();
    }
  }
};

// 注册事件监听器
const setupEventListeners = () => {
  // 监听DOM事件
  window.addEventListener(
    "model_progress",
    handleModelProgress as EventListener,
  );
  window.addEventListener(
    "model_complete",
    handleModelComplete as EventListener,
  );

  // 监听Electron IPC事件
  if (window.ipcRenderer) {
    window.ipcRenderer.on("model_progress_update", handleIpcModelProgress);
    window.ipcRenderer.on("model_complete", handleIpcModelComplete);
  }
};

// 移除事件监听器
const removeEventListeners = () => {
  window.removeEventListener(
    "model_progress",
    handleModelProgress as EventListener,
  );
  window.removeEventListener(
    "model_complete",
    handleModelComplete as EventListener,
  );

  if (window.ipcRenderer) {
    window.ipcRenderer.off("model_progress_update", handleIpcModelProgress);
    window.ipcRenderer.off("model_complete", handleIpcModelComplete);
  }
};

// 组件销毁前移除事件监听
onBeforeUnmount(() => {
  removeEventListeners();
});

const datasetConfig = computed(() => {
  if (props.resultType === "ml") {
    return {
      order: ["train", "test", "cv", "validation"] as const,
      titles: {
        train: "训练集",
        test: "测试集",
        cv: "交叉验证",
        validation: "验证集",
      },
    };
  } else {
    return {
      order: ["train", "test", "cv"] as const,
      titles: {
        train: "训练集",
        test: "测试集",
        cv: "交叉验证",
      },
    };
  }
});

const customFields = computed(() => [
  {
    key: "duration",
    label: "训练时长",
    formatter: (value: number) => formatDuration(value),
  },
  {
    key: "datasetSize",
    label: "数据集大小",
    formatter: (value: number | string) => formatDatasetSize(value),
  },
]);

const formatDuration = (duration: number) => {
  if (!duration) return "-";
  if (duration < 60) {
    return `${duration.toFixed(2)}秒`;
  } else if (duration < 3600) {
    return `${(duration / 60).toFixed(2)}分钟`;
  } else {
    return `${(duration / 3600).toFixed(2)}小时`;
  }
};
const formatDatasetSize = (size: number | string) => {
  if (typeof size === "number") {
    return `${size.toLocaleString()} 条记录`;
  }
  return size;
};

const getComponent = (key: string) => {
  switch (key) {
    case "metrics":
      return MetricsTable;
    case "charts":
      return ModelCharts;
    case "predictions":
      return PredictionTable;
    case "splits":
      return DataSplitTable;
    case "optimization":
      return OptimizationResultTable;
    case "predict":
      return ModelPrediction;
    default:
      return null;
  }
};

const toggleSection = (key: string) => {
  if (key === "save") {
    handleSaveModel();
    return;
  }
  if (visibleSections.hasOwnProperty(key)) {
    visibleSections[key] = !visibleSections[key];
  }
};

onMounted(() => {
  try {
    // Manually set theme color for the new window
    const storedThemeColor = epThemeStore.getEpThemeColor;
    if (storedThemeColor) {
      document.documentElement.style.setProperty(
        "--el-color-primary",
        storedThemeColor,
      );
      epThemeStore.setEpThemeColor(storedThemeColor);
    }
    // 设置事件监听
    setupEventListeners();

    // 获取查询参数
    const taskUid_param = route.query.taskUid as string | undefined;
    const modelTypeParam = route.query.modelType as string | undefined;

    console.log("Route query params:", {
      taskUid: taskUid_param,
      modelType: modelTypeParam,
    });

    // 先检查路由query参数中的taskUid
    if (taskUid_param) {
      taskUid.value = taskUid_param;
    }

    // 检查路由参数中的modelType
    if (modelTypeParam) {
      modelType.value = modelTypeParam;
    }

    // 如果URL参数不可用，尝试从URL搜索参数获取
    if (!taskUid.value) {
      const url = new URL(window.location.href);

      // 尝试获取taskUid，兼容各种参数名
      const urlTaskUid = url.searchParams.get("taskUid");
      if (urlTaskUid) {
        taskUid.value = urlTaskUid;
      }

      if (!modelType.value) {
        modelType.value = url.searchParams.get("modelType") || null;
      }
    }

    console.log("处理后的参数:", {
      taskUid: taskUid.value,
      modelType: modelType.value,
    });

    // 注册窗口加载事件到Electron
    if (window.ipcRenderer && taskUid.value) {
      window.ipcRenderer.send("model_result_window_ready", {
        taskUid: taskUid.value,
        modelType: modelType.value,
      });
    }

    if (taskUid.value) {
      // 如果只有taskUid，则显示进度条并等待构建完成
      console.log("Starting progress tracking for taskUid:", taskUid.value);

      buildStatus.value = "waiting";
      buildStatusMessage.value = "等待模型构建开始...";

      // 保持isLoading为true，显示进度条
      isLoading.value = true;

      // 监听socket，如果已连接且任务已完成，尝试获取结果
      const socket = getSocket();
      if (socket && socket.connected) {
        socket.emit(
          "check_task_status",
          { taskUid: taskUid.value },
          (response: any) => {
            console.log("Task status check result:", response);

            if (response && response.status === "completed") {
              // 如果任务已完成，直接获取结果
              fetchModelResult();
            } else if (response && response.status) {
              // 如果任务存在但未完成，显示其状态
              buildStatus.value = response.status;
              if (response.progress) {
                progress.value = response.progress;
              }
              if (response.message) {
                buildStatusMessage.value = response.message;
              }
            }
          },
        );
      }
    } else {
      ElMessage.warning("未找到模型结果数据或任务ID");
      console.error("未找到有效的taskUid或结果数据", {
        taskUid: taskUid.value,
      });
      isLoading.value = false;
    }
  } catch (err) {
    ElMessage.error("数据加载失败: " + (err as Error).message);
    console.error("Error loading model result:", err);
    isLoading.value = false;
  }
});
</script>

<style lang="scss" scoped>
.model-result-page {
  height: 100vh;
  background: #f8f9fa;
  display: flex;
  flex-direction: column;
  -webkit-app-region: none;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.15);
}

.custom-titlebar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 50px;
  padding: 0;
  background-color: #fff;
  -webkit-app-region: drag;
  border-bottom: 1px solid #e4e7ed;

  .title-left {
    display: flex;
    align-items: center;
    padding-left: 20px;
    height: 100%;
    h3 {
      font-size: 16px;
      font-weight: 600;
      margin: 0;
      color: #303133;
    }
  }

  .title-right {
    display: flex;
    align-items: center;
    height: 100%;
    -webkit-app-region: no-drag;
  }

  .control-container {
    height: 100%;
    width: 124px;
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 24.5px 0 0 24.5px;
    background-color: v-bind(epThemeColor);
    -webkit-app-region: no-drag;
  }

  .window-controls {
    display: flex;
    color: white;
    -webkit-app-region: no-drag;
    height: 100%;
    align-items: center;
    justify-content: center;
  }

  .control-btn {
    padding: 0 10px;
    cursor: pointer;
    line-height: 30px;
    height: 30px;
    -webkit-app-region: no-drag;
    display: flex;
    align-items: center;
    justify-content: center;
    
    &:hover {
      border-radius: 8px;
      background-color: rgba(255, 255, 255, 0.2);
    }

    .control-icon {
      width: 10px;
      height: 10px;
      display: inline-block;
      filter: brightness(0) invert(1); /* 将图标转为白色 */
    }
  }

  .close-btn:hover {
    background-color: #e81123;
  }
}

/* 深色模式适配 */
html.dark .custom-titlebar {
  background-color: var(--el-bg-color);
  border-bottom: 1px solid var(--el-border-color);
  
  h3 {
    color: var(--el-text-color-primary);
  }
}

/* 添加内容容器，提供统一的内边距 */
.content-container {
  flex: 1;
  padding: 20px;
  display: flex;
  flex-direction: column;
  min-height: 0; /* 修复flexbox溢出问题 */
}

.model-result-layout {
  display: flex;
  flex-direction: column;
  flex: 1;
  min-height: 0;
}

.page-header {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
  
  .el-button {
    font-size: 16px;
  }

  .page-title {
    font-size: 18px;
    font-weight: 600;
    color: var(--el-text-color-primary);
    margin-left: 8px;
  }
}

.info-alert {
  margin-top: 20px;
}

.model-static-info {
  margin: 0 0 20px;
}

.result-card {
  background: #ffffff;
  border-radius: 8px;
  box-shadow: 0px 0px 20px 0px rgba(61, 61, 61, 0.08);
  margin: 20px 15px;
  overflow: hidden;

  .result-card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 20px;
    transition: background-color 0.3s;

    &:hover {
      background-color: #fafcfe;
    }
  }

  .result-card-header.is-clickable {
    cursor: pointer;
  }

  .header-left {
    display: flex;
    align-items: center;
    gap: 12px;
  }

  .header-icon {
    font-size: 24px;
    color: var(--el-color-primary);
  }

  .header-text {
    .header-title {
      font-size: 16px;
      font-weight: 600;
      color: var(--el-text-color-primary);
    }
    .header-desc {
      font-size: 13px;
      color: var(--el-text-color-regular);
      margin-top: 4px;
    }
  }

  .expand-btn {
    font-size: 14px;
    background: #f9fbff;
    .el-icon--right {
      margin-left: 4px;
      transition: transform 0.3s;
    }
    .el-icon--right.is-active {
      transform: rotate(180deg);
    }
  }

  .result-card-body {
    padding: 20px;
    border-top: none;
  }
}

/* 添加详情视图容器样式 */
.detail-view-container {
  background-color: #fff;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

/* 深色模式适配 */
html.dark .detail-view-container {
  background-color: var(--el-bg-color);
}
html.dark .page-header .page-title {
  color: var(--el-text-color-primary);
}
html.dark .result-card {
  background-color: var(--el-bg-color-overlay);
  border-color: var(--el-border-color-light);

  .result-card-header:hover {
    background-color: var(--el-fill-color-darker);
  }

  .result-card-body {
    border-top-color: var(--el-border-color-light);
  }
}


.result-entry-list {
  margin-top: 20px;
}

.entry-row {
  row-gap: 20px;
}

.entry-card {
  cursor: pointer;

  // Most styles are now in .model-card global class
  .entry-title {
    font-size: 20px;
    font-weight: 600;
    margin-bottom: 12px;
    color: #303133;
    display: flex;
    align-items: center;
    gap: 8px;

    .el-icon {
      font-size: 24px;
      color: #409eff;
    }
  }

  .entry-desc {
    color: #909399;
    font-size: 14px;
    line-height: 1.5;
    font-weight: 500;
  }
}

.sub-header {
  display: flex;
  align-items: center;
  background: white;
  padding: 16px 20px;
  border-radius: 12px;
  margin: 0 0 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  border: 1px solid #f0f0f0;

  .el-button {
    font-weight: 500;
    color: #409eff;

    &:hover {
      color: #66b1ff;
    }
  }

  .sub-title {
    display: flex;
    align-items: center;
    margin-left: 8px;
    font-size: 1.125rem; /* 18px */
    line-height: 1.75rem; /* 28px */
    font-weight: 500;
    color: #303133;

    .el-icon {
      margin-right: 4px;
      color: #409eff;
    }
  }
}

.empty-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400px;
}

.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400px;
}

.loading-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
  width: 100%;
  max-width: 400px;
  padding: 32px;
  text-align: center;
}

.loading-icon {
  width: 138px;
  height: 138px;
  margin-left: 55px;
}

.loading-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: #303133;
}

.progress-wrapper {
  width: 100%;
  display: flex;
  align-items: center;
}

.progress-percentage {
  font-size: 14px;
  font-weight: 600;
  color: var(--el-color-primary);
  min-width: 40px;
  text-align: left;
}

.progress-bar {
  flex-grow: 1;
}

.build-status {
  color: #909399;
  font-size: 14px;
}

.build-progress {
  width: 80%;
  max-width: 800px;
  padding: 32px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
  text-align: center;

  h2 {
    margin-bottom: 24px;
    font-size: 1.5rem;
    color: #303133;
  }

  .el-progress {
    margin-bottom: 20px;
  }

  .build-status {
    margin-top: 16px;
    color: #606266;
    font-size: 1rem;
  }
}

/* 深色模式适配 */
html.dark .sub-header {
  background-color: var(--el-bg-color);
  border: 1px solid var(--el-border-color-light);
}

html.dark .loading-title {
  color: var(--el-text-color-primary);
}

html.dark .build-status {
  color: var(--el-text-color-regular);
}

html.dark .model-result-layout {
  background-color: var(--el-bg-color);
}

html.dark .results-scroll-container {
  background-color: var(--el-bg-color);
}

.results-scroll-container {
  flex: 1;
  overflow-y: auto;
  /* 滚动条整体部分 */
  &::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }
  /* 滚动条滑块 */
  &::-webkit-scrollbar-thumb {
    border-radius: 3px;
    background-color: rgba(0, 0, 0, 0.1);
    transition: all 0.2s;
    &:hover {
      background-color: rgba(0, 0, 0, 0.2);
    }
  }
  /* 滚动条轨道 */
  &::-webkit-scrollbar-track {
    background: transparent;
  }
}

@media (max-width: 768px) {
  .model-result-page {
    padding: 0;
  }

  .content-container {
    padding: 12px;
  }

  .result-entry-list {
    padding: 0;
  }

  .entry-card :deep(.el-card__body) {
    padding: 16px;
  }

  .entry-title {
    font-size: 18px;
  }

  .sub-header {
    padding: 12px 16px;
    margin: 0 0 16px;
  }

  .build-progress {
    width: 95%;
    padding: 20px;

    h2 {
      font-size: 1.25rem;
      margin-bottom: 16px;
    }
  }
}
</style>
