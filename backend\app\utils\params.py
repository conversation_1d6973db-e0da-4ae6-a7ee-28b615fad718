import re
from typing import Union


def camel_to_snake(data) -> Union[dict, list]:
    if isinstance(data, dict):
        return {re.sub(r'(?<!^)(?=[A-Z])', '_', k).lower(): camel_to_snake(v) for k, v in data.items()}
    elif isinstance(data, list):
        return [camel_to_snake(item) for item in data]
    return data

def snake_to_camel(data):
    if isinstance(data, dict):
        return {''.join([k.split('_')[0]] + [x.capitalize() for x in k.split('_')[1:]]): snake_to_camel(v) for k, v in data.items()}
    elif isinstance(data, list):
        return [snake_to_camel(item) for item in data]
    return data