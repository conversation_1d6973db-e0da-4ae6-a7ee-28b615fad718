// @see https://www.electron.build/configuration/configuration
{
  $schema: "https://raw.githubusercontent.com/electron-userland/electron-builder/master/packages/app-builder-lib/scheme.json",
  appId: "com.pure.electron",
  productName: "固体推进剂配方智能设计软件",
  copyright: "Copyright © 2025-present, 中国航天科技集团有限公司第四研究院四十二所",
  publish: {
    provider: "github",
    releaseType: "release",
  },
  directories: {
    buildResources: "dist",
    output: "release/${version}",
  },
  files: ["dist-electron/**", "dist/**"],
  extraResources: [
    {
      from: "../backend/dist/backend",
      to: "backend",
    },
    {
      from: "../resources/otp",
      to: "resources/otp",
    },
    {
      from: "../resources/rabbitmq",
      to: "resources/rabbitmq",
    },
    {
      from: "../start_rabbitmq.bat",
      to: "start_rabbitmq.bat",
    },
    {
      from: "../monitor_rabbitmq.bat",
      to: "monitor_rabbitmq.bat",
    },
  ],
  nsis: {
    allowToChangeInstallationDirectory: true,
    createDesktopShortcut: true,
    createStartMenuShortcut: true,
    shortcutName: "pure-admin",
    perMachine: true,
    oneClick: false,
  },
  mac: {
    icon: "dist/icons/mac/icon.icns",
    artifactName: "${productName}_${version}.${ext}",
    target: ["dmg"],
  },
  win: {
    icon: "dist/icons/win/icon.ico",
    artifactName: "${productName}_${version}.${ext}",
    target: [
      {
        target: "nsis",
        arch: ["x64"],
      },
    ],
  },
  linux: {
    icon: "dist/icons/png",
    artifactName: "${productName}_${version}.${ext}",
    target: ["deb", "AppImage"],
  },
}
