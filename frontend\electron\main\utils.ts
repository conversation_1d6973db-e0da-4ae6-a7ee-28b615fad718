import { exec } from "node:child_process";

// 检查是否有运行中的runserver.exe进程，若有则返回进程ID，否则返回空数组
export async function checkExistingBackendProcesses(
  processName: string = "runserver.exe",
): Promise<number[]> {
  return new Promise((resolve) => {
    // 暂只检测windows平台，若需要检测其他平台，请修改此处的判断条件
    if (process.platform !== "win32") {
      resolve([]);
      return;
    }

    // wmic命令用于获取windows系统中的进程信息
    // 检测进程名为runserver.exe的进程ID
    const cmd = `wmic process where "name='${processName}'" get processid`;

    exec(cmd, (error, stdout) => {
      if (error) {
        console.log(
          "No existing backend processes found or error running command",
        );
        resolve([]);
        return;
      }

      // 解析进程ID
      const pidPattern = /(\d+)/g;
      const pids: number[] = [];
      let match;

      while ((match = pidPattern.exec(stdout)) !== null) {
        const pid = parseInt(match[1], 10);
        if (!isNaN(pid) && pid !== process.pid) {
          pids.push(pid);
        }
      }

      console.log(`Found ${pids.length} existing backend processes:`, pids);
      resolve(pids);
    });
  });
}

// 检查是否有运行中的RabbitMQ进程
// rmq启动方式为rabbitmq-server.bat，该bat文件会启动erl.exe和epmd.exe
export async function checkExistingRabbitMQProcesses(): Promise<number[]> {
  return new Promise((resolve) => {
    if (process.platform !== "win32") {
      resolve([]);
      return;
    }

    const cmd =
      "wmic process where \"name='erl.exe' or name='epmd.exe'\" get processid,name";

    exec(cmd, (error, stdout) => {
      if (error) {
        console.log(
          "No existing RabbitMQ processes found or error running command",
        );
        resolve([]);
        return;
      }

      // 解析进程ID和名称
      const processLines = stdout.trim().split("\n").slice(1); // 跳过标题行
      const pids: number[] = [];

      for (const line of processLines) {
        const match = line.trim().match(/(\d+)\s+(.+)/);
        if (match) {
          const pid = parseInt(match[1], 10);
          const name = match[2].trim();
          if (!isNaN(pid) && pid !== process.pid) {
            console.log(
              `Found RabbitMQ related process: ${name} (PID: ${pid})`,
            );
            pids.push(pid);
          }
        }
      }

      console.log(`Found ${pids.length} existing RabbitMQ processes`);
      resolve(pids);
    });
  });
}
