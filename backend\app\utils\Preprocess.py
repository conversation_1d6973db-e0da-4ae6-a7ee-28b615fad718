import pandas as pd
from sklearn.impute import SimpleImputer
from sklearn.ensemble import IsolationForest
from sklearn.neighbors import LocalOutlierFactor
from sklearn.svm import OneClassSVM
class DropNA(object):
    def __init__(self, **kwargs):
        self.X = None
        self.Y = None

    def fit(self, X, Y=None):
        self.X = X
        self.X = self.X.dropna(axis=0, how="any")

    def transform(self, X=None):
        return self.X.values

class BFill(object):
    def __init__(self, **kwargs):
        self.X = None
        self.Y = None

    def fit(self, X, Y=None):
        self.X = X
        self.X = self.X.bfill()
        self.X = self.X.ffill()

    def transform(self, X=None):
        return self.X.values

class FFill(object):
    def __init__(self, **kwargs):
        self.X = None
        self.Y = None

    def fit(self, X, Y=None):
        self.X = X
        self.X = self.X.ffill()
        self.X = self.X.bfill()

    def transform(self, X=None):
        return self.X.values

class Interpolation(object):
    def __init__(self, **kwargs):
        self.X = None
        self.Y = None

    def fit(self, X, Y=None):
        self.X = X
        self.X = self.X.interpolate(method="linear")
        self.X = self.X.bfill()
        self.X = self.X.ffill()

    def transform(self, X=None):
        return self.X.values

FILL_ALGORITHM = {
    "DropNA": {
        "model": DropNA,
        "model_params": {},
        "model_params_type": {},
    },
    "SimpleImputer": {
        "model": SimpleImputer,
        "model_params": {
            "strategy": "mean",
            "fill_value": None,
        },
        "model_params_type": {
            "strategy": "str",
            "fill_value": "float",
        },
    },
    "BFill": {
        "model": BFill,
        "model_params": {},
        "model_params_type": {},
    },
    "FFill": {
        "model": FFill,
        "model_params": {},
        "model_params_type": {},
    },
    "Interpolation": {
        "model": Interpolation,
        "model_params": {},
        "model_params_type": {},
    },
}

OUTLIER_ALGORITHM = {
    "LocalOutlierFactor": {
        "model": LocalOutlierFactor,
        "model_params": {},
        "model_params_type": {},
    },
    "IsolationForest": {
        "model": IsolationForest,
        "model_params": {},
        "model_params_type": {},
    },
    "OneClassSVM": {
        "model": OneClassSVM,
        "model_params": {},
        "model_params_type": {},
    },
}

class Preprocess(object):
    def __init__(self, X, Y):
        self.X = X.apply(pd.to_numeric, errors="coerce")
        self.Y = Y

    def fill(self, preprocess_name: str, preprocess_params: dict):
        preprocess_algorithm = FILL_ALGORITHM.get(preprocess_name)
        params = preprocess_algorithm.get("model_params")
        params.update(preprocess_params)
        if "fill_value" in params:
            params["fill_value"] = pd.to_numeric(params["fill_value"], errors="coerce")
        preprocessor = preprocess_algorithm.get("model")(**params)
        preprocessor.fit(self.X.copy(), self.Y)
        if preprocess_name == "DropNA":
            self.X = pd.DataFrame(preprocessor.transform(self.X), columns=self.X.columns)
        else:
            self.X[:] = preprocessor.transform(self.X)
        return self.X

    def outlier(self, preprocess_name: str, preprocess_params: dict):
        preprocess_algorithm = OUTLIER_ALGORITHM.get(preprocess_name)
        params = preprocess_algorithm.get("model_params")
        params.update(preprocess_params)
        preprocessor = preprocess_algorithm.get("model")(**params)
        labels = preprocessor.fit_predict(self.X.copy(), self.Y)
        return labels

        

