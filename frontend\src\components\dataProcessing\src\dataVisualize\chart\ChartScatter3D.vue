<template>
  <div class="chart-content" ref="chartRef"></div>
</template>

<script setup lang="ts">
import { ref, onMounted, onBeforeUnmount, watch } from "vue";
import * as echarts from "echarts";
import type { ECharts } from "echarts";

const props = defineProps<{
  chartData: any[][];
  columns: string[];
  selectedColumns: string[];
}>();

const chartRef = ref<HTMLElement | null>(null);
let chartInstance: ECharts | null = null;

// 导出函数，供父组件调用
const getChartInstance = () => chartInstance;
const getChartOptions = () => create3DScatterOption();
const getChartData = () => {
  if (props.selectedColumns.length < 3) return null;
  
  const xIndex = props.columns.indexOf(props.selectedColumns[0]);
  const yIndex = props.columns.indexOf(props.selectedColumns[1]);
  const zIndex = props.columns.indexOf(props.selectedColumns[2]);
  
  return {
    columns: [props.selectedColumns[0], props.selectedColumns[1], props.selectedColumns[2]],
    data: props.chartData.map(row => {
      const x = parseFloat(row[xIndex]);
      const y = parseFloat(row[yIndex]);
      const z = parseFloat(row[zIndex]);
      return [isNaN(x) ? 0 : x, isNaN(y) ? 0 : y, isNaN(z) ? 0 : z];
    }).filter(item => item[0] !== null && item[1] !== null && item[2] !== null)
  };
};
defineExpose({ getChartInstance, getChartOptions, getChartData });

// 创建3D散点图
const create3DScatterOption = () => {
  // 需要至少3列数据才能创建3D散点图
  if (props.selectedColumns.length < 3) return {};
  
  const xIndex = props.columns.indexOf(props.selectedColumns[0]);
  const yIndex = props.columns.indexOf(props.selectedColumns[1]);
  const zIndex = props.columns.indexOf(props.selectedColumns[2]);
  
  const data = props.chartData.map(row => {
    const x = parseFloat(row[xIndex]);
    const y = parseFloat(row[yIndex]);
    const z = parseFloat(row[zIndex]);
    return [isNaN(x) ? 0 : x, isNaN(y) ? 0 : y, isNaN(z) ? 0 : z];
  }).filter(item => item[0] !== null && item[1] !== null && item[2] !== null);
  
  return {
    tooltip: {},
    grid3D: {
      viewControl: {
        autoRotate: true,
        autoRotateSpeed: 10
      }
    },
    xAxis3D: {
      name: props.selectedColumns[0],
      type: 'value'
    },
    yAxis3D: {
      name: props.selectedColumns[1],
      type: 'value'
    },
    zAxis3D: {
      name: props.selectedColumns[2],
      type: 'value'
    },
    series: [{
      type: 'scatter3D',
      data: data,
      symbolSize: 8,
      itemStyle: {
        color: '#005DFF'
      }
    }]
  };
};

// 初始化图表
const initChart = () => {
  if (chartRef.value) {
    chartInstance = echarts.init(chartRef.value);
    updateChart();
  }
};

// 更新图表
const updateChart = () => {
  if (chartInstance) {
    const option = create3DScatterOption();
    chartInstance.setOption(option);
  }
};

// 监听数据变化，更新图表
watch(() => props.chartData, updateChart, { deep: true });
watch(() => props.selectedColumns, updateChart, { deep: true });

// 窗口大小变化时，重新调整图表大小
const handleResize = () => {
  if (chartInstance) {
    chartInstance.resize();
  }
};

onMounted(() => {
  initChart();
  window.addEventListener('resize', handleResize);
});

onBeforeUnmount(() => {
  if (chartInstance) {
    chartInstance.dispose();
    chartInstance = null;
  }
  window.removeEventListener('resize', handleResize);
});
</script>

<style scoped>
.chart-content {
  width: 100%;
  height: 100%;
}
</style> 