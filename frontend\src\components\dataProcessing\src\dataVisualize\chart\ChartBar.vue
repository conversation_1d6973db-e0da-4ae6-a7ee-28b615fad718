<template>
  <div class="chart-content" ref="chartRef"></div>
</template>

<script setup lang="ts">
import { ref, onMounted, onBeforeUnmount, watch } from "vue";
import * as echarts from "echarts";
import type { ECharts } from "echarts";

const props = defineProps<{
  chartData: any[][];
  columns: string[];
  selectedColumns: string[];
}>();

const chartRef = ref<HTMLElement | null>(null);
let chartInstance: ECharts | null = null;

// 导出函数，供父组件调用
const getChartInstance = () => chartInstance;
const getChartOptions = () => createBarOption();
const getChartData = () => {
  if (props.selectedColumns.length === 0) return null;
  
  const xIndex = props.columns.indexOf(props.selectedColumns[0]);
  
  // 统计值的频率
  const valueMap = new Map();
  props.chartData.forEach(row => {
    const value = row[xIndex];
    if (value !== null && value !== undefined) {
      if (!valueMap.has(value)) {
        valueMap.set(value, 0);
      }
      valueMap.set(value, valueMap.get(value) + 1);
    }
  });
  
  // 取频率最高的20个值
  const sortedEntries = [...valueMap.entries()]
    .sort((a, b) => b[1] - a[1])
    .slice(0, 20);
  
  return {
    columns: [props.selectedColumns[0], '频率'],
    data: sortedEntries.map(entry => [entry[0], entry[1]])
  };
};
defineExpose({ getChartInstance, getChartOptions, getChartData });

// 创建柱状图
const createBarOption = () => {
  if (props.selectedColumns.length === 0) return {};
  
  // 始终使用第一个选择的列作为数据源
  const xIndex = props.columns.indexOf(props.selectedColumns[0]);
  
  // 使用值的频率统计作为柱状图，无论选择了几个列
  // 统计前20个值的频率
  const valueMap = new Map();
  props.chartData.forEach(row => {
    const value = row[xIndex];
    if (value !== null && value !== undefined) {
      if (!valueMap.has(value)) {
        valueMap.set(value, 0);
      }
      valueMap.set(value, valueMap.get(value) + 1);
    }
  });
  
  // 取频率最高的20个值
  const sortedEntries = [...valueMap.entries()]
    .sort((a, b) => b[1] - a[1])
    .slice(0, 20);
  
  const xData = sortedEntries.map(entry => entry[0]);
  const yData = sortedEntries.map(entry => entry[1]);
  
  return {
    grid: {
      left: '10%',
      right: '10%',
      bottom: '15%',
      top: '10%'  // 为顶部图例提供更多空间
    },
    tooltip: {
      trigger: 'axis'
    },
    xAxis: {
      type: 'category',
      data: xData,
      name: props.selectedColumns[0],
      nameLocation: 'middle',
      nameGap: 30,
      axisLabel: {
        interval: 0,
        rotate: 45
      }
    },
    yAxis: {
      type: 'value',
      name: '频率',
      nameLocation: 'middle',
      nameGap: 30
    },
    series: [{
      type: 'bar',
      data: yData,
      itemStyle: {
        color: '#005DFF'
      }
    }]
  };
};

// 初始化图表
const initChart = () => {
  if (chartRef.value) {
    chartInstance = echarts.init(chartRef.value);
    updateChart();
  }
};

// 更新图表
const updateChart = () => {
  if (chartInstance) {
    const option = createBarOption();
    chartInstance.setOption(option);
  }
};

// 监听数据变化，更新图表
watch(() => props.chartData, updateChart, { deep: true });
watch(() => props.selectedColumns, updateChart, { deep: true });

// 窗口大小变化时，重新调整图表大小
const handleResize = () => {
  if (chartInstance) {
    chartInstance.resize();
  }
};

onMounted(() => {
  initChart();
  window.addEventListener('resize', handleResize);
});

onBeforeUnmount(() => {
  if (chartInstance) {
    chartInstance.dispose();
    chartInstance = null;
  }
  window.removeEventListener('resize', handleResize);
});
</script>

<style scoped>
.chart-content {
  width: 100%;
  height: 100%;
}
</style> 