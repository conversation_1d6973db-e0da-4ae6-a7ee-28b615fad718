<script setup lang="ts">
import { ref } from "vue";

const icons = ref([
  { icon: "folder", name: "Workspace" },
  { icon: "appstore", name: "A<PERSON>" },
  { icon: "setting", name: "<PERSON><PERSON><PERSON>" }
]);
</script>

<template>
  <div class="icon-sidebar">
    <div v-for="item in icons" :key="item.name" class="icon-item">
      <font-icon :icon="item.icon" />
    </div>
  </div>
</template>

<style lang="scss" scoped>
.icon-sidebar {
  width: 50px;
  height: 100%;
  background-color: #f0f2f5;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding-top: 20px;
  border-right: 1px solid #e0e0e0;
}

.icon-item {
  margin-bottom: 25px;
  cursor: pointer;
  font-size: 24px;
  color: #555;

  &:hover {
    color: #409eff;
  }
}
</style> 