from app.types.model_request import ModelConfig
from app.utils.Regression import reg_model_config
from typing import Optional, Tuple, Any, Union
import re
from sklearn.preprocessing import StandardScaler
from app.utils.eval import cal_reg_metric
from sklearn.model_selection import KFold, LeaveOneOut
import numpy as np
import pandas as pd
import time
from collections import OrderedDict

def check_alg_param(alg_name: str, alg_param: Optional[dict]) -> Tuple[Any, dict]:
    # check params
    if alg_name not in reg_model_config.keys():
        raise ValueError("Invalid algorithm name")
    alg = reg_model_config[alg_name]["model"]
    params = reg_model_config[alg_name]["model_params"].copy()
    if alg_param is not None:
        for key in alg_param.keys():
            params[key] = alg_param[key]

    # SVM 特殊处理
    if alg_name == "SVR":
        if 'c' in params.keys():
            C = params.pop('c')
            params['C'] = C

    # MLPRegressor 特殊处理
    if alg_name == "MLPRegressor":
        hidden_layer_sizes = re.findall(r'\d+', params['hidden_layer_sizes'])
        params['hidden_layer_sizes'] = tuple(int(size) for size in hidden_layer_sizes)
    
    return alg, params


def check_params_type(params: Union[dict, OrderedDict]) -> Union[dict, OrderedDict]:
    for k, v in params.items():
        if k in [
            "n_estimators", 
            "n_neighbors", 
            "n_jobs", 
            "n_splits", 
            "n_components", 
            "n_features", 
            "n_features_out", 
            "n_iter", 
            "max_iter", 
            "random_state", 
            "max_depth",
            "min_samples_leaf",
            "min_samples_split",
            "degree"
            ]:
            if isinstance(v, list):
                params[k] = [int(size) for size in v]
            else:
                params[k] = int(v) if v is not None else None
        else:
            params[k] = v
    return params

class RegressionService(object):

    def __init__(self, model_config: ModelConfig, report=None, update=None):
        self.x_train = model_config.x_train
        self.y_train = model_config.y_train
        self.x_test = model_config.x_test
        self.y_test = model_config.y_test
        self.cv = model_config.cv
        self.loocv = model_config.loocv
        self.test = model_config.test
        self.optimize = model_config.optimize
        self.alg_name = model_config.alg_name
        self.alg_param = model_config.alg_param
        self.is_rabbitmq_ready = model_config.is_rabbitmq_ready
        self.asynchronous = model_config.asynchronous
        self.task_uid = model_config.task_uid
        self.feature_names = model_config.x_train.columns.tolist()
        self.y_train_name = model_config.y_train.name

        self.model = None
        self.info = {
            "task_type": "reg",
            "alg": self.alg_name,
            "model_params": model_config.to_dict(),
            "optimized_params": None,
            "optimized_result": None,
            "eval": {
                "train": {
                    "y_true": None,
                    "y_predict": None,
                    "metrics": None
                },
                "cv": {
                    "y_true": None,
                    "y_predict": None,
                    "metrics": None
                },
                "loocv": {
                    "y_true": None,
                    "y_predict": None,
                    "metrics": None
                },
                "test": {
                    "y_true": None,
                    "y_predict": None,
                    "metrics": None
                },
            }
        }
        self.optimized_result = None
        self.optimized_params = None

        self.report = report
        self.update = update
        self.alg, self.params = check_alg_param(self.alg_name, self.alg_param)
        if not self.optimize: self.params = check_params_type(self.params)
        self.scaler = StandardScaler().fit(self.x_train)

        self.step_count = np.array([self.optimize, self.cv, self.loocv, self.test]).astype(int).sum() + 1
    
    @property
    def _params(self) -> dict:
        if self.optimize and (self.optimized_params is not None): 
            return self.optimized_params
        elif not self.optimize:
            return self.params
        else:
            raise ValueError("Invalid parameter")

    def _train(self, params: dict) -> None:
        x_train = self.scaler.transform(self.x_train)
        self.model = self.alg(**params).fit(x_train, self.y_train)
        predictions = self.model.predict(x_train)
        self.info["eval"]["train"]["metrics"] = cal_reg_metric(self.y_train, predictions)
        self.info["eval"]["train"]["y_true"] = self.y_train.tolist()
        self.info["eval"]["train"]["y_predict"] = predictions.tolist()

    def _test(self) -> None:
        x_test = self.scaler.transform(self.x_test)
        if self.model is not None: 
            predictions = self.model.predict(x_test)
        self.info["eval"]["test"]["metrics"] = cal_reg_metric(self.y_test, predictions)
        self.info["eval"]["test"]["y_true"] = self.y_test.tolist() if self.y_test is not None else None
        self.info["eval"]["test"]["y_predict"] = predictions.tolist()

    def predict(self, x: pd.DataFrame) -> float:
        x = self.scaler.transform(x)
        if self.model is None:
            self._train(self._params)
        return self.model.predict(x)

    def _leave_one_out(self, params: dict, progress: str) -> None:
        loo = LeaveOneOut()
        predictions = []
        progress_float = float(progress)
        for train_index, test_index in loo.split(self.x_train):
            sub_progress = f"{len(predictions)+1}/{self.x_train.shape[0]}"
            sub_percent = len(predictions)/self.x_train.shape[0]*(1/self.step_count)*100
            if self.report: self.report(self.task_uid, str(progress_float+sub_percent), "running", f"留一法交叉验证中:{sub_progress}")
            time.sleep(0.01)
            xtrain, ytrain = self.x_train.iloc[train_index], self.y_train.iloc[train_index]
            xtest, ytest = self.x_train.iloc[test_index], self.y_train.iloc[test_index]
            xtrain = self.scaler.transform(xtrain)
            xtest = self.scaler.transform(xtest)
            model = self.alg(**params).fit(xtrain, ytrain)
            predictions.append(model.predict(xtest).astype(float)[0])
        self.info["eval"]["cv"]["metrics"] = cal_reg_metric(self.y_train, predictions)
        self.info["eval"]["cv"]["y_true"] = self.y_train.tolist()
        self.info["eval"]["cv"]["y_predict"] = predictions

    def _cross_validation(self, params: dict, output=False) -> Union[dict, None]:
        cv = self.cv or 5
        kf = KFold(n_splits=cv, shuffle=True, random_state=42)
        predictions = []
        obs = []
        for train_index, test_index in kf.split(self.x_train):
            xtrain, ytrain = self.x_train.iloc[train_index], self.y_train.iloc[train_index]
            xtest, ytest = self.x_train.iloc[test_index], self.y_train.iloc[test_index]
            xtrain = self.scaler.transform(xtrain)
            xtest = self.scaler.transform(xtest)
            model = self.alg(**params).fit(xtrain, ytrain)
            predictions += model.predict(xtest).astype(float).tolist()
            obs += ytest.tolist()
        predictions = np.array(predictions)
        obs = np.array(obs)
        if output:
            return cal_reg_metric(obs, predictions)
        else:
            self.info["eval"]["cv"]["metrics"] = cal_reg_metric(obs, predictions)
            self.info["eval"]["cv"]["y_true"] = obs.tolist()
            self.info["eval"]["cv"]["y_predict"] = predictions.tolist()

    def _optimize(self, progress: str) -> None:
        params = {}
        other_params = {}
        total_length = 1
        for k, v in self.params.items():
            if isinstance(v, list) and len(v) > 1:
                if isinstance(v[0], str) and v[0].replace("_", "").isalpha():
                    params[k] = v
                    total_length *= len(v)
                elif len(v) == 2:
                    step = int((v[1]-v[0])/(v[1]*0.1))+1
                    params[k] = np.linspace(v[0], v[1], step, endpoint=True)
                    total_length *= step
                elif len(v) == 3:
                    step = int((v[1]-v[0])/v[2])+1
                    params[k] = np.linspace(v[0], v[1], step, endpoint=True)
                    total_length *= step
                else:
                    raise ValueError(f"Invalid parameter: {k}")
            else:
                other_params[k] = v
        from itertools import product
        results = []
        progress_float = float(progress)
        for i, param_values in enumerate(product(*params.values())):
            sub_progress = f"{i+1}/{total_length}"
            sub_percent = i/total_length*(1/self.step_count)*100
            if self.report: self.report(self.task_uid, str(progress_float+sub_percent), "running", f"参数优化中:{sub_progress}")
            time.sleep(0.1)
            param_dict = OrderedDict(zip(params.keys(), param_values))
            param_dict = check_params_type(param_dict)
            param_dict.update(other_params)
            metrics = self._cross_validation(param_dict, output=True)
            if metrics is not None:
                param_dict.update(metrics)
                results.append(param_dict)
        self.optimized_result = pd.DataFrame(results)
        self.optimized_params = self.optimized_result.iloc[self.optimized_result["MAE"].idxmin()].loc[params.keys()].to_dict()
        self.optimized_params = check_params_type(self.optimized_params)
        self.info["optimized_params"] = self.optimized_params
        self.info["optimized_result"] = self.optimized_result.to_dict(orient="records")

    def fit(self) -> Tuple[object, dict]:
        step_count = 0
        if self.optimize:
            progress_float = step_count/self.step_count*100
            if self.report: self.report(self.task_uid, f"{progress_float:.0f}", "running", "参数优化中")
            if self.update: self.update(self.task_uid, progress="optimize")
            self._optimize(f"{progress_float:.0f}")
            step_count += 1
        if self.report: self.report(self.task_uid, f"{step_count/self.step_count*100:.0f}", "running", "模型训练中")
        if self.update: self.update(self.task_uid, progress="train")
        self._train(self._params)
        step_count += 1
        if self.cv:
            progress_float = step_count/self.step_count*100
            if self.report: self.report(self.task_uid, f"{progress_float:.0f}", "running", "交叉验证中")
            if self.update: self.update(self.task_uid, progress="cv")
            self._cross_validation(self._params)
            step_count += 1
        if self.loocv:
            progress_float = step_count/self.step_count*100
            if self.report: self.report(self.task_uid, f"{progress_float:.0f}", "running", "留一法交叉验证中")
            if self.update: self.update(self.task_uid, progress="loocv")
            self._leave_one_out(self._params, f"{progress_float:.0f}")
            step_count += 1
        if self.test:
            progress_float = step_count/self.step_count*100
            if self.report: self.report(self.task_uid, f"{progress_float:.0f}", "running", "测试集验证中")
            if self.update: self.update(self.task_uid, progress="test")
            self._test()
            step_count += 1
        return {"model": self.model, "scaler": self.scaler, "feature_names": self.feature_names, "y_train_name": self.y_train_name}, self.info
        
