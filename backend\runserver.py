from app import create_app
from app import start_worker_processes
from app.extensions import socketio
import logging, sys, multiprocessing, argparse

app = create_app()

logger = logging.getLogger(__name__)

if __name__ == '__main__':
    multiprocessing.freeze_support()
    
    # parse command line arguments
    parser = argparse.ArgumentParser(description='start Flask server')
    parser.add_argument('--port', '-p', type=int, default=5561, 
                       help='server port (default: 5561)')
    args = parser.parse_args()
    
    port = args.port
    
    # 检测运行环境
    is_frozen = getattr(sys, 'frozen', False)
    logger.info(f'running environment: {"PyInstaller packaged" if is_frozen else "development environment"}')
    
    logger.info(f'Flask application started - project directory: {app.config["PROJECTS_FOLDER"]}')
    logger.info(f'allowed file types: {app.config["ALLOWED_EXTENSIONS"]}')
    logger.info(f'maximum file size: {app.config["MAX_CONTENT_LENGTH"]/1024/1024}MB')
    logger.info(f'start server http://localhost:{port}/')
    
    start_worker_processes(app)
    try:
        socketio.run(
            app, 
            host="0.0.0.0", 
            port=port, 
            debug=True, 
            allow_unsafe_werkzeug=True,
            use_reloader=False,
        )
    except (Exception, AssertionError) as e:
        logger.error(f"error occurred while starting the server: {e}")
        import traceback
        logger.error(f"detailed error information: {traceback.format_exc()}")
        raise