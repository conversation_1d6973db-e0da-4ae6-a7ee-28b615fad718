from flask import Blueprint, request, make_response, current_app, jsonify
import logging
from app.utils.params import camel_to_snake, snake_to_camel
from app.types.model_request import ModelRequest, ModelConfig
from app.routers.models.utils import check_model_request
from app.services.DBService import DBService
from app.types.services import ModelTask
from app.services.TaskService import TaskService
from typing import cast
from app.types.flaskapp import FlaskWithExecutor

linear_bp = Blueprint("linear", __name__)
logger = logging.getLogger()

@linear_bp.route("/build", methods=["POST"])
def linear_build(): 
    post_data: ModelRequest = camel_to_snake(request.json) # type: ignore
    try:
        model_config: ModelConfig = check_model_request(post_data)
        model_task: ModelTask = DBService.create_model_task(
            task_uid=model_config.task_uid, 
            name=linear_bp.name, 
            params=model_config
        )
        DBService.add_model_task(model_task=model_task)
        logger.info(f"linear_build: 添加模型任务: {model_task.task_uid}")

        TaskService.submit_model_task(model_config)
        return_data = {
            "task_uid": model_task.task_uid,
            "asynchronous": model_config.asynchronous,
            "is_rabbitmq_ready": cast(FlaskWithExecutor, current_app).is_rabbitmq_ready
        }
        return make_response(jsonify({"code": 200, "msg": "success", "data": snake_to_camel(return_data)}), 200)
    except Exception as e:
        logger.error(f"linear_build: 添加模型任务失败: {e}")
        return make_response(jsonify({"code": 500, "msg": f"添加模型任务失败: {e}", "data": None}), 500)

@linear_bp.route("/model_info", methods=["POST"])
def model_info():
    post_data: dict = camel_to_snake(request.json) # type: ignore
    task_uid = post_data.get("task_uid")
    if task_uid is not None:
        model_info = DBService.get_model_info(task_uid)
        return make_response(jsonify({"code": 200, "msg": "success", "data": model_info}), 200)
    else:
        return make_response(jsonify({"code": 400, "msg": "invalid task_uid", "data": None}), 400)
