# -*- mode: python ; coding: utf-8 -*-
import sys ; sys.setrecursionlimit(sys.getrecursionlimit() * 5)
block_cipher = None


a = Analysis(['runserver.py'],
             pathex=['dist'],
             binaries=[
                 ('C:/Users/<USER>/.conda/envs/flaskweb3810/lib/site-packages/xgboost/lib/xgboost.dll', 'xgboost/lib'),
                 ('C:/Users/<USER>/.conda/envs/flaskweb3810/lib/site-packages/xgboost/VERSION', 'xgboost'),
             ],
             datas=[],
             hiddenimports=[
                'engineio.async_drivers.threading',
                'engineio.async_drivers.eventlet',
                'engineio.async_drivers.gevent',
                'autograd'
            ],
             hookspath=[],
             runtime_hooks=[],
             excludes=[],
             win_no_prefer_redirects=False,
             win_private_assemblies=False,
             cipher=block_cipher,
             noarchive=False,
        )
pyz = PYZ(a.pure, a.zipped_data,
             cipher=block_cipher)
exe = EXE(pyz,
          a.scripts,
          [],
          exclude_binaries=True,
          name='runserver',
          debug=False,
          bootloader_ignore_signals=False,
          strip=False,
          upx=True,
          console=True, 
    )
coll = COLLECT(exe,
               a.binaries,
               a.zipfiles,
               a.datas,
               strip=False,
               upx=False,
               upx_exclude=[],
               name='backend',
            )
