<template>
  <div class="main">
    <el-card class="box-card" v-loading="loading">
      <template #header>
        <div class="card-header">
          <span>材料配方优化</span>
        </div>
      </template>

      <el-scrollbar class="main-scrollbar">
        <!-- 优化设置 -->
        <div class="setting-section">
          <h3 class="section-title">优化设置</h3>
          <el-row :gutter="10" align="middle">
            <el-col :span="5">
              <div class="form-item-label">预测模型</div>
              <el-select v-model="selectedModel" placeholder="选择预测模型" style="width: 100%" @change="handleModelChange">
                <el-option v-for="item in modelOptions" :key="item.value" :label="item.label" :value="item.value">
                  <el-tooltip :content="`ID: ${item.id} | 创建时间: ${item.createdAt} | 目标: ${item.targetName}`"
                    placement="right" effect="light">
                    <div>{{ item.label }}</div>
                  </el-tooltip>
                </el-option>
              </el-select>
            </el-col>
            <el-col :span="5">
              <div class="form-item-label">优化算法</div>
              <el-select v-model="selectedOptimizer" placeholder="选择优化算法" style="width: 100%">
                <el-option v-for="item in optimizerOptions" :key="item.id" :label="item.config.displayName"
                  :value="item.id" />
              </el-select>
            </el-col>
            <el-col :span="2">
              <div class="form-item-label">目标值</div>
              <el-input-number v-model="targetValue" :precision="1" :step="0.1" :min="minTargetValue"
                :max="maxTargetValue" placeholder="目标值" style="width: 100%" :disabled="!selectedModel" />
            </el-col>
            <el-col :span="2">
              <div class="form-item-label">优化精度%</div>
              <el-input-number v-model="criterion" :precision="2" :step="0.01" :min="0.01" :max="1" placeholder="优化精度%"
                style="width: 100%" :disabled="!selectedModel" />
            </el-col>
            <el-col :span="8">
              <div class="form-item-label">优化目标</div>
              <div class="target-info">
                <span class="optimization-target-label">{{ optimizationTargetLabel }}</span>
                <el-tooltip content="当前优化目标来自于选择的预测模型" placement="top" effect="light">
                  <el-icon class="info-icon">
                    <InfoFilled />
                  </el-icon>
                </el-tooltip>
                <!-- 高级设置切换 -->
                <el-switch v-model="showAdvancedSettings" active-text="高级设置" style="margin-left: 50px;" />
                <el-button type="danger" v-if="progress === 100" @click="resetOptimization" style="margin-left: 20px;">
                  重置
                </el-button>
              </div>
            </el-col>
          </el-row>
          <el-row class="mt-4" align="middle">
            <el-col :span="24" style="display: flex; align-items: center;">
              <el-button type="primary" @click="startOptimization" :disabled="disableOptimization">
                开始优化
              </el-button>
              <el-progress :percentage="progress" type="circle" :width="40" style="margin-left: 16px;"
                v-show="progress > 0" :status="progress === 100 ? 'success' : ''" />
            </el-col>
          </el-row>
        </div>

        <!-- 高级设置控制的参数部分，使用 v-show 保持挂载 -->
        <el-divider v-show="showAdvancedSettings" />
        <div class="params-section" v-show="showAdvancedSettings">
          <h3 class="section-title">优化算法参数</h3>
          <ModelParamsSetting v-if="selectedOptimizer" ref="modelParamsSettingRef" :algorithm-name="selectedOptimizer"
            :is-optimizer="true" />
          <el-empty v-else description="请先选择优化算法" />
        </div>

        <!-- 优化配方结果 -->
        <el-divider />
        <div class="result-section">
          <h3 class="section-title">优化配方结果</h3>
          <el-table :data="resultData" border stripe class="result-table" max-height="calc(100vh - 350px)">
            <template #empty>
              <el-empty description="暂无结果" />
            </template>
            <el-table-column type="index" label="序号" width="50" v-if="resultData.length > 0" />
            <!-- 优化目标列（始终显示在第二列） -->
            <el-table-column v-if="resultData.length > 0 && optimizationTargetLabel"
              :prop="optimizationTargetColumn.prop" :label="optimizationTargetColumn.label" sortable />
            <el-table-column v-for="col in filteredTableColumns" :key="col.prop" :prop="col.prop" :label="col.label"
              sortable />
          </el-table>
        </div>
      </el-scrollbar>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch, computed, onBeforeUnmount, defineComponent } from "vue";
import { ElMessage, ElLoading } from "element-plus";
import { InfoFilled } from "@element-plus/icons-vue";
import ModelParamsSetting from "@/components/modelManagement/src/ModelParamsSetting/index.vue";
import { SupportedModel } from "@/utils/modelParamsLoader";
import { getModelList, runOptimization } from "@/api/models/optimize";
import { dynamicModelManager, ModelMetadata, getOptimizationAlgorithms } from "@/utils/dynamicModelLoader";
import { getSocket } from "@/utils/socket";
import { Loading, Check } from "@element-plus/icons-vue";

defineOptions({
  name: "componentOptimization"
});

const selectedModel = ref<string | null>(null);
const selectedOptimizer = ref<string | null>(null);
const optimizationTargetLabel = ref("-");
const targetValue = ref(0);
const criterion = ref(0.05);
const loading = ref(false);
const optimizationInProgress = ref(false); // 新增：标记优化过程是否进行中

// 新增：高级设置开关
const showAdvancedSettings = ref(false);

// 参数设置组件引用
const modelParamsSettingRef = ref<InstanceType<typeof ModelParamsSetting> | null>(null);

// 动态获取的模型选项 (从后端获取)
const modelOptions = ref<Array<{
  label: string;
  value: string;
  targetName: string;
  id: number;
  targetValues: any[];
  createdAt: string;
}>>([]);

// 优化算法选项 (从searchParams JSON文件获取)
const optimizerOptions = ref<ModelMetadata[]>([]);

// 目标值范围
const minTargetValue = ref(0);
const maxTargetValue = ref(100);

// 结果表格列
const tableColumns = ref<Array<{ prop: string; label: string }>>([]);
const resultData = ref<Array<Record<string, any>>>([]);

// 节流
let lastUpdate = 0;
const throttleInterval = 1000; // 1000ms
const throttleData = ref<Array<any>>([]);

// 代数
const progress = ref(0);

const handleSearchInfo = (data) => {
  console.log(data);
  progress.value = Number(data.data.progress || 0);

  if (data.code === 200) {

    if (Number(data.data.progress) >= 100 || (data.data.status && data.data.status === "completed")) {
      optimizationInProgress.value = false; // 重新启用按钮
      ElMessage.success("优化已完成");
      progress.value = 100;
    } else {
      // 节流
      const now = Date.now();
      if (now - lastUpdate < throttleInterval) {
        throttleData.value.push(...data.data.searchInfo);
        return;
      };
      lastUpdate = now;
    }

    let tmpData = [];
    if (throttleData.value.length > 0) {
      tmpData.push(...throttleData.value);
      throttleData.value = [];
    }
    if (data.data.searchInfo && data.data.searchInfo.length > 0) {
      tmpData.push(...data.data.searchInfo);
    }
    resultData.value.push(...tmpData);
    if (tableColumns.value.length === 0 && resultData.value.length > 0) {
      tableColumns.value = Object.keys(resultData.value[0]).map(key => ({
        prop: key,
        label: key
      }));
    }
  } else {
    ElMessage.error(data.msg);
  }

};

// 优化目标列配置
const optimizationTargetColumn = computed(() => {
  return {
    prop: optimizationTargetLabel.value,
    label: optimizationTargetLabel.value
  };
});

const resetOptimization = () => {
  resultData.value = [];
  progress.value = 0;
  optimizationInProgress.value = false;
  tableColumns.value = [];
};

// 过滤表格列，移除优化目标列（因为已经单独显示）
const filteredTableColumns = computed(() => {
  if (!optimizationTargetLabel.value || optimizationTargetLabel.value === "-" || tableColumns.value.length === 0) {
    return tableColumns.value;
  }
  return tableColumns.value.filter(col => col.prop !== optimizationTargetLabel.value);
});

// 从后端获取模型列表
const fetchModelList = async () => {
  loading.value = true;
  try {
    const response = await getModelList();
    if (response.code === 200 && Array.isArray(response.data)) {
      // 过滤出状态为completed的模型作为可选项
      const completedModels = response.data.filter(model => model.status === "completed");

      modelOptions.value = completedModels.map(model => {
        // 格式化创建时间
        const createdDate = new Date(model.createdAt);
        const formattedDate = createdDate.toLocaleString('zh-CN', {
          year: 'numeric',
          month: '2-digit',
          day: '2-digit',
          hour: '2-digit',
          minute: '2-digit'
        });

        return {
          label: `${model.name} (${formattedDate})`,
          value: model.taskUid,
          targetName: model.targetName,
          id: model.id,
          targetValues: model.targetValues,
          createdAt: formattedDate  // 存储格式化后的创建时间
        };
      });

      // 初始时不设置默认选择，只在用户选择后更新
      // 如果希望在加载完成后自动选择第一个模型，可以在这里添加逻辑
      // 例如：if (modelOptions.value.length > 0) { selectedModel.value = modelOptions.value[0].value; }

    } else {
      console.warn("Invalid response format or no models found");
      ElMessage.warning("未找到可用模型或响应格式错误");
    }
  } catch (error) {
    console.error("Error fetching model list:", error);
    ElMessage.error("获取模型列表失败");
  } finally {
    loading.value = false;
  }
};

// 从动态加载器获取优化算法
const loadOptimizers = () => {
  loading.value = true;
  try {
    // 使用新的专用方法获取优化算法
    optimizerOptions.value = getOptimizationAlgorithms();

    if (optimizerOptions.value.length === 0) {
      console.warn("No optimization algorithms found");
      ElMessage.warning("未找到可用的优化算法");
    } else {
      console.log(`Loaded ${optimizerOptions.value.length} optimization algorithms:`,
        optimizerOptions.value.map(m => m.id).join(', '));
      selectedOptimizer.value = optimizerOptions.value[0].id; // 设置默认优化算法
    }
  } catch (error) {
    console.error("Error loading optimization algorithms:", error);
    ElMessage.error("加载优化算法失败");
  } finally {
    loading.value = false;
  }
};

const startOptimization = async () => {
  if (disableOptimization.value) {
    ElMessage.warning("请选择预测模型和优化算法，并设置有效的目标值");
    return;
  }

  // 重置结果和进度
  resultData.value = [];
  progress.value = 0;
  optimizationInProgress.value = true; // 标记开始优化

  // 获取优化算法的参数
  let params = {};
  try {
    if (modelParamsSettingRef.value && typeof modelParamsSettingRef.value.getParams === 'function') {
      params = modelParamsSettingRef.value.getParams();
    } else {
      console.warn("无法获取优化参数，使用默认参数");
    }
  } catch (error) {
    console.error("Error getting optimization parameters:", error);
    ElMessage.warning("获取优化参数时出错，将使用默认参数");
  }

  const loadingInstance = ElLoading.service({
    lock: true,
    text: '优化中，请稍候...',
    background: 'rgba(0, 0, 0, 0.7)',
  });

  try {
    const response = await runOptimization({
      taskUid: selectedModel.value,
      target: {
        name: optimizationTargetLabel.value,
        value: targetValue.value,
      },
      search: {
        algorithm: selectedOptimizer.value,
        params: {
          ...params,
          criterion: criterion.value
        }
      }
    });

    if (response.code === 200) {
      const taskId = response.data.taskId;
      const socket = getSocket();

      // 清理旧的监听器
      socket.off("search_info");

      // 设置新的监听器
      socket.emit("get_search_info", { taskId });
      socket.on("search_info", (data) => {
        if (typeof data === "string") {
          data = JSON.parse(data);
        }
        handleSearchInfo(data);
      });

      // 设置初始进度
      progress.value = 1; // 显示进度环

    } else {
      ElMessage.error(`优化失败: ${response.msg || '未知错误'}`);
      optimizationInProgress.value = false; // 优化失败，重置状态
    }
  } catch (error) {
    console.error("Optimization error:", error);
    ElMessage.error('优化请求失败，请重试');
    optimizationInProgress.value = false; // 优化失败，重置状态
  } finally {
    loadingInstance.close();
  }
};

// 当选择模型发生变化时，更新优化目标标签和默认值
const handleModelChange = () => {
  // 清空结果表格数据和表头
  resultData.value = [];
  tableColumns.value = [];

  if (selectedModel.value) {
    const selectedModelData = modelOptions.value.find(model => model.value === selectedModel.value);
    if (selectedModelData) {
      optimizationTargetLabel.value = selectedModelData.targetName || "-";

      // 更新目标值范围和默认值
      if (selectedModelData.targetValues && selectedModelData.targetValues.length > 0) {
        const values = selectedModelData.targetValues.filter(v => typeof v === 'number' && !isNaN(v));

        if (values.length > 0) {
          // 计算最小值和最大值
          minTargetValue.value = Math.min(...values);
          maxTargetValue.value = Math.max(...values);

          // 设置一个合理的默认值（可以是均值或中位数）
          // 这里使用均值作为示例
          const sum = values.reduce((acc, val) => acc + val, 0);
          targetValue.value = parseFloat((sum / values.length).toFixed(1));
        } else {
          // 如果没有有效的目标值，重置为默认值
          minTargetValue.value = 0;
          maxTargetValue.value = 100;
          targetValue.value = 0;
        }
      } else {
        // 如果没有 targetValues，重置为默认值
        minTargetValue.value = 0;
        maxTargetValue.value = 100;
        targetValue.value = 0;
      }
    } else {
      // 如果 selectedModel.value 存在但找不到对应的 modelData (例如清空选择)
      optimizationTargetLabel.value = "-";
      targetValue.value = 0;
      minTargetValue.value = 0;
      maxTargetValue.value = 100;
    }
  } else {
    // selectedModel.value 为 null (未选择任何模型)
    optimizationTargetLabel.value = "-";
    targetValue.value = 0;
    minTargetValue.value = 0;
    maxTargetValue.value = 100;
  }
};

// 禁用开始优化按钮的条件
const disableOptimization = computed(() => {
  return !selectedModel.value ||
    !selectedOptimizer.value ||
    isNaN(targetValue.value) ||
    loading.value ||
    optimizationInProgress.value; // 新增：优化进行中时禁用按钮
});

// 在组件挂载时获取模型列表和优化算法
onMounted(async () => {
  loading.value = true;
  try {
    // 确保动态模型管理器已初始化
    await dynamicModelManager.initialize();

    // 加载模型列表和优化算法
    await Promise.all([fetchModelList(), Promise.resolve(loadOptimizers())]);

    // 初始化默认表格列
    tableColumns.value = [

    ];
  } catch (error) {
    console.error("Error initializing optimization page:", error);
    ElMessage.error("初始化页面失败");
  } finally {
    loading.value = false;
  }
});

// 监听模型选择变化
watch(selectedModel, () => {
  if (selectedModel.value) {
    handleModelChange();
  } else {
    // 当模型选择被清空时，重置目标值和优化目标标签
    handleModelChange();
  }
});

// 在组件卸载前清理 WebSocket 监听器
onBeforeUnmount(() => {
  try {
    const socket = getSocket();
    socket.off("search_info");
  } catch (error) {
    console.error("Error cleaning up socket listeners:", error);
  }
});
</script>

<style scoped>
.main {
  margin: 20px;
  height: calc(100% - 40px);
  display: flex;
  flex-direction: column;
  overflow-x: hidden;
  /* Prevent horizontal scroll */
}

.box-card {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  /* clipping */
}

.box-card :deep(.el-card__body) {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  max-width: 100%;
  padding: 20px;
  box-sizing: border-box;
}

.main-scrollbar {
  flex: 1;
  height: 100%;
  overflow: hidden;
  width: 100%;
  display: flex;
  flex-direction: column;
}

.main-scrollbar :deep(.el-scrollbar__wrap) {
  overflow-x: hidden;
  width: 100%;
  height: 100%;
}

.main-scrollbar :deep(.el-scrollbar__view) {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 16px;
}

.form-item-label {
  margin-bottom: 8px;
  font-size: 14px;
  color: var(--el-text-color-regular);
}

.el-divider {
  margin: 32px 0;
}

.optimization-target-label {
  font-size: 14px;
  color: var(--el-text-color-regular);
  white-space: nowrap;
  display: flex;
  align-items: center;
  height: 32px;
  /* 与其他输入框对齐 */
}

.target-info {
  display: flex;
  align-items: center;
  height: 32px;
  /* 与其他输入框对齐 */
}

.info-icon {
  margin-left: 8px;
  color: var(--el-text-color-secondary);
  cursor: help;
}

.mt-4 {
  margin-top: 1.5rem;
}

/* 防止横向滚动 */
.main,
.main-scrollbar,
.box-card {
  overflow-x: hidden;
  box-sizing: border-box;
}

/* 修正表格宽度，防止溢出 */
.result-table {
  width: 100%;
  overflow-x: auto;
  flex: 1;
}

/* 确保内容不超出卡片边界 */
.setting-section,
.params-section,
.result-section {
  width: 100%;
  box-sizing: border-box;
  overflow-x: hidden;
}

.result-section {
  display: flex;
  flex-direction: column;
  flex: 1;
  min-height: 0;
  /* 重要，确保flex子元素不会溢出 */
}

/* 高级设置参数横向排列 */
.params-section :deep(.params-setting-container) {
  display: flex !important;
  flex-wrap: wrap !important;
}

.params-section :deep(.param-card) {
  flex: 1 1 45% !important;
  margin: 10px !important;
}

/* 优化结果表格 */
.result-table {
  margin-bottom: 20px;
}

/* 进度环样式 */
.progress-circle {
  position: relative;
}

.progress-content {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
}

.progress-icon {
  font-size: 16px;
}

/* 调整进度环文字样式 */
:deep(.el-progress) {
  &.el-progress--circle {
    .el-progress__text {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      font-size: 14px !important;
      font-weight: 600;
      color: var(--el-text-color-primary);
      line-height: 1;
      text-align: center;
      width: 100%;
      margin: 0 !important;
    }
  }
}
</style>