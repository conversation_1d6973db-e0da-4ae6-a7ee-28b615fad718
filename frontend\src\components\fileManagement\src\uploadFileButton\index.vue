<template>
  <el-upload
    ref="upload"
    class="inline-block"
    :before-upload="beforeUpload"
    accept=".xls,.xlsx,.csv"
    :on-change="handleUpload"
    :limit="1"
    :on-exceed="handleExceed"
    :auto-upload="false"
    :show-file-list="false"
  >
    <template #trigger>
      <slot name="trigger">
        <el-button>上传文件</el-button>
      </slot>
    </template>
  </el-upload>
</template>

<script lang="ts" setup>
import { ref, useTemplateRef } from "vue";
import type { UploadInstance, UploadProps, UploadRawFile } from "element-plus";
import { ElMessage, genFileId } from "element-plus";
import { readFile } from "@/utils/file";

defineOptions({
  name: "UploadDataFileButton"
});

const emits = defineEmits<{
  (e: "upload", dataBinary: ArrayBuffer): void;
}>();

const upload = useTemplateRef<UploadInstance>("upload");

/* 上传前检查 */
const beforeUpload: UploadProps["beforeUpload"] = rawFile => {
  const isExcel =
    rawFile.type.includes("spreadsheet") || rawFile.type.includes("excel");
  const isSizeValid = rawFile.size / 1024 / 1024 < 10;

  if (!isExcel) ElMessage.error("只能上传 Excel 文件");
  if (!isSizeValid) ElMessage.error("文件大小不能超过 10MB");

  return isExcel && isSizeValid;
};

/* 上传处理优化 */
const handleUpload: UploadProps["onChange"] = async (
  uploadFile,
  uploadFiles,
) => {
  try {
    const dataBinary = await readFile(uploadFile);
    emits("upload", dataBinary);
  } catch (error) {
    ElMessage.error("文件读取失败");
    console.error(error);
  }
};

/* 处理文件超出限制 */
const handleExceed: UploadProps["onExceed"] = (files, uploadFiles) => {
  upload.value!.clearFiles();
  const file = files[0] as UploadRawFile;
  file.uid = genFileId();
  upload.value!.handleStart(file);
};
</script>
