import type { UploadRawFile, UploadFile } from "element-plus";
/* 读取Excel文件 */
const readFile = (
  file: File | UploadRawFile | UploadFile
): Promise<ArrayBuffer> => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    if (file instanceof File) {
      reader.readAsArrayBuffer(file);
    } else {
      reader.readAsArrayBuffer(file.raw as Blob);
    }
    reader.onload = e => resolve(e.target?.result as ArrayBuffer);
    reader.onerror = e => reject(e);
  });
};

export { readFile };
