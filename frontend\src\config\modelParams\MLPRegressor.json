{"name": "MLPRegressor", "displayName": "多层感知机网络", "description": "通过多个隐藏层学习复杂的非线性关系", "category": "ml", "type": "regression", "defaultParams": {"hidden_layer_sizes": {"value": "(100,)", "type": "text", "description": "隐藏层的大小，例如：(100,) 表示一个100节点的隐藏层，(100, 50) 表示两个隐藏层", "displayName": "隐藏层大小"}, "activation": {"value": "relu", "type": "select", "description": "隐藏层的激活函数", "displayName": "激活函数", "options": [{"label": "ReLU", "value": "relu"}, {"label": "<PERSON><PERSON>", "value": "tanh"}, {"label": "Logistic", "value": "logistic"}, {"label": "Identity", "value": "identity"}]}, "solver": {"value": "adam", "type": "select", "description": "权重优化的求解器", "displayName": "优化器", "options": [{"label": "<PERSON>", "value": "adam"}, {"label": "L-BFGS", "value": "lbfgs"}, {"label": "SGD", "value": "sgd"}]}, "alpha": {"value": 0.1, "type": "number", "optimizeParam": true, "description": "L2正则化项的强度", "displayName": "正则化强度", "min": 0.1, "max": 1.0, "step": 0.1}, "learning_rate_init": {"value": 0.1, "type": "number", "optimizeParam": true, "description": "权重更新的初始学习率", "displayName": "初始学习率", "min": 0.1, "max": 1.0, "step": 0.1}, "max_iter": {"value": 200, "type": "number", "description": "求解器的最大迭代次数", "displayName": "最大迭代次数", "min": 100, "max": 10000, "step": 100}, "random_state": {"value": 42, "type": "number", "optimizeParam": false, "description": "控制随机性的种子值", "displayName": "随机种子", "nullable": true}, "early_stopping": {"value": false, "type": "boolean", "description": "是否使用早停法防止过拟合", "displayName": "早停法"}}, "evaluation": {"splitDataset": {"value": false, "description": "是否划分训练/测试集"}, "trainRatio": {"value": 70, "min": 50, "max": 90, "step": 5, "description": "训练集比例(%)"}, "randomState": {"value": 42, "min": 0, "max": 1000, "description": "随机种子"}, "useModelValidation": {"value": false, "description": "是否使用交叉验证"}, "validationType": {"value": "k-fold", "options": ["k-fold", "leave-one-out"], "description": "验证方法"}, "kFolds": {"value": 5, "min": 2, "max": 10, "description": "k折交叉验证的k值"}}, "tips": ["神经网络适用于复杂的非线性问题，具有强大的拟合能力", "隐藏层数量和节点数影响模型复杂度，需要根据数据量调整", "Adam优化器通常表现良好，L-BFGS适用于小数据集", "建议对输入数据进行标准化处理以提高训练效果", "可以使用早停法和正则化来防止过拟合"], "introduction": {"detailedDescription": "多层感知器由输入层、隐藏层和输出层组成。每个神经元接收来自前一层的输入，通过激活函数进行非线性变换。网络通过反向传播算法学习权重，最小化预测误差。深度网络能够学习复杂的特征表示。", "usageTips": ["适用于复杂的非线性问题", "需要大量的训练数据", "对特征缩放敏感，建议进行标准化", "容易过拟合，需要适当的正则化"], "scenarios": "适用于复杂的非线性问题，在图像识别、自然语言处理等领域应用广泛。", "mainParams": [{"name": "hidden_layer_sizes", "description": "隐藏层的大小和数量"}, {"name": "activation", "description": "隐藏层的激活函数"}, {"name": "learning_rate_init", "description": "初始学习率"}]}}