from flask import Blueprint, request, make_response, current_app, jsonify
import traceback, uuid, numpy as np, pandas as pd
from app.utils.Regression import Regression
from app.utils.params import camel_to_snake, snake_to_camel
from sklearn.model_selection import train_test_split
from app.utils.Preprocess import Preprocess

outlier_bp = Blueprint("outlier", __name__)

@outlier_bp.route("/", methods=["POST"])
def outlier_build():
    post_data = request.json
    post_data = camel_to_snake(post_data)

    preprocess_info = post_data.get("preprocess")
    dataset_info = post_data.get("dataset")

    algorithm = preprocess_info.get("algorithm", {})

    dataset_meta = dataset_info.get("meta", {})
    data = dataset_info.get("data", [])

    data = pd.DataFrame(data, columns=dataset_meta.get("headers"))
    params = algorithm.get("params", {})

    preprocess = Preprocess(data, None)
    data = preprocess.outlier(algorithm.get("name"), params)

    return_data = {
        "labels": data.tolist()
    }
    return make_response(jsonify({"code": 200, "message": "success", "data": return_data}), 200)
