from flask import Blueprint, request, make_response, current_app, jsonify
import logging
from app.utils.params import camel_to_snake, snake_to_camel
from app.types.model_request import ModelRequest, ModelConfig
from app.routers.models.utils import check_model_request
from app.services.DBService import DBService
from app.types.services import ModelTask, SearchTask
from app.services.TaskService import TaskService
from typing import cast
from app.types.flaskapp import FlaskWithExecutor
from app.types.search_request import SearchConfig
from app.routers.applications.utils import check_search_request
from app.services.FileService import FileService

single_target_search_bp = Blueprint("single_target_search", __name__)
logger = logging.getLogger()

@single_target_search_bp.route("/run_optimization", methods=["POST"])
def single_target_search():
    """
    单目标搜索
    
    Args:
        task_uid: 模型uid
        target: {
            "name": str,
            "value": float
        }
        search: {
            "algorithm": "ga",
            "params": {
                "population_size": int,
                "generations": int,
                "crossover": float,
                "mutation": float,
                "max_iter": int,
                "max_time": int,
            },
        }
    """
    post_data: dict = camel_to_snake(request.json) # type: ignore
    try:
        search_config: SearchConfig = check_search_request(post_data)
        search_task: SearchTask = DBService.create_search_task(
            task_uid=search_config.task_uid,
            name=single_target_search_bp.name,
            params=search_config
        )
        task_id = DBService.add_search_task(search_task)
        logger.info(f"single_target_search: 添加搜索任务: {search_task.task_uid}, task_id: {task_id}")
        search_config.task_id = task_id
        DBService.update_search_task(search_config.task_id, params=search_config.to_dict())

        model = FileService.load_model(search_config.task_uid)
        model_config = ModelConfig.from_dict(model["info"]["model_params"], model["info"]["optimized_params"])
        model = model['model']
        TaskService.submit_search_task(search_config, model_config, model)
        return_data = {
            "task_uid": search_config.task_uid,
            "task_id": search_config.task_id
        }
        return make_response(jsonify({"code": 200, "msg": "success", "data": snake_to_camel(return_data)}), 200)
    except Exception as e:
        logger.error(f"single_target_search: 添加搜索任务失败: {e}")
        return make_response(jsonify({"code": 500, "msg": f"添加搜索任务失败: {e}", "data": None}), 500)