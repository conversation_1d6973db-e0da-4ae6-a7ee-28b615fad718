export default {
    path: "/modelApplication",
    redirect: "/modelApplication/componentOptimization",
    meta: {
      icon: "ri:folder-3-line",
      // showLink: false,
      title: "模型应用",
      rank: 10
    },
    children: [
      {
        path: "/modelApplication/componentOptimization",
        name: "componentOptimization",
        component: () => import("@/views/model/optimization/index.vue"),
        meta: {
          title: "材料配方优化",
          keepAlive: true
        }
      },
      // {
      //   path: "/error/500",
      //   name: "500",
      //   component: () => import("@/views/error/500.vue"),
      //   meta: {
      //     title: "500"
      //   }
      // }
    ]
  } satisfies RouteConfigsTable;
  