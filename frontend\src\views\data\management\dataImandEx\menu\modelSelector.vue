<template>
  <div class="model-selector" :class="{ disabled: props.disabled }">
    <el-tooltip content="回归模型构建" placement="bottom">
      <el-button :disabled="props.disabled" @click="openModal" class="menu-button" circle>
        <ModelIcon class="menu-icon normal" />
        <ModelSelectedIcon class="menu-icon hover" />
      </el-button>
    </el-tooltip>

    <el-dialog v-model="isModalVisible" title="模型中心" width="1200px" class="model-center-dialog" :append-to-body="true">
      <div class="model-categories-container">
        <div v-for="category in modelCategories" :key="category.key" class="category-section">
          <h3 class="category-title">
            <img :src="getCategoryIcon(category.key)" class="category-icon" alt="model icon" />
            {{ category.label }}
          </h3>
          <div class="model-list">
            <div v-for="model in category.models" :key="model.id" class="model-item-card"
              @click="selectModel(model, category.key)">
              <div class="model-card-header">
                <span class="model-label">{{ model.config.displayName }}</span>
                <img :src="getLargeIcon(category.key)" class="model-large-icon" alt="model type" />
              </div>
              <p class="model-description">{{ model.config.description }}</p>
              <div class="model-card-footer">
                <img :src="rightArrowIcon" class="right-arrow-icon" alt="right arrow" />
              </div>
            </div>
          </div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted } from "vue";
import ModelIcon from "@/assets/svg/model.svg?component";
import ModelSelectedIcon from "@/assets/svg/model_selected.svg?component";
import {
  dynamicModelManager,
  getCategories,
  type ModelCategory,
  type ModelMetadata
} from "@/utils/dynamicModelLoader";

const props = defineProps<{
  disabled?: boolean;
}>();

defineOptions({
  name: "ModelSelector"
});

const emit = defineEmits<{
  (e: "buildModel", model: { id: string, category: string }): void;
}>();

// 响应式数据
const isModalVisible = ref(false);
const modelCategories = ref<ModelCategory[]>([]);
const isLoading = ref(false);

// 获取分类图标
const getCategoryIcon = (categoryKey: string) => {
  switch (categoryKey) {
    case 'linear':
      return new URL('@/assets/svg/linear.svg', import.meta.url).href;
    case 'tree':
      return new URL('@/assets/svg/tree.svg', import.meta.url).href;
    case 'ml':
    default:
      return new URL('@/assets/svg/other.svg', import.meta.url).href;
  }
};

// 获取大图标
const getLargeIcon = (categoryKey: string) => {
  switch (categoryKey) {
    case 'linear':
      return new URL('@/assets/svg/linear_lg.svg', import.meta.url).href;
    case 'tree':
      return new URL('@/assets/svg/tree_lg.svg', import.meta.url).href;
    case 'ml':
    default:
      return new URL('@/assets/svg/other_lg.svg', import.meta.url).href;
  }
};

// 右箭头图标
const rightArrowIcon = new URL('@/assets/svg/right_arrow.svg', import.meta.url).href;

// 初始化动态模型管理器
const initializeModels = async () => {
  try {
    isLoading.value = true;
    await dynamicModelManager.initialize();
    modelCategories.value = getCategories();
    console.log("Dynamic models loaded:", modelCategories.value);
  } catch (error) {
    console.error("Failed to initialize dynamic models:", error);
  } finally {
    isLoading.value = false;
  }
};

// 方法
const openModal = () => {
  if (props.disabled) return;
  isModalVisible.value = true;
};

const selectModel = (model: ModelMetadata, categoryKey: string) => {
  emit("buildModel", { id: model.id, category: categoryKey });
  // 选择后自动关闭
  isModalVisible.value = false;
};

onMounted(async () => {
  // 初始化动态模型
  await initializeModels();
});
</script>

<style scoped>
.model-selector {
  position: relative;
  display: inline-block;
}

.model-selector.disabled {
  cursor: not-allowed;
}

/* 调整为图标模式 */
.selector-button.icon-only {
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 旧样式保留以兼容 */
.selector-button {
  transition: all 0.3s ease;
}

.model-categories-container {
  padding: 0 10px;
}

.category-section {
  margin-bottom: 20px;
}

.category-title {
  font-size: 16px;
  color: #999999;
  margin-bottom: 16px;
  display: flex;
  align-items: center;
  padding: 0 20px;
}

.category-icon {
  width: 20px;
  height: 20px;
  margin-right: 8px;
}

.model-list {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 20px;
  padding: 0 20px;
}

.model-item-card {
  background: rgba(255, 255, 255, 0.85);
  border: 1px solid var(--el-border-color-light);
  border-radius: 2px;
  padding: 16px;
  cursor: pointer;
  transition: all 0.25s ease;
  position: relative;
  overflow: hidden;
  box-shadow: 0px 2px 8px 0px rgba(0, 0, 0, 0.08);
}

.model-item-card:hover {
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.12);
  border-color: var(--el-color-primary);
}

.model-card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.model-label {
  font-size: 18px;
  font-weight: bold;
  color: #3D3D3D;
}

.model-large-icon {
  width: 24px;
  height: 24px;
  color: var(--el-color-primary);
}

.model-description {
  font-size: 14px;
  color: #999999;
  line-height: 1.5;
  margin-bottom: 12px;
  min-height: 40px;
}

.model-card-footer {
  text-align: left;
  padding-top: 8px;
  margin-top: 8px;
}

.right-arrow-icon {
  width: 16px;
  height: 16px;
  transition: transform 0.2s ease;
}

.model-item-card:hover .right-arrow-icon {
  transform: translateX(4px);
}

/* 深色模式适配 */
html.dark .model-item-card {
  background: rgba(30, 30, 30, 0.85);
  border-color: var(--el-border-color);
}

html.dark .model-item-card:hover {
  border-color: var(--el-color-primary);
}

/* 对话框样式覆盖 */
:deep(.model-center-dialog .el-dialog__header) {
  border-bottom: none;
  margin-right: 0;
  padding: 20px;
}

:deep(.model-center-dialog .el-dialog__body) {
  padding: 0 0 20px 0;
  background: #F9FBFF;
}

:deep(.model-center-dialog .el-dialog__title) {
  font-weight: bold;
  font-size: 18px;
}

/* 加载状态 */
.selector-button.loading {
  pointer-events: none;
  opacity: 0.7;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }

  to {
    transform: rotate(360deg);
  }
}

.menu-button {
  width: 36px;
  height: 36px;
  border-radius: 2px;
  background: #FFFFFF;
  box-shadow: 0px 2px 8px 0px rgba(0, 0, 0, 0.08);

  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;

  &:hover {

    .menu-icon.normal {
      opacity: 0;
    }

    .menu-icon.hover {
      opacity: 1;
    }
  }

  .menu-icon {
    width: 22px;
    height: 22px;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    transition: opacity 0.2s ease;
  }

  .normal {
    opacity: 1;
  }

  .hover,
  .selected {
    opacity: 0;
  }

}
</style>
