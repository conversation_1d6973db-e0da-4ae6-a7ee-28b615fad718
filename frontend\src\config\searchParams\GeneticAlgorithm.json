{"name": "GeneticAlgorithm", "displayName": "遗传算法", "description": "通过遗传算法设计配方", "type": "single-target-search", "defaultParams": {"population_size": {"value": 40, "type": "number", "description": "种群大小", "displayName": "种群大小", "min": 10, "max": 100, "step": 10}, "generations": {"value": 40, "type": "number", "description": "种群代数", "displayName": "种群代数", "min": 10, "max": 1000, "step": 10}, "crossover": {"value": 0.8, "type": "number", "description": "交叉概率", "displayName": "交叉概率", "min": 0.1, "max": 1, "step": 0.1}, "mutation": {"value": 0.1, "type": "number", "description": "变异概率", "displayName": "变异概率", "min": 0.1, "max": 1, "step": 0.1}, "max_iter": {"value": 100, "type": "number", "description": "最大迭代次数", "displayName": "最大迭代次数", "min": 10, "max": 1000, "step": 10}, "max_time": {"value": 10000, "type": "number", "description": "最大时间", "displayName": "最大时间", "min": 10, "max": 10000, "step": 10}}, "tips": ["遗传算法是一种优化算法，通过模拟自然界的进化过程来寻找最优解", "遗传算法适用于单目标优化问题，如参数优化、函数优化等", "遗传算法具有全局搜索能力，能够避免陷入局部最优解", "遗传算法具有较好的鲁棒性，能够适应不同的优化问题"], "introduction": {"detailedDescription": "遗传算法是一种优化算法，通过模拟自然界的进化过程来寻找最优解", "usageTips": ["遗传算法适用于单目标优化问题，如参数优化、函数优化等", "遗传算法具有全局搜索能力，能够避免陷入局部最优解", "遗传算法具有较好的鲁棒性，能够适应不同的优化问题"], "scenarios": "适用于单目标优化问题，如参数优化、函数优化等", "mainParams": [{"name": "population_size", "description": "种群大小"}, {"name": "generations", "description": "迭代次数"}, {"name": "crossover", "description": "交叉概率"}, {"name": "mutation", "description": "变异概率"}]}}