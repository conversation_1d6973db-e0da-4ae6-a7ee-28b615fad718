from typing import Literal, TypedDict, Union, Optional, Dict, Any
from sqlalchemy.orm import DeclarativeBase, Mapped, mapped_column
from sqlalchemy import Integer, String, JSON, DateTime, Boolean
from datetime import datetime
import json

RabbitmqHost = str
RabbitmqPort = int

class Base(DeclarativeBase):
    pass

class ModelTask(Base):
    __tablename__ = "model_task"
    id: Mapped[int] = mapped_column(Integer, primary_key=True)
    task_uid: Mapped[str] = mapped_column(String(255), nullable=True)
    name: Mapped[str] = mapped_column(String(255))
    params: Mapped[Dict] = mapped_column(JSON)
    status: Mapped[str] = mapped_column(String(255))
    progress: Mapped[int] = mapped_column(String(255), nullable=True)
    created_at: Mapped[datetime] = mapped_column(DateTime)
    updated_at: Mapped[datetime] = mapped_column(DateTime)
    result: Mapped[Dict] = mapped_column(JSON, nullable=True)
    error: Mapped[str] = mapped_column(String(255), nullable=True)
    is_rabbitmq_ready: Mapped[bool] = mapped_column(<PERSON><PERSON>an, default=False)
    asynchronous: Mapped[bool] = mapped_column(<PERSON><PERSON>an, default=False)

    def to_dict(self) -> Dict[str, Any]:
        """
        将ModelTask对象转换为可JSON序列化的字典
        
        Returns:
            Dict[str, Any]: 包含所有字段的字典，datetime对象转换为ISO格式字符串
        """
        return {
            'id': self.id,
            'task_uid': self.task_uid,
            'name': self.name,
            'params': self.params,
            'status': self.status,
            'progress': self.progress,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None,
            'result': self.result,
            'error': self.error,
            'is_rabbitmq_ready': self.is_rabbitmq_ready,
            'asynchronous': self.asynchronous
        }
    
    def to_json(self) -> str:
        """
        将ModelTask对象转换为JSON字符串
        
        Returns:
            str: JSON格式的字符串
        """
        return json.dumps(self.to_dict(), ensure_ascii=False)

class SearchTask(Base):
    __tablename__ = "search_task"
    id: Mapped[int] = mapped_column(Integer, primary_key=True)
    task_uid: Mapped[str] = mapped_column(String(255), nullable=True)
    name: Mapped[str] = mapped_column(String(255))
    params: Mapped[Dict] = mapped_column(JSON)
    status: Mapped[str] = mapped_column(String(255))
    progress: Mapped[int] = mapped_column(String(255), nullable=True)
    created_at: Mapped[datetime] = mapped_column(DateTime)
    updated_at: Mapped[datetime] = mapped_column(DateTime)
    result: Mapped[Dict] = mapped_column(JSON, nullable=True)
    error: Mapped[str] = mapped_column(String(255), nullable=True)
    is_rabbitmq_ready: Mapped[bool] = mapped_column(Boolean, default=False)
    asynchronous: Mapped[bool] = mapped_column(Boolean, default=False)

    def to_dict(self) -> Dict[str, Any]:
        """
        将SearchTask对象转换为可JSON序列化的字典
        
        Returns:
            Dict[str, Any]: 包含所有字段的字典，datetime对象转换为ISO格式字符串
        """
        return {
            'id': self.id,
            'task_uid': self.task_uid,
            'name': self.name,
            'params': self.params,
            'status': self.status,
            'progress': self.progress,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None,
            'result': self.result,
            'error': self.error,
            'is_rabbitmq_ready': self.is_rabbitmq_ready,
            'asynchronous': self.asynchronous
        }
    
    def to_json(self) -> str:
        """
        将SearchTask对象转换为JSON字符串
        
        Returns:
            str: JSON格式的字符串
        """
        return json.dumps(self.to_dict(), ensure_ascii=False)
