# 项目 Bug 问题记录

| Bug 编号 | 描述                                                                            | 发生时间 | 修复状态 | 解决方式                                                                                                                                                                                                              |
| -------- | ------------------------------------------------------------------------------- | -------- | -------- | --------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| 1        | `xlsx` 读取时科学计数法及小数问题                                             | 04-15    | 已修复   | `XLSX.utils.sheet_to_json`的 `header:1`后面新增 `raw: false`                                                                                                                                                    |
| 2        | 右键无法添加列                                                                  | 04-16    | 已修复   | 是 `XLSX`的问题：不加上 `header:1`的时候，读取出的是一个数组中包含对象加上之后：读取出来是一个二维数组，另外注意使用这种方式的时候，不需要在给 `handsontable`的设置中添加 `columns`这个属性了，否则渲染不成功 |
| 3        | 读取数据时候的问题，当某一个列只有标题，但没有数据的时候，需要也b能在表格中渲染 | 04-16    | 已修复   |   `XLSX`读取数据时加上`defval:"null"`就可以使全部空值设置为字符串null                                                                              |
| 4        | 使用 `teleport`方式当切换路由时不会重新渲染                                   | 04-17    | 已修复   | teleport上加defer                                                                                                                                                                                                     |
| 5        | 保留设置按钮时和其他左侧按钮的样式问题                                          | 04-17    | 已修复   | parent节点加flex样式                                                                                                                                                                                                  |
| 6        | `elupload`自带样式和其他按钮冲突                                              | 04-14    | 已修复   | 参考：https://blog.csdn.net/weixin_42636552/article/details/91510581                                                                                                                                                  |
