<template>
  <el-dialog
    :model-value="dialogStore.dialogs[dialogKey]"
    :title="dialogTitle"
    width="85%"
    :class="dialogClass"
    top="5vh"
    destroy-on-close
    @update:model-value="(val) => !val && handleClose()"
    @close="handleClose()"
    @opened="handleDialogOpened"
  >
    <el-form :model="modelConfig" label-position="top" label-width="auto">
      <!-- 模型简介（包含模型名称） -->
      <ModelIntroduction
        :model-type="modelType"
        :model-display-name="getModelTypeLabel(modelType)"
        class="model-intro-container"
      />

      <!-- Custom Selection Card -->
      <el-card class="selection-card" shadow="never">
        <div class="selection-controls">
          <button
            type="button"
            :class="['selection-button', { 'is-active': activeSelectionTab === 'variables' }]"
            @click="activeSelectionTab = 'variables'"
          >
            <img :src="variableIcon" alt="icon" class="btn-icon" />
            变量选择
          </button>
          <button
            type="button"
            :class="['selection-button', { 'is-active': activeSelectionTab === 'parameters' }]"
            @click="activeSelectionTab = 'parameters'"
          >
            <img :src="parameterIcon" alt="icon" class="btn-icon" />
            参数设置
          </button>
        </div>

        <div class="selection-content">
          <div v-show="activeSelectionTab === 'variables'">
            <ModelSelection
              ref="modelSelectionRef"
              style="width: 100%"
              :headers="tableDataStore.currentTableHeader"
              :model-type="modelType"
              @update:selection="handleVariableSelection"
            />
          </div>
          <div v-show="activeSelectionTab === 'parameters'">
            <ModelParamsSetting
              ref="paramsSettingRef"
              style="width: 100%"
              :algorithm-name="modelType"
              @update:params="handleParamsUpdate"
            />
          </div>
        </div>
      </el-card>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose()" size="default" class="cancel-btn">
          取消
        </el-button>
        <el-button
          type="primary"
          @click="handleConfirmAction"
          :loading="isBuilding"
          size="default"
          class="confirm-btn"
        >
          {{ isBuilding ? '构建中...' : '确定' }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { ref, watch, nextTick, computed, reactive } from "vue";
import { useDialogStore } from "@/store/modules/dialog";
import {
  ModelSelection,
  ModelParamsSetting,
  ModelIntroduction
} from "@/components/modelManagement";
import { useTableDataStore } from "@/store/modules/tableData";
import { ElMessageBox, ElMessage } from "element-plus";
import type { ModelConfig, ModelAlgorithm, ModelMetaEvaluation } from "@/types/models";
import { getSocket } from "@/utils/socket";

// SVG Asset Loading
const svgAssets = import.meta.glob('@/assets/svg/*.svg', { query: '?url', eager: true, import: 'default' });
const getAssetUrl = (name: string) => svgAssets[`/src/assets/svg/${name}.svg`] || '';

const props = defineProps<{
  modelType?: string;
  dialogType: 'linear' | 'ml';
}>();

const emit = defineEmits<{
  (e: "confirm", config: ModelConfig): void;
  (e: "close"): void;
  (e: "dialogOpened"): void;
}>();

const dialogStore = useDialogStore();
const tableDataStore = useTableDataStore();
const isBuilding = ref(false);
const paramsSettingRef = ref();
const modelSelectionRef = ref();
const activeSelectionTab = ref<'variables' | 'parameters'>('variables');

const variableIcon = computed(() =>
  activeSelectionTab.value === 'variables'
    ? getAssetUrl('variable_selected')
    : getAssetUrl('variable')
);
const parameterIcon = computed(() =>
  activeSelectionTab.value === 'parameters'
    ? getAssetUrl('parameter_selected')
    : getAssetUrl('parameter')
);


const dialogKey = computed(() =>
  props.dialogType === "linear" ? "linearModel" : "mlModel"
);
const dialogTitle = computed(() =>
  props.dialogType === "linear" ? "构建线性模型" : "构建机器学习模型"
);
const dialogClass = computed(() =>
  props.dialogType === "linear" ? "linear-model-dialog" : "ml-model-dialog"
);

const modelConfig = reactive<ModelConfig>({
  dataset: {
    data: [[]],
    meta: {
      headers: {
        index: [],
        target: [],
        features: [],
        deletes: [],
        all: [],
      },
    },
  },
  model: {
    algorithm: {
      name: '',
      params: {},
    },
    meta: {
      evaluation: {
        cv: undefined,
        loocv: false,
        test: undefined,
      },
      headers: {
        index: [],
        target: [],
        features: [],
        all: [],
      },
    },
  },
});

// 监听模型类型变化
watch(
  () => props.modelType,
  (newType: string) => {
    if (newType) {
      modelConfig.model.algorithm.name = newType;
    }
  },
  { immediate: true }
);

const getModelTypeLabel = (type: string) => {
  const typeMap = {
    LinearRegression: "多元线性回归",
    Ridge: "岭回归",
    Lasso: "Lasso回归",
    ElasticNet: "弹性网络回归",
    DecisionTreeRegressor: '决策树',
    RandomForestRegressor: '随机森林',
    XGBoost: 'XGBoost',
    GradientBoostingRegressor: '梯度提升回归',
    SVR: '支持向量机',
    MLPRegressor: '人工神经网络',
  };
  return typeMap[type as keyof typeof typeMap] || type;
};

const handleModelProgress = (data: { status: string; message?: string }) => {
  if (data.status === "processing") {
    isBuilding.value = true;
    ElMessage.info(data.message || "模型构建中...");
  } else if (data.status === "completed") {
    isBuilding.value = false;
    ElMessage.success(data.message || "模型构建成功！");
    handleClose();
  } else if (data.status === "error") {
    isBuilding.value = false;
    ElMessage.error(data.message || "模型构建失败。");
  }
};

// 处理dialog打开事件
const handleDialogOpened = () => {
  console.log("ModelDialog opened, emitting dialogOpened event");
  emit('dialogOpened');
  
  const socket = getSocket();
  if (socket) {
    socket.on("model_progress", handleModelProgress);
  }

  nextTick(() => {
    if (paramsSettingRef.value?.forceReloadConfig) {
      paramsSettingRef.value.forceReloadConfig();
    }
    if (modelSelectionRef.value?.reset) {
      modelSelectionRef.value.reset();
    }
  });
};

// 处理变量选择
const handleVariableSelection = (selection: typeof modelConfig.dataset.meta.headers) => {
  modelConfig.dataset.meta.headers = selection;
};

// 处理参数更新
const handleParamsUpdate = (params: {
  algorithm: ModelAlgorithm;
  evaluation: ModelMetaEvaluation;
}) => {
  modelConfig.model.algorithm = params.algorithm;
  modelConfig.model.meta.evaluation = params.evaluation;
};

// 验证变量选择
const validateVariables = async (variables: typeof modelConfig.model.meta.headers) => {
  const errors: string[] = [];

  // 检查序号变量
  if (variables.index.length > 1) {
    errors.push(
      `<li>发现多个序号变量：<span style="color: skyblue">${variables.index.join("、")}</span>，请只保留一个序号变量</li>`
    );
  }

  // 检查目标变量
  if (variables.target.length !== 1) {
    errors.push(
      `<li>目标变量不合法，当前选择：<span style="color: skyblue">${variables.target.length ? variables.target.join("、") : "空"}</span>，请只保留一个目标变量</li>`
    );
  }

  // 检查自变量
  if (!variables.features || variables.features.length === 0) {
    errors.push("<li>请至少选择一个自变量</li>");
  }

  // 如果有错误，显示所有错误信息
  if (errors.length > 0) {
    await ElMessageBox.alert(errors.join(""), "变量选择错误", {
      type: "error",
      confirmButtonText: "确定",
      dangerouslyUseHTMLString: true,
    });
    return false;
  }

  return true;
};

// 验证数据
const validateData = async (data: any[][], meta: {
  headers: string[];
  variables: {
    index: string[];
    target: string[];
    features: string[];
  };
}) => {
  const errors: string[] = [];
  const { headers, variables } = meta;

  const isNumeric = (value: any): boolean => {
    if (typeof value === "number") return true;
    if (typeof value === "string") {
      return value.trim() !== "" && !isNaN(Number(value));
    }
    return false;
  };

  // 获取需要检查的列的索引
  const checkColumns = new Map();
  variables.features.forEach((col: string) => {
    checkColumns.set(headers.indexOf(col), "自变量");
  });
  checkColumns.set(headers.indexOf(variables.target[0]), "目标变量");
  if (variables.index.length) {
    checkColumns.set(headers.indexOf(variables.index[0]), "序号变量");
  }

  // 检查每一行数据
  data.forEach((row, rowIndex) => {
    checkColumns.forEach((type, colIndex) => {
      // 检查空值
      if (
        row[colIndex] === null ||
        row[colIndex] === undefined ||
        row[colIndex] === ""
      ) {
        errors.push(
          `<li>第 ${rowIndex + 1} 行的${type}「${headers[colIndex]}」存在空值,建议进行数据预处理</li>`
        );
        return;
      } else if (type !== "序号变量" && !isNumeric(row[colIndex])) {
        errors.push(
          `<li>第 ${rowIndex + 1} 行的${type}「${headers[colIndex]}」值「${row[colIndex]}」不是数字</li>`
        );
      }
    });
  });

  // 如果有错误，显示所有错误信息
  if (errors.length > 0) {
    await ElMessageBox.alert(
      `<div style="max-height: 300px; overflow-y: auto;">
        <p>发现以下问题：</p>
        <ul>${errors.join("")}</ul>
      </div>`,
      "数据验证错误",
      {
        type: "error",
        confirmButtonText: "确定",
        dangerouslyUseHTMLString: true,
      }
    );
    return false;
  }

  return true;
};

const handleClose = () => {
  dialogStore.hideDialog(dialogKey.value);
  
  const socket = getSocket();
  if (socket) {
    socket.off("model_progress", handleModelProgress);
  }
  
  emit("close");
};

const handleConfirmAction = async () => {
  const data = tableDataStore.currentTableData;
  const headers = tableDataStore.currentTableHeader.map((h: any) => {
    if (typeof h === 'string') return h;
    if (typeof h === 'object') {
      return h.prop || h.title || '';
    }
    return String(h);
  });
  
  const variables = modelConfig.dataset.meta.headers;

  const isVariablesValid = await validateVariables(variables);
  if (!isVariablesValid) return;

  const isDataValid = await validateData(data, { headers, variables });
  if (!isDataValid) return;

  modelConfig.dataset.data = data;
  modelConfig.model.meta.headers = variables; // Align model meta headers with dataset meta headers

  emit("confirm", modelConfig);
  // The dialog will be closed by the "completed" event from the socket
  // dialogStore.hideDialog(dialogKey.value); 
};

watch(
  () => dialogStore.dialogs[dialogKey.value],
  async (newValue, oldValue) => {
    if (!newValue && oldValue) {
      await nextTick();
      if (paramsSettingRef.value?.resetParams) {
        paramsSettingRef.value.resetParams();
      }
    }
  }
);
</script>

<style scoped>
.model-intro-container {
  margin-bottom: 24px;
}

.selection-card {
  margin: 0 16px 16px 16px;
  border-radius: 8px;
  background: #FFFFFF;
  box-shadow: 0px 0px 40px 0px rgba(0, 93, 255, 0.0784);
  border: none;
}

:deep(.selection-card .el-card__body) {
  padding: 16px;
}

.selection-controls {
  display: flex;
  gap: 12px;
  margin-bottom: 16px;
}

.selection-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 184px;
  height: 36px;
  border-radius: 24px;
  border: none;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.3s ease;
  background: rgba(206, 206, 206, 0.1);
  color: #666;
}

.selection-button.is-active {
  background: rgba(0, 93, 255, 0.1);
  border: 1px solid #005DFF;
  color: #005DFF;
  font-weight: 500;
}

.btn-icon {
  width: 16px;
  height: 16px;
  margin-right: 8px;
}

.selection-content {
  margin-top: 16px;
}

.dialog-footer {
  text-align: right;
  padding-top: 20px;
}

.cancel-btn {
  margin-right: 10px;
}
</style>
