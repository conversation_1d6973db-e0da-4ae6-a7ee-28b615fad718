from flask import current_app, send_file, make_response, jsonify, request
from app.extensions.socketio import socketio
from app.utils.params import camel_to_snake, snake_to_camel
from app.services.ProgressService import ProgressService
from app.types.flaskapp import FlaskWithExecutor
from app.services.DBService import DBService
import logging
from typing import cast

logger = logging.getLogger("socketio")

@socketio.on('get_search_info')
def get_search_info(data: dict):
    data = camel_to_snake(data) # type: ignore
    task_id: str = data["task_id"]
    print(task_id)
    try:
        if task_id is not None:
            ProgressService.report_search(task_id)
            return_data = {
                "task_id": task_id,
            }
            return {"code": 200, "msg": "success", "data": snake_to_camel(return_data)}
        else:
            return {"code": 400, "msg": "task_id is None", "data": None}
    except Exception as e:
        logger.error(f"handle_search_info: 搜索信息获取失败: {e}")
        return {"code": 500, "msg": f"搜索信息获取失败: {e}", "data": None}
    