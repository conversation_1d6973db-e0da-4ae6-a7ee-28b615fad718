// frontend/src/style/model-management.scss
// This file contains common styles for the model management components.

// Common card styles
.el-card.model-card {
  border-radius: 12px;
  border: none;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  transition: all 0.3s ease;
  margin-bottom: 16px;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
  }

  .el-card__header {
    background: #f0f9ff;
    border-bottom: 1px solid #f0f0f0;
    border-radius: 12px 12px 0 0;
    padding: 16px 20px;
    font-weight: 600;
    color: #303133;
  }

  .el-card__body {
    padding: 20px;
  }
}

// Common table styles
.el-table.model-table {
  border: none;
  box-shadow: none;
  border-radius: 0;

  .el-table__header-wrapper {
    .el-table__header {
      th.el-table__cell {
        background-color: transparent;
        border-bottom: none;
      }
    }
  }

  .el-table__body-wrapper {
    .el-table__body {
      .el-table__row {
        &:hover > td.el-table__cell {
          background-color: transparent;
        }

        td.el-table__cell {
          border-bottom: none;
        }
      }
    }
  }
}

// Common empty state styles
.el-empty.model-empty {
  background: white;
  border-radius: 12px;
  padding: 40px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

// Common form controls
.model-controls {
  .el-select .el-input__wrapper {
    border-radius: 8px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  }

  .el-button {
    border-radius: 8px;
    font-weight: 500;
  }
}

// Common statistics block
.model-statistics {
  padding: 20px;
  margin: 16px auto;
} 