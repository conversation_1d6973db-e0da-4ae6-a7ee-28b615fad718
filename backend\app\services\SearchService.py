from app.types.search_request import SearchConfig
from app.types.model_request import ModelConfig
from app.algorithms.genetic_algorithms import GeneticAlgorithm
import numpy as np, pandas as pd
from typing import Dict, Any

def check_search_params(alg_name: str, alg_param: dict) -> dict:
    if alg_name == "GeneticAlgorithm":
        new_params = {}
        for key, value in alg_param.items():
            if key == "population_size":
                new_params["population_size"] = int(value)
            elif key == "generations":
                new_params["generations"] = int(value)
            elif key == "crossover":
                new_params["crossover"] = float(value)
            elif key == "mutation":
                new_params["mutation"] = float(value)
            elif key == "max_iter":
                new_params["max_iter"] = int(value)
            elif key == "max_time":
                new_params["max_time"] = int(value)
            elif key == "criterion":
                new_params["criterion"] = float(value)
        return GeneticAlgorithm, new_params
    else:
        raise ValueError(f"Invalid algorithm: {alg_name}")

def check_search_space(model_config: ModelConfig):
    xtrain = pd.DataFrame(model_config.x_train)
    xl = xtrain.min(axis=0)
    xu = xtrain.max(axis=0)
    return xl.values.reshape(-1, ), xu.values.reshape(-1, )

class SearchService(object):

    def __init__(self, search_config: SearchConfig, model_config: ModelConfig, model: Dict[str, Any], report=None, update=None):
        self.task_id = search_config.task_id
        self.task_uid = search_config.task_uid
        self.target_name = search_config.target_name
        self.target_value = search_config.target_value
        self.alg_name = search_config.alg_name
        self.alg_param = search_config.alg_param
        self.asynchronous = search_config.asynchronous
        self.is_rabbitmq_ready = search_config.is_rabbitmq_ready
        self.model_config = model_config
        self.feature_names = model_config.x_train.columns.tolist()
        self.model = model
        self.info = {
            "task_type": "search",
            "alg": self.alg_name,
            "search_params": search_config.to_dict(),
            "search_result": [],
        }

        self.alg, self.params = check_search_params(self.alg_name, self.alg_param)
        self.xl, self.xu = check_search_space(self.model_config)

        self.report = report
        self.update = update

    def fit(self) -> None:
        self.alg(**self.params).fit(
            self.xl, 
            self.xu, 
            self.model_config, 
            self.model,
            self.target_value, 
            self.feature_names, 
            self.report, 
            self.update, 
            self.task_id
        )