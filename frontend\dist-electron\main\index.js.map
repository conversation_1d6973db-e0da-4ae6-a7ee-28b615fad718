{"version": 3, "file": "index.js", "sources": ["../../electron/main/utils.ts", "../../electron/main/index.ts"], "sourcesContent": ["import { exec } from \"node:child_process\";\r\n\r\n// 检查是否有运行中的runserver.exe进程，若有则返回进程ID，否则返回空数组\r\nexport async function checkExistingBackendProcesses(\r\n  processName: string = \"runserver.exe\",\r\n): Promise<number[]> {\r\n  return new Promise((resolve) => {\r\n    // 暂只检测windows平台，若需要检测其他平台，请修改此处的判断条件\r\n    if (process.platform !== \"win32\") {\r\n      resolve([]);\r\n      return;\r\n    }\r\n\r\n    // wmic命令用于获取windows系统中的进程信息\r\n    // 检测进程名为runserver.exe的进程ID\r\n    const cmd = `wmic process where \"name='${processName}'\" get processid`;\r\n\r\n    exec(cmd, (error, stdout) => {\r\n      if (error) {\r\n        console.log(\r\n          \"No existing backend processes found or error running command\",\r\n        );\r\n        resolve([]);\r\n        return;\r\n      }\r\n\r\n      // 解析进程ID\r\n      const pidPattern = /(\\d+)/g;\r\n      const pids: number[] = [];\r\n      let match;\r\n\r\n      while ((match = pidPattern.exec(stdout)) !== null) {\r\n        const pid = parseInt(match[1], 10);\r\n        if (!isNaN(pid) && pid !== process.pid) {\r\n          pids.push(pid);\r\n        }\r\n      }\r\n\r\n      console.log(`Found ${pids.length} existing backend processes:`, pids);\r\n      resolve(pids);\r\n    });\r\n  });\r\n}\r\n\r\n// 检查是否有运行中的RabbitMQ进程\r\n// rmq启动方式为rabbitmq-server.bat，该bat文件会启动erl.exe和epmd.exe\r\nexport async function checkExistingRabbitMQProcesses(): Promise<number[]> {\r\n  return new Promise((resolve) => {\r\n    if (process.platform !== \"win32\") {\r\n      resolve([]);\r\n      return;\r\n    }\r\n\r\n    const cmd =\r\n      \"wmic process where \\\"name='erl.exe' or name='epmd.exe'\\\" get processid,name\";\r\n\r\n    exec(cmd, (error, stdout) => {\r\n      if (error) {\r\n        console.log(\r\n          \"No existing RabbitMQ processes found or error running command\",\r\n        );\r\n        resolve([]);\r\n        return;\r\n      }\r\n\r\n      // 解析进程ID和名称\r\n      const processLines = stdout.trim().split(\"\\n\").slice(1); // 跳过标题行\r\n      const pids: number[] = [];\r\n\r\n      for (const line of processLines) {\r\n        const match = line.trim().match(/(\\d+)\\s+(.+)/);\r\n        if (match) {\r\n          const pid = parseInt(match[1], 10);\r\n          const name = match[2].trim();\r\n          if (!isNaN(pid) && pid !== process.pid) {\r\n            console.log(\r\n              `Found RabbitMQ related process: ${name} (PID: ${pid})`,\r\n            );\r\n            pids.push(pid);\r\n          }\r\n        }\r\n      }\r\n\r\n      console.log(`Found ${pids.length} existing RabbitMQ processes`);\r\n      resolve(pids);\r\n    });\r\n  });\r\n}\r\n", "import { release } from \"node:os\";\nimport { fileURLToPath } from \"node:url\";\nimport { join, dirname } from \"node:path\";\nimport {\n  type MenuItem,\n  type MenuItemConstructorOptions,\n  app,\n  Menu,\n  shell,\n  ipcMain,\n  BrowserWindow,\n  dialog,\n} from \"electron\";\nimport { spawn, ChildProcess, exec } from \"node:child_process\";\nimport { existsSync } from \"node:fs\";\nimport {\n  checkExistingBackendProcesses,\n  checkExistingRabbitMQProcesses,\n} from \"./utils\";\n\n// 在文件顶部添加 fs 和 path 的引入\n// import { existsSync, mkdirSync, readFileSync, writeFileSync, readdirSync, Stats, statSync } from 'node:fs'\n// import { dialog } from 'electron'\n\n// The built directory structure\n//\n// ├─┬ dist-electron\n// │ ├─┬ main\n// │ │ └── index.js    > Electron-Main\n// │ └─┬ preload\n// │   └── index.mjs    > Preload-Scripts\n// ├─┬ dist\n// │ └── index.html    > Electron-Renderer\n//\nconst __filename = fileURLToPath(import.meta.url);\nconst __dirname = dirname(__filename);\nprocess.env.DIST_ELECTRON = join(__dirname, \"..\");\nprocess.env.DIST = join(process.env.DIST_ELECTRON, \"../dist\");\nprocess.env.PUBLIC = process.env.VITE_DEV_SERVER_URL\n  ? join(process.env.DIST_ELECTRON, \"../public\")\n  : process.env.DIST;\n// 是否为开发环境\nconst isDev = process.env[\"NODE_ENV\"] === \"development\";\n\n// Disable GPU Acceleration for Windows 7\nif (release().startsWith(\"6.1\")) app.disableHardwareAcceleration();\n\n// Set application name for Windows 10+ notifications\nif (process.platform === \"win32\") app.setAppUserModelId(app.getName());\n\nif (!app.requestSingleInstanceLock()) {\n  app.quit();\n  process.exit(0);\n}\n\n// Remove electron security warnings\n// This warning only shows in development mode\n// Read more on https://www.electronjs.org/docs/latest/tutorial/security\n// process.env['ELECTRON_DISABLE_SECURITY_WARNINGS'] = 'true'\n\nlet win: BrowserWindow | null = null; // Will become mainWindow\nlet backendProcess: ChildProcess | null = null; // 后端进程\nlet backendPID: number | null = null; // 存储后端进程PID\nconst runningBackendProcesses: number[] = []; // 存储所有正在运行的后端进程PID\nlet rabbitmqProcess: ChildProcess | null = null; // RabbitMQ进程\nlet isShuttingDown = false; // 添加关闭标志\n\n// 用于暂存找不到目标窗口时的模型完成事件\nconst pendingModelCompleteEvents: Map<string, any> = new Map();\n\n// Here, you can also use other preload\nconst preload = join(__dirname, \"../preload/index.js\");\nconst url = process.env.VITE_DEV_SERVER_URL;\nconst indexHtml = join(process.env.DIST, \"index.html\");\n\n// 终止进程\nasync function killProcess(pid: number): Promise<boolean> {\n  return new Promise((resolve) => {\n    if (!pid || pid <= 0) {\n      resolve(false);\n      return;\n    }\n\n    console.log(`Attempting to kill process with PID: ${pid}`);\n\n    try {\n      // Windows使用taskkill命令终止进程树\n      if (process.platform === \"win32\") {\n        exec(`taskkill /pid ${pid} /T /F`, (err) => {\n          if (err) {\n            console.error(`Failed to kill process ${pid}:`, err);\n            resolve(false);\n          } else {\n            console.log(`Successfully terminated process ${pid}`);\n            // 从正在运行的进程列表中删除\n            const index = runningBackendProcesses.indexOf(pid);\n            if (index !== -1) {\n              runningBackendProcesses.splice(index, 1);\n            }\n            resolve(true);\n          }\n        });\n      } else {\n        // 非Windows平台使用process.kill\n        try {\n          process.kill(pid, \"SIGTERM\");\n          setTimeout(() => {\n            try {\n              // 检查进程是否还存在\n              process.kill(pid, 0);\n              // 如果没有抛出异常，则进程仍然存在，尝试SIGKILL\n              process.kill(pid, \"SIGKILL\");\n              console.log(`Had to use SIGKILL for ${pid}`);\n            } catch (e) {\n              // 进程已经终止\n            }\n\n            // 从正在运行的进程列表中删除\n            const index = runningBackendProcesses.indexOf(pid);\n            if (index !== -1) {\n              runningBackendProcesses.splice(index, 1);\n            }\n\n            resolve(true);\n          }, 1000);\n        } catch (e) {\n          console.error(`Failed to kill process ${pid}:`, e);\n          resolve(false);\n        }\n      }\n    } catch (error) {\n      console.error(`Error in killProcess for PID ${pid}:`, error);\n      resolve(false);\n    }\n  });\n}\n\n// 启动后端服务\nasync function startBackendService(): Promise<boolean> {\n  return new Promise(async (resolve) => {\n    try {\n      // 先检查是否有已运行的后端进程\n      const existingPids = await checkExistingBackendProcesses();\n\n      // 终止所有已存在的进程\n      for (const pid of existingPids) {\n        await killProcess(pid);\n      }\n\n      // 如果当前已有进程，则不再启动新的\n      if (backendProcess !== null && backendPID !== null) {\n        console.log(`Backend service already running with PID: ${backendPID}`);\n        resolve(true);\n        return;\n      }\n\n      const backendExecutablePath = app.isPackaged\n        ? join(process.resourcesPath, \"backend\", \"runserver.exe\")\n        : join(app.getAppPath(), \"../backend/dist/backend\", \"runserver.exe\");\n\n      console.log(\"Resource path:\", process.resourcesPath);\n      console.log(`Starting backend service from: ${backendExecutablePath}`);\n\n      // Check if the file exists before spawning\n      if (!existsSync(backendExecutablePath)) {\n        console.error(\n          `Backend executable not found at: ${backendExecutablePath}`,\n        );\n        resolve(false);\n        return;\n      }\n\n      console.log(`Backend executable exists, attempting to spawn process`);\n\n      backendProcess = spawn(backendExecutablePath, [], {\n        windowsHide: false, // 在Windows上显示控制台窗口\n        stdio: \"pipe\", // 改为pipe以便捕获输出\n        cwd: app.isPackaged\n          ? join(process.resourcesPath, \"backend\")\n          : undefined, // Set working directory to backend folder in packaged app\n        detached: false, // 确保进程不会脱离父进程\n      });\n\n      if (!backendProcess || !backendProcess.pid) {\n        console.error(\"Failed to start backend service: Invalid process\");\n        resolve(false);\n        return;\n      }\n\n      backendPID = backendProcess.pid;\n      runningBackendProcesses.push(backendPID);\n\n      console.log(\"Backend service started with PID:\", backendPID);\n\n      // 监听后端输出，确认启动成功\n      let startupTimeout: NodeJS.Timeout;\n      let isStarted = false;\n      let hasFlaskApp = false;\n      let hasRunningOn = false;\n\n      const checkStartup = (data: Buffer) => {\n        const output = data.toString();\n        console.log(\"Backend output:\", output);\n\n        // 检查Flask应用是否启动\n        if (\n          output.includes(\"Serving Flask app\") ||\n          output.includes(\"Flask 应\")\n        ) {\n          hasFlaskApp = true;\n          console.log(\"Flask app detected\");\n        }\n\n        // 检查服务器是否开始监听\n        if (\n          output.includes(\"Running on http://\") ||\n          output.includes(\"* Running on all addresses\") ||\n          output.includes(\"Press CTRL+C to quit\")\n        ) {\n          hasRunningOn = true;\n          console.log(\"Server listening detected\");\n        }\n\n        // 只有当两个条件都满足时才认为启动成功\n        if (hasFlaskApp && hasRunningOn && !isStarted) {\n          isStarted = true;\n          clearTimeout(startupTimeout);\n          console.log(\n            \"Backend service started successfully - Flask app is serving and listening\",\n          );\n          resolve(true);\n        }\n\n        // 或者直接检测到完整的启动信息\n        if (\n          (output.includes(\"Running on http://\") &&\n            output.includes(\"Press CTRL+C to quit\")) ||\n          output.includes(\"Server started\") ||\n          output.includes(\"Backend ready\") ||\n          output.includes(\"服务已启动\")\n        ) {\n          if (!isStarted) {\n            isStarted = true;\n            clearTimeout(startupTimeout);\n            console.log(\"Backend service started successfully\");\n            resolve(true);\n          }\n        }\n      };\n\n      const checkStartupError = (data: Buffer) => {\n        const output = data.toString();\n        console.log(\"Backend error:\", output);\n\n        // 检查Flask应用是否启动（有些信息可能在stderr中）\n        if (\n          output.includes(\"Serving Flask app\") ||\n          output.includes(\"Flask 应\")\n        ) {\n          hasFlaskApp = true;\n          console.log(\"Flask app detected (from stderr)\");\n        }\n\n        // 检查服务器是否开始监听（有些信息可能在stderr中）\n        if (\n          output.includes(\"Running on http://\") ||\n          output.includes(\"* Running on all addresses\") ||\n          output.includes(\"Press CTRL+C to quit\")\n        ) {\n          hasRunningOn = true;\n          console.log(\"Server listening detected (from stderr)\");\n        }\n\n        // 只有当两个条件都满足时才认为启动成功\n        if (hasFlaskApp && hasRunningOn && !isStarted) {\n          isStarted = true;\n          clearTimeout(startupTimeout);\n          console.log(\n            \"Backend service started successfully - Flask app is serving and listening (from stderr)\",\n          );\n          resolve(true);\n        }\n\n        // 检查真正的错误信息\n        if (\n          output.includes(\"Address already in use\") ||\n          output.includes(\"Permission denied\") ||\n          output.includes(\"Fatal error\") ||\n          output.includes(\"Failed to start\") ||\n          (output.includes(\"Error:\") &&\n            !output.includes(\"INFO\") &&\n            !output.includes(\"WARNING\"))\n        ) {\n          if (!isStarted) {\n            console.error(\"Backend startup error detected:\", output);\n            clearTimeout(startupTimeout);\n            resolve(false);\n          }\n        }\n      };\n\n      if (backendProcess.stdout) {\n        backendProcess.stdout.on(\"data\", checkStartup);\n      }\n\n      if (backendProcess.stderr) {\n        backendProcess.stderr.on(\"data\", checkStartupError);\n      }\n\n      backendProcess.on(\"error\", (err) => {\n        console.error(\"Failed to start backend service:\", err);\n\n        if (backendPID !== null) {\n          const index = runningBackendProcesses.indexOf(backendPID);\n          if (index !== -1) {\n            runningBackendProcesses.splice(index, 1);\n          }\n        }\n\n        backendProcess = null;\n        backendPID = null;\n\n        if (!isStarted) {\n          clearTimeout(startupTimeout);\n          resolve(false);\n        }\n      });\n\n      backendProcess.on(\"close\", (code) => {\n        console.log(`Backend service exited with code ${code}`);\n\n        if (backendPID !== null) {\n          const index = runningBackendProcesses.indexOf(backendPID);\n          if (index !== -1) {\n            runningBackendProcesses.splice(index, 1);\n          }\n        }\n\n        backendProcess = null;\n        backendPID = null;\n\n        if (!isStarted && !isShuttingDown) {\n          clearTimeout(startupTimeout);\n          resolve(false);\n        }\n      });\n\n      // 设置启动超时 - 增加超时时间，因为后端需要初始化数据库等\n      startupTimeout = setTimeout(() => {\n        if (!isStarted) {\n          console.error(\"Backend service startup timeout\");\n          console.log(\n            `Startup status: hasFlaskApp=${hasFlaskApp}, hasRunningOn=${hasRunningOn}`,\n          );\n          resolve(false);\n        }\n      }, 45000); // 增加到45秒超时\n    } catch (error) {\n      console.error(\"Error starting backend service:\", error);\n      backendProcess = null;\n      backendPID = null;\n      resolve(false);\n    }\n  });\n}\n\n// 终止所有后端进程\nasync function terminateAllBackendProcesses() {\n  console.log(\"Terminating all backend processes...\");\n\n  // 首先尝试终止当前记录的进程\n  if (backendProcess !== null && backendPID !== null) {\n    try {\n      await killProcess(backendPID);\n    } catch (error) {\n      console.error(\n        `Error terminating current backend process ${backendPID}:`,\n        error,\n      );\n    }\n    backendProcess = null;\n    backendPID = null;\n  }\n\n  // 然后终止所有已知的后端进程\n  const processes = [...runningBackendProcesses];\n  for (const pid of processes) {\n    await killProcess(pid);\n  }\n\n  // 最后检查系统中是否还有任何runserver.exe进程\n  const remainingPids = await checkExistingBackendProcesses();\n  for (const pid of remainingPids) {\n    await killProcess(pid);\n  }\n}\n\n// 终止所有RabbitMQ相关进程\nasync function terminateAllRabbitMQProcesses() {\n  console.log(\"Terminating all RabbitMQ processes...\");\n\n  // 首先尝试正常终止当前RabbitMQ进程\n  if (rabbitmqProcess !== null && rabbitmqProcess.pid) {\n    try {\n      console.log(\n        `Terminating current RabbitMQ process with PID: ${rabbitmqProcess.pid}`,\n      );\n      await terminateRabbitMQ();\n    } catch (error) {\n      console.error(`Error terminating current RabbitMQ process:`, error);\n    }\n    rabbitmqProcess = null;\n  }\n\n  // 然后检查并终止系统中所有RabbitMQ相关进程\n  try {\n    const remainingPids = await checkExistingRabbitMQProcesses();\n    console.log(\n      `Found ${remainingPids.length} remaining RabbitMQ processes to terminate`,\n    );\n    for (const pid of remainingPids) {\n      await killProcess(pid);\n    }\n  } catch (error) {\n    console.error(`Error checking for remaining RabbitMQ processes:`, error);\n  }\n}\n\n// 创建菜单\nfunction createMenu(label = \"进入全屏幕\") {\n  const menu = Menu.buildFromTemplate(\n    appMenu(label) as (MenuItemConstructorOptions | MenuItem)[],\n  );\n  Menu.setApplicationMenu(menu);\n}\n\nasync function createMainWindow(initialRoute?: string) {\n  // Renamed from createWindow, added initialRoute\n  win = new BrowserWindow({\n    width: 1024,\n    height: 768,\n    minWidth: 1024,\n    minHeight: 768,\n    title: \"Main window\",\n    icon: join(process.env.PUBLIC, \"favicon.ico\"),\n    frame: false, // Remove default window frame\n    transparent: true, // Enable transparency\n    resizable: true, // Allow window resizing\n    webPreferences: {\n      preload,\n      nodeIntegration: false, // Recommended for security\n      contextIsolation: true, // Required for contextBridge\n    },\n  });\n\n  // 窗口创建后立即最大化\n  win.maximize();\n\n  const targetUrl = initialRoute ? `${url}#${initialRoute}` : url;\n  const targetIndexHtml = initialRoute\n    ? { pathname: indexHtml, hash: initialRoute }\n    : indexHtml;\n\n  if (process.env.VITE_DEV_SERVER_URL) {\n    // electron-vite-vue#298\n    win.loadURL(targetUrl); // MainWindow loads the main content\n    // Open devTool if the app is not packaged\n    win.webContents.openDevTools({ mode: \"bottom\" }); // 打开开发者工具\n  } else {\n    win.loadFile(\n      typeof targetIndexHtml === \"string\"\n        ? targetIndexHtml\n        : targetIndexHtml.pathname,\n      typeof targetIndexHtml === \"string\" ? {} : { hash: targetIndexHtml.hash },\n    ); // MainWindow loads the main content\n  }\n\n  createMenu();\n\n  // Test actively push message to the Electron-Renderer\n  win.webContents.on(\"did-finish-load\", () => {\n    win?.webContents.send(\"main-process-message\", new Date().toLocaleString());\n  });\n\n  // Make all links open with the browser, not with the application\n  win.webContents.setWindowOpenHandler(({ url }) => {\n    // if (url.startsWith(\"https:\")) shell.openExternal(url);\n    // return { action: \"deny\" };\n    // 这里用于新开一个页面--------->必须要把这个设置成允许，否则将不能使用路由打开一个子页面\n\n    // 创建自定义窗口，隐藏菜单栏防止出现bug\n    const isModelResultWindow = url.includes(\"/modelManagement/\");\n    const childWindow = new BrowserWindow({\n      width: 1024,\n      height: 768,\n      minWidth: 1024,\n      minHeight: 768,\n      autoHideMenuBar: true, // 隐藏菜单栏，防止出现bug\n      frame: !isModelResultWindow, // 对模型结果页面使用无边框窗口\n      transparent: isModelResultWindow, // 为模型结果窗口启用透明\n      backgroundColor: isModelResultWindow ? \"#00000000\" : \"#fff\", // 透明背景\n      webPreferences: {\n        preload,\n        nodeIntegration: false,\n        contextIsolation: true,\n      },\n      ...(isModelResultWindow\n        ? {\n            show: false, // 先不显示，等加载完成后再显示\n          }\n        : {}),\n    });\n\n    childWindow.loadURL(url);\n\n    // 如果是模型结果窗口，等待内容加载完成后显示\n    if (isModelResultWindow) {\n      childWindow.once(\"ready-to-show\", () => {\n        childWindow.show();\n      });\n    }\n\n    return { action: \"deny\" }; // 阻止默认行为，使用我们自定义的窗口\n  });\n  // win.webContents.on('will-navigate', (event, url) => { }) #344\n\n  // 窗口进入全屏状态时触发\n  win.on(\"enter-full-screen\", () => {\n    createMenu(\"退出全屏幕\");\n  });\n\n  // 窗口离开全屏状态时触发\n  win.on(\"leave-full-screen\", () => {\n    createMenu();\n  });\n}\n\n// 存储任务ID和相关窗口的映射\nconst taskWindowMap = new Map<number, BrowserWindow>();\n\n// 调试函数\nconst printEventData = (eventName: string, data: any) => {\n  console.log(`[IPC Event] ${eventName} - 接收数据:`, data);\n  console.log(`[IPC Event] ${eventName} - taskUid类型:`, typeof data.taskUid);\n  console.log(`[IPC Event] ${eventName} - taskUid值:`, data.taskUid);\n  if (data.taskUid === undefined) {\n    console.log(`[IPC Event] ${eventName} - 警告: taskUid未定义!`);\n  }\n};\n\n// WebSocket实例存储 - 只在主窗口维护一个WebSocket连接\nlet mainWindowSocket: any = null;\n\n// 从主窗口获取WebSocket连接\nconst getMainWindowSocketStatus = () => {\n  if (!win) return { connected: false };\n\n  return new Promise((resolve) => {\n    win?.webContents\n      .executeJavaScript(\n        `\n      (function() {\n        const socket = window.socketInstance;\n        return { \n          connected: socket ? socket.connected : false,\n          id: socket ? socket.id : null\n        };\n      })()\n    `,\n      )\n      .then((status) => {\n        console.log(\"Main window socket status:\", status);\n        resolve(status);\n      })\n      .catch((error) => {\n        console.error(\"Error getting socket status:\", error);\n        resolve({ connected: false });\n      });\n  });\n};\n\n// 监听模型进度更新，并转发到相关窗口\nipcMain.on(\"model_progress_update\", (event, data) => {\n  printEventData(\"model_progress_update\", data);\n\n  const taskUid = data.taskUid;\n  const targetWindow = taskWindowMap.get(taskUid);\n\n  console.log(\n    `Progress update for task ${taskUid}, target window exists: ${!!targetWindow}`,\n  );\n\n  if (targetWindow && !targetWindow.isDestroyed()) {\n    targetWindow.webContents.send(\"model_progress_update\", data);\n  } else {\n    console.log(\n      `未找到窗口，当前映射中的taskUid有:`,\n      Array.from(taskWindowMap.keys()),\n    );\n  }\n});\n\n// 监听模型完成事件，并转发到相关窗口\nipcMain.on(\"model_complete\", (event, data) => {\n  console.log(data);\n  const taskUid = data.taskUid;\n  const targetWindow = taskWindowMap.get(taskUid);\n\n  console.log(\n    `Model complete for task ${taskUid}, target window exists: ${!!targetWindow}`,\n  );\n\n  // 无论当前是否找到窗口，一律缓存，保证后续窗口可获取\n  pendingModelCompleteEvents.set(taskUid, data);\n\n  if (targetWindow && !targetWindow.isDestroyed()) {\n    targetWindow.webContents.send(\"model_complete\", data);\n  }\n});\n\n// 监听打开模型结果窗口事件\nipcMain.on(\"open_model_result_window\", (event, data) => {\n  printEventData(\"open_model_result_window\", data);\n  const taskUid = data.taskUid;\n\n  if (!taskUid) {\n    console.error(\"无效的taskUid:\", data.taskUid);\n    return;\n  }\n\n  console.log(\n    `Registering window for task ${taskUid} from open_model_result_window event`,\n  );\n\n  // 将发送事件的渲染进程所属的窗口标记为该任务ID对应的窗口\n  const sourceWindow = BrowserWindow.fromWebContents(event.sender);\n  if (sourceWindow) {\n    taskWindowMap.set(taskUid, sourceWindow);\n  }\n});\n\n// 监听模型结果窗口准备就绪事件\nipcMain.on(\"model_result_window_ready\", (event, data) => {\n  printEventData(\"model_result_window_ready\", data);\n\n  const taskUid = data.taskUid;\n\n  if (!taskUid) {\n    console.error(\"无效的taskUid:\", data.taskUid);\n    return;\n  }\n\n  console.log(`Model result window ready for task ${taskUid}`);\n\n  // 将当前窗口注册为接收该任务ID的窗口\n  const resultWindow = BrowserWindow.fromWebContents(event.sender);\n  if (resultWindow) {\n    taskWindowMap.set(taskUid, resultWindow);\n    console.log(`Model result window registered for task ID: ${taskUid}`);\n\n    // 如果之前已经收到完成事件但窗口尚未就绪，现在立即发送\n    const pendingData = pendingModelCompleteEvents.get(taskUid);\n    if (pendingData) {\n      resultWindow.webContents.send(\"model_complete\", pendingData);\n      pendingModelCompleteEvents.delete(taskUid);\n    }\n  }\n});\n\n// 监听模型信息请求 - 由主窗口处理WebSocket请求\nipcMain.on(\"request_model_info\", async (event, data) => {\n  printEventData(\"request_model_info\", data);\n\n  const taskUid = data.taskUid;\n\n  if (!taskUid) {\n    console.error(\"无效的taskUid:\", data.taskUid);\n    event.sender.send(\"model_info_response\", {\n      code: 400,\n      msg: \"无效的任务ID\",\n    });\n    return;\n  }\n\n  console.log(`处理模型信息请求，taskUid: ${taskUid}`);\n\n  // 确保将当前窗口注册为该任务的接收窗口\n  const resultWindow = BrowserWindow.fromWebContents(event.sender);\n  if (resultWindow) {\n    taskWindowMap.set(taskUid, resultWindow);\n  }\n\n  // 在主窗口中执行WebSocket请求\n  if (!win || win.isDestroyed()) {\n    console.error(\"主窗口不可用\");\n    event.sender.send(\"model_info_response\", {\n      code: 500,\n      msg: \"主窗口不可用，无法执行WebSocket请求\",\n    });\n    return;\n  }\n\n  try {\n    // 在主窗口中执行WebSocket请求\n    const result = await win.webContents.executeJavaScript(`\n      (function() {\n        return new Promise((resolve) => {\n          const socket = window.socketInstance;\n          if (!socket || !socket.connected) {\n            resolve({ code: 500, msg: 'WebSocket未连接' });\n            return;\n          }\n          \n          console.log('在主窗口中发送get_model_info请求, taskUid: ' + '${taskUid}');\n          socket.emit('get_model_info', { taskUid: '${taskUid}' }, (response) => {\n            console.log('主窗口收到get_model_info响应:', response);\n            resolve(response || { code: 404, msg: '未收到响应' });\n          });\n          \n          // 设置超时\n          setTimeout(() => {\n            resolve({ code: 408, msg: '请求超时' });\n          }, 10000);\n        });\n      })()\n    `);\n\n    console.log(\"从主窗口获取到模型信息结果:\", result);\n\n    // 将结果发送回请求窗口\n    event.sender.send(\"model_info_response\", result);\n  } catch (error: any) {\n    console.error(\"执行WebSocket请求失败:\", error);\n    event.sender.send(\"model_info_response\", {\n      code: 500,\n      msg: \"执行请求失败: \" + (error.message || \"未知错误\"),\n    });\n  }\n});\n\n// 启动RabbitMQ服务\nasync function startRabbitMQ(): Promise<boolean> {\n  return new Promise(async (resolve) => {\n    try {\n      // 先检查是否有已运行的RabbitMQ进程\n      const existingPids = await checkExistingRabbitMQProcesses();\n\n      // 终止所有已存在的进程\n      if (existingPids.length > 0) {\n        console.log(\n          `Terminating ${existingPids.length} existing RabbitMQ processes before starting new one`,\n        );\n        for (const pid of existingPids) {\n          await killProcess(pid);\n        }\n\n        // 等待一些时间确保进程完全终止\n        await new Promise((resolve) => setTimeout(resolve, 2000));\n      }\n\n      // 获取RabbitMQ脚本路径\n      const rabbitmqScriptPath = app.isPackaged\n        ? join(process.resourcesPath, \"start_rabbitmq.bat\")\n        : join(app.getAppPath(), \"../start_rabbitmq.bat\");\n\n      console.log(`Starting RabbitMQ service from: ${rabbitmqScriptPath}`);\n\n      // 检查脚本文件是否存在\n      if (!existsSync(rabbitmqScriptPath)) {\n        console.error(`RabbitMQ script not found at: ${rabbitmqScriptPath}`);\n        resolve(false);\n        return;\n      }\n\n      console.log(`RabbitMQ script exists, attempting to start service`);\n\n      // 计算工作目录路径\n      const workingDir = app.isPackaged\n        ? dirname(rabbitmqScriptPath) // 在打包后的环境中，使用脚本所在目录作为工作目录\n        : join(app.getAppPath(), \"..\"); // 在开发环境中，使用项目根目录\n\n      console.log(`RabbitMQ working directory: ${workingDir}`);\n\n      // 启动RabbitMQ\n      rabbitmqProcess = spawn(rabbitmqScriptPath, [], {\n        windowsHide: false, // 显示命令提示符窗口以便查看输出\n        stdio: \"pipe\", // 改为pipe以便捕获输出\n        cwd: workingDir, // 设置工作目录\n        shell: true, // 使用shell执行，确保批处理文件可以正确执行\n      });\n\n      if (!rabbitmqProcess || !rabbitmqProcess.pid) {\n        console.error(\"Failed to start RabbitMQ: Invalid process\");\n        resolve(false);\n        return;\n      }\n\n      console.log(\"RabbitMQ process started with PID:\", rabbitmqProcess.pid);\n\n      // 监听RabbitMQ输出，确认启动成功\n      let startupTimeout: NodeJS.Timeout;\n      let isStarted = false;\n\n      const checkStartup = (data: Buffer) => {\n        const output = data.toString();\n        console.log(\"RabbitMQ output:\", output);\n\n        // 检查RabbitMQ是否启动成功的标志\n        // 根据实际的RabbitMQ输出调整这些条件\n        if (\n          output.includes(\"completed with\") ||\n          output.includes(\"started\") ||\n          (output.includes(\"RabbitMQ\") && output.includes(\"running\")) ||\n          output.includes(\"broker running\") ||\n          output.includes(\"Starting broker\")\n        ) {\n          isStarted = true;\n          clearTimeout(startupTimeout);\n          console.log(\"RabbitMQ service started successfully\");\n          resolve(true);\n        }\n\n        // 检查错误信息\n        if (\n          output.includes(\"error\") ||\n          output.includes(\"failed\") ||\n          output.includes(\"could not start\")\n        ) {\n          console.error(\"RabbitMQ startup error detected:\", output);\n          clearTimeout(startupTimeout);\n          resolve(false);\n        }\n      };\n\n      if (rabbitmqProcess.stdout) {\n        rabbitmqProcess.stdout.on(\"data\", checkStartup);\n      }\n\n      if (rabbitmqProcess.stderr) {\n        rabbitmqProcess.stderr.on(\"data\", (data) => {\n          const errorOutput = data.toString();\n          console.error(\"RabbitMQ error:\", errorOutput);\n          checkStartup(data); // 也检查错误输出\n        });\n      }\n\n      // 监听进程事件\n      rabbitmqProcess.on(\"error\", (err) => {\n        console.error(\"Failed to start RabbitMQ service:\", err);\n        rabbitmqProcess = null;\n\n        if (!isStarted) {\n          clearTimeout(startupTimeout);\n          resolve(false);\n        }\n      });\n\n      rabbitmqProcess.on(\"close\", (code) => {\n        console.log(`RabbitMQ service exited with code ${code}`);\n        rabbitmqProcess = null;\n\n        if (!isStarted && !isShuttingDown) {\n          clearTimeout(startupTimeout);\n          resolve(false);\n        }\n      });\n\n      // 设置启动超时\n      startupTimeout = setTimeout(() => {\n        if (!isStarted) {\n          console.error(\"RabbitMQ service startup timeout\");\n          resolve(false);\n        }\n      }, 60000); // 60秒超时，RabbitMQ可能需要更长时间\n    } catch (error) {\n      console.error(\"Error starting RabbitMQ service:\", error);\n      rabbitmqProcess = null;\n      resolve(false);\n    }\n  });\n}\n\n// 终止RabbitMQ服务\nasync function terminateRabbitMQ(): Promise<void> {\n  return new Promise((resolve) => {\n    if (rabbitmqProcess === null) {\n      console.log(\"RabbitMQ process is already null\");\n      resolve();\n      return;\n    }\n\n    console.log(\"Terminating RabbitMQ service...\");\n\n    try {\n      // 在Windows下，由于使用批处理文件启动，需要使用特殊方法终止\n      if (process.platform === \"win32\" && rabbitmqProcess.pid) {\n        // 使用taskkill终止进程树\n        exec(`taskkill /F /T /PID ${rabbitmqProcess.pid}`, (err) => {\n          if (err) {\n            console.error(\"Error terminating RabbitMQ process:\", err);\n          } else {\n            console.log(\"RabbitMQ service terminated successfully.\");\n          }\n          rabbitmqProcess = null;\n          resolve();\n        });\n      } else {\n        rabbitmqProcess.kill();\n        console.log(\"RabbitMQ service terminated successfully.\");\n        rabbitmqProcess = null;\n        resolve();\n      }\n    } catch (error) {\n      console.error(\"Error terminating RabbitMQ service:\", error);\n      rabbitmqProcess = null;\n      resolve();\n    }\n  });\n}\n\napp.whenReady().then(async () => {\n  console.log(\"App ready, starting services...\");\n  const BackendEnabled = import.meta.env.VITE_START_BACKEND;\n\n  if (BackendEnabled === \"true\") {\n    // 在启动前检查并清理可能的残留进程\n    console.log(\"Checking for residual processes from previous runs...\");\n\n    try {\n      // 清理所有可能的残留后端进程\n      const existingBackendPids = await checkExistingBackendProcesses();\n      if (existingBackendPids.length > 0) {\n        console.log(\n          `Found ${existingBackendPids.length} residual backend processes, cleaning up...`,\n        );\n        for (const pid of existingBackendPids) {\n          await killProcess(pid);\n        }\n      }\n\n      // 清理所有可能的残留RabbitMQ进程\n      const existingRabbitMQPids = await checkExistingRabbitMQProcesses();\n      if (existingRabbitMQPids.length > 0) {\n        console.log(\n          `Found ${existingRabbitMQPids.length} residual RabbitMQ processes, cleaning up...`,\n        );\n        for (const pid of existingRabbitMQPids) {\n          await killProcess(pid);\n        }\n        // 给进程一些时间完全终止\n        await new Promise((resolve) => setTimeout(resolve, 2000));\n      }\n\n      console.log(\"Residual process cleanup completed\");\n    } catch (error) {\n      console.error(\"Error during residual process cleanup:\", error);\n    }\n\n    // 首先启动RabbitMQ服务\n    console.log(\"Starting RabbitMQ...\");\n    const rabbitmqStarted = await startRabbitMQ();\n\n    if (!rabbitmqStarted) {\n      console.error(\"Failed to start RabbitMQ, aborting startup\");\n      dialog.showErrorBox(\n        \"启动失败\",\n        \"RabbitMQ服务启动失败，请检查配置并重试。\",\n      );\n      app.quit();\n      return;\n    }\n\n    // 等待一段时间让RabbitMQ完全初始化\n    console.log(\"Waiting for RabbitMQ to fully initialize...\");\n    await new Promise((resolve) => setTimeout(resolve, 5000));\n\n    // 然后启动后端服务\n    console.log(\"Starting backend service...\");\n    const backendStarted = await startBackendService();\n\n    if (!backendStarted) {\n      console.error(\"Failed to start backend service, aborting startup\");\n      dialog.showErrorBox(\"启动失败\", \"后端服务启动失败，请检查配置并重试。\");\n      await terminateRabbitMQ();\n      app.quit();\n      return;\n    }\n\n    // 等待后端服务完全初始化\n    console.log(\"Waiting for backend service to fully initialize...\");\n    await new Promise((resolve) => setTimeout(resolve, 3000));\n  }\n  // 最后创建启动屏幕\n  console.log(\"All services started successfully, creating UI...\");\n  createMainWindow(\"/welcome\");\n});\n\napp.on(\"window-all-closed\", () => {\n  // Splash window might be closed before main window, ensure 'win' refers to mainWindow\n  if (BrowserWindow.getAllWindows().length === 0) {\n    // Check if all windows are closed\n    app.quit();\n  }\n});\n\n// 在应用退出前关闭后端服务\napp.on(\"before-quit\", async (event) => {\n  const BackendEnabled = import.meta.env.VITE_START_BACKEND;\n  if (!isShuttingDown && BackendEnabled === \"true\") {\n    event.preventDefault();\n    isShuttingDown = true;\n\n    console.log(\"Application is quitting, terminating all services...\");\n\n    // 并行终止服务，但设置超时防止卡死\n    const terminateWithTimeout = async (\n      terminateFunc: () => Promise<void>,\n      name: string,\n      timeout: number,\n    ) => {\n      return Promise.race([\n        terminateFunc(),\n        new Promise<void>((resolve) => {\n          setTimeout(() => {\n            console.warn(`${name} termination timeout after ${timeout}ms`);\n            resolve();\n          }, timeout);\n        }),\n      ]);\n    };\n\n    try {\n      await Promise.all([\n        terminateWithTimeout(\n          async () => await terminateAllRabbitMQProcesses(),\n          \"RabbitMQ\",\n          5000,\n        ),\n        terminateWithTimeout(\n          async () => await terminateAllBackendProcesses(),\n          \"Backend\",\n          5000,\n        ),\n      ]);\n      console.log(\"All processes terminated successfully\");\n    } catch (error) {\n      console.error(\"Error during service termination:\", error);\n    }\n\n    // 最后一次检查是否还有残留进程\n    try {\n      const remainingBackendPids = await checkExistingBackendProcesses();\n      const remainingRabbitMQPids = await checkExistingRabbitMQProcesses();\n\n      const allRemainingPids = [\n        ...remainingBackendPids,\n        ...remainingRabbitMQPids,\n      ];\n      if (allRemainingPids.length > 0) {\n        console.log(\n          `Found ${allRemainingPids.length} remaining processes, attempting final cleanup...`,\n        );\n        await Promise.all(allRemainingPids.map((pid) => killProcess(pid)));\n      }\n    } catch (error) {\n      console.error(\"Error during final process cleanup:\", error);\n    }\n\n    // 确保所有服务都已终止后再退出\n    setTimeout(() => {\n      console.log(\"Clean exit completed, quitting application\");\n      app.exit(0);\n    }, 1000);\n  }\n});\n\n// 确保进程在退出时清理\nprocess.on(\"exit\", () => {\n  const BackendEnabled = import.meta.env.VITE_START_BACKEND;\n  if (BackendEnabled === \"true\") {\n    console.log(\"Node process exiting, attempting final cleanup\");\n\n    // 在进程退出时只能同步操作\n    if (process.platform === \"win32\") {\n      const { execSync } = require(\"child_process\");\n\n      // 尝试终止后端进程\n      if (backendPID) {\n        try {\n          execSync(`taskkill /pid ${backendPID} /T /F`, { stdio: \"ignore\" });\n          console.log(`Terminated backend process ${backendPID} during exit`);\n        } catch (e) {\n          // 忽略错误\n        }\n      }\n\n      // 尝试终止RabbitMQ进程\n      if (rabbitmqProcess && rabbitmqProcess.pid) {\n        try {\n          execSync(`taskkill /pid ${rabbitmqProcess.pid} /T /F`, {\n            stdio: \"ignore\",\n          });\n          console.log(\n            `Terminated RabbitMQ process ${rabbitmqProcess.pid} during exit`,\n          );\n        } catch (e) {\n          // 忽略错误\n        }\n      }\n\n      // 额外检查所有可能残留的后端和RabbitMQ进程\n      try {\n        execSync(\"taskkill /F /IM runserver.exe /T 2>nul\", { stdio: \"ignore\" });\n        execSync(\"taskkill /F /IM erl.exe /T 2>nul\", { stdio: \"ignore\" });\n        execSync(\"taskkill /F /IM epmd.exe /T 2>nul\", { stdio: \"ignore\" });\n        execSync('taskkill /F /IM \"rabbitmq-server.bat\" /T 2>nul', {\n          stdio: \"ignore\",\n        });\n        console.log(\"Terminated all remaining processes during exit\");\n      } catch (e) {\n        // 忽略错误\n      }\n    } else {\n      // 非Windows平台\n      if (backendPID) {\n        try {\n          process.kill(backendPID, \"SIGKILL\");\n          console.log(`Terminated backend process ${backendPID} during exit`);\n        } catch (e) {\n          // 忽略错误\n        }\n      }\n\n      if (rabbitmqProcess && rabbitmqProcess.pid) {\n        try {\n          process.kill(rabbitmqProcess.pid, \"SIGKILL\");\n          console.log(\n            `Terminated RabbitMQ process ${rabbitmqProcess.pid} during exit`,\n          );\n        } catch (e) {\n          // 忽略错误\n        }\n      }\n    }\n  }\n});\n\n// 处理未捕获的异常，确保在崩溃时也能清理进程\nprocess.on(\"uncaughtException\", async (error) => {\n  console.error(\"Uncaught exception:\", error);\n  isShuttingDown = true;\n\n  // 尝试快速清理\n  const cleanup = async () => {\n    console.log(\"Performing emergency cleanup due to uncaught exception\");\n    const promises = [];\n\n    try {\n      // 检查并终止所有后端进程\n      const backendPids = await checkExistingBackendProcesses();\n      for (const pid of backendPids) {\n        promises.push(killProcess(pid));\n      }\n\n      // 检查并终止所有RabbitMQ进程\n      const rabbitmqPids = await checkExistingRabbitMQProcesses();\n      for (const pid of rabbitmqPids) {\n        promises.push(killProcess(pid));\n      }\n\n      await Promise.race([\n        Promise.all(promises),\n        new Promise((resolve) => setTimeout(resolve, 3000)), // 3秒超时\n      ]);\n      console.log(\"Emergency cleanup completed\");\n    } catch (e) {\n      console.error(\"Emergency cleanup error:\", e);\n    }\n  };\n\n  await cleanup();\n  app.exit(1);\n});\n\n// 处理未处理的拒绝Promise，通常是异步错误\nprocess.on(\"unhandledRejection\", async (reason, promise) => {\n  console.error(\"Unhandled Promise rejection:\", reason);\n  // 不退出应用，但记录错误\n  // 如果是严重错误导致应用无法继续，将触发uncaughtException\n});\n\napp.on(\"second-instance\", () => {\n  // Focus on the main window if the user tried to open another\n  if (win && !win.isDestroyed()) {\n    if (win.isMinimized()) win.restore();\n    win.focus();\n  }\n});\n\napp.on(\"activate\", () => {\n  // On macOS it's common to re-create a window in the app when the\n  // dock icon is clicked and there are no other windows open.\n  const allWindows = BrowserWindow.getAllWindows();\n  if (allWindows.length === 0) {\n    createMainWindow(\"/welcome\"); // Always start with welcome\n  } else {\n    // If windows exist, focus the main one\n    if (win && !win.isDestroyed()) {\n      if (win.isMinimized()) win.restore();\n      win.focus();\n    } else {\n      // Fallback if recorded window is destroyed for some reason\n      allWindows[0].focus();\n    }\n  }\n});\n\n// Listener to close splash and open main window\nipcMain.on(\n  \"APP_READY_TO_SHOW_MAIN_WINDOW\",\n  (\n    event,\n    args: {\n      targetRoute?: string;\n      openedFilePath?: string;\n      singleFileMode?: boolean;\n      clearCache?: boolean;\n    } = {},\n  ) => {\n    createMainWindow(args.targetRoute); // Pass targetRoute to createMainWindow\n\n    // If there's a file to open, send it to the main window\n    if (args.openedFilePath) {\n      console.log(\"Main process: Preparing to send file data:\", {\n        filePath: args.openedFilePath,\n        targetRoute: args.targetRoute,\n        singleFileMode: args.singleFileMode,\n      });\n\n      const sendFileData = () => {\n        console.log(\"Main process: Sending file data events\");\n\n        if (args.targetRoute?.includes(\"/dataManagement/imandex\")) {\n          // For Excel files\n          console.log(\"Main process: Sending excel-file-selected event\");\n          win?.webContents.send(\"excel-file-selected\", args.openedFilePath);\n        } else {\n          // For other files\n          console.log(\"Main process: Sending workspace-file-selected event\");\n          win?.webContents.send(\"workspace-file-selected\", args.openedFilePath);\n        }\n\n        if (args.singleFileMode) {\n          console.log(\"Main process: Sending set-single-file-mode event\");\n          win?.webContents.send(\"set-single-file-mode\", args.openedFilePath);\n        }\n      };\n\n      win?.webContents.once(\"did-finish-load\", sendFileData);\n      win?.webContents.once(\"dom-ready\", sendFileData);\n      setTimeout(sendFileData, 1000);\n    }\n  },\n);\n\n// 菜单栏 https://www.electronjs.org/zh/docs/latest/api/menu-item#%E8%8F%9C%E5%8D%95%E9%A1%B9\nconst appMenu = (fullscreenLabel: string) => {\n  // 开发者工具菜单项（仅在开发模式下显示）\n  const devMenuItems: MenuItemConstructorOptions[] = [];\n  if (isDev) {\n    devMenuItems.push(\n      { label: \"开发者工具\", role: \"toggleDevTools\" },\n      { label: \"强制刷新\", role: \"forceReload\" },\n    );\n  }\n\n  const template: Array<MenuItemConstructorOptions | MenuItem> = [\n    {\n      label: \"文件\",\n      submenu: [\n        {\n          label: \"导入项目...\",\n          accelerator: \"CmdOrCtrl+Shift+O\",\n          // 添加动效提示\n          toolTip: \"导入现有项目文件夹\",\n          click: async () => {\n            if (win && !win.isDestroyed()) {\n              win.focus();\n              win.webContents.send(\"menu-triggered-import-project\");\n            } else {\n              const dialogOptions: Electron.OpenDialogOptions = {\n                properties: [\"openDirectory\"],\n              };\n              const directoryPathResult =\n                await dialog.showOpenDialog(dialogOptions);\n\n              if (\n                !directoryPathResult.canceled &&\n                directoryPathResult.filePaths.length > 0\n              ) {\n                const projectPath = directoryPathResult.filePaths[0];\n                createMainWindow(\n                  `/workspace/${encodeURIComponent(projectPath)}`,\n                );\n              }\n            }\n          },\n        },\n        {\n          label: \"打开文件...\",\n          accelerator: \"CmdOrCtrl+O\",\n          toolTip: \"打开单个文件进行编辑\",\n          click: async () => {\n            if (win && !win.isDestroyed()) {\n              win.focus();\n\n              // 获取当前窗口URL，根据URL类型决定如何处理文件打开\n              const currentURL = win.webContents.getURL();\n              console.log(\"Current window URL:\", currentURL);\n\n              // 检查是否在数据导入导出页面\n              const isDataImportPage = currentURL.includes(\n                \"/dataManagement/imandex\",\n              );\n\n              // 如果当前页面是数据导入导出页面，直接发送到该页面处理\n              if (isDataImportPage) {\n                console.log(\n                  \"Sending menu-triggered-open-file to dataImandEx page\",\n                );\n                win.webContents.send(\"menu-triggered-open-file\");\n              } else {\n                // 其他页面使用标准文件选择对话框\n                const dialogOptions: Electron.OpenDialogOptions = {\n                  properties: [\"openFile\"],\n                };\n                const filePathResult =\n                  await dialog.showOpenDialog(dialogOptions);\n\n                if (\n                  !filePathResult.canceled &&\n                  filePathResult.filePaths.length > 0\n                ) {\n                  const filePath = filePathResult.filePaths[0];\n\n                  // Check if it's an Excel file\n                  const isExcelFile = /\\.(xlsx|xls|csv)$/i.test(filePath);\n\n                  if (isExcelFile) {\n                    // For Excel files, navigate to dataImandEx page\n                    createMainWindow(`/dataManagement/imandex`);\n                    // Send the file path to be opened\n                    const sendExcelFile = () => {\n                      win?.webContents.send(\"excel-file-selected\", filePath);\n                      win?.webContents.send(\"set-single-file-mode\", filePath);\n                    };\n                    win?.webContents.once(\"did-finish-load\", sendExcelFile);\n                    win?.webContents.once(\"dom-ready\", sendExcelFile);\n                    setTimeout(sendExcelFile, 1000);\n                  } else {\n                    // For other files, create a workspace with the file\n                    const fileDir = filePath.substring(\n                      0,\n                      filePath.lastIndexOf(\"/\") || filePath.lastIndexOf(\"\\\\\"),\n                    );\n                    createMainWindow(\n                      `/workspace/${encodeURIComponent(fileDir)}`,\n                    );\n                    // Use multiple event listeners to ensure the message is received\n                    const sendWorkspaceFile = () => {\n                      win?.webContents.send(\n                        \"workspace-file-selected\",\n                        filePath,\n                      );\n                      win?.webContents.send(\"set-single-file-mode\", filePath);\n                    };\n                    win?.webContents.once(\"did-finish-load\", sendWorkspaceFile);\n                    win?.webContents.once(\"dom-ready\", sendWorkspaceFile);\n                    // Also send after a delay to ensure components are mounted\n                    setTimeout(sendWorkspaceFile, 1000);\n                  }\n                }\n              }\n            } else {\n              const dialogOptions: Electron.OpenDialogOptions = {\n                properties: [\"openFile\"],\n              };\n              const filePathResult = await dialog.showOpenDialog(dialogOptions);\n\n              if (\n                !filePathResult.canceled &&\n                filePathResult.filePaths.length > 0\n              ) {\n                const filePath = filePathResult.filePaths[0];\n\n                // Check if it's an Excel file\n                const isExcelFile = /\\.(xlsx|xls|csv)$/i.test(filePath);\n\n                if (isExcelFile) {\n                  // For Excel files, navigate to dataImandEx page\n                  createMainWindow(`/dataManagement/imandex`);\n                  // Send the file path to be opened\n                  const sendExcelFile = () => {\n                    win?.webContents.send(\"excel-file-selected\", filePath);\n                    win?.webContents.send(\"set-single-file-mode\", filePath);\n                  };\n                  win?.webContents.once(\"did-finish-load\", sendExcelFile);\n                  win?.webContents.once(\"dom-ready\", sendExcelFile);\n                  setTimeout(sendExcelFile, 1000);\n                } else {\n                  // For other files, create a workspace with the file\n                  const fileDir = filePath.substring(\n                    0,\n                    filePath.lastIndexOf(\"/\") || filePath.lastIndexOf(\"\\\\\"),\n                  );\n                  createMainWindow(`/workspace/${encodeURIComponent(fileDir)}`);\n                  // Use multiple event listeners to ensure the message is received\n                  const sendWorkspaceFile = () => {\n                    win?.webContents.send(\"workspace-file-selected\", filePath);\n                    win?.webContents.send(\"set-single-file-mode\", filePath);\n                  };\n                  win?.webContents.once(\"did-finish-load\", sendWorkspaceFile);\n                  win?.webContents.once(\"dom-ready\", sendWorkspaceFile);\n                  // Also send after a delay to ensure components are mounted\n                  setTimeout(sendWorkspaceFile, 1000);\n                }\n              }\n            }\n          },\n        },\n        { type: \"separator\" },\n        {\n          label: \"退出\",\n          role: \"quit\",\n          accelerator: \"CmdOrCtrl+Q\",\n        },\n      ],\n    },\n    {\n      label: \"编辑\",\n      submenu: [\n        {\n          label: \"撤销\",\n          role: \"undo\",\n          accelerator: \"CmdOrCtrl+Z\",\n        },\n        {\n          label: \"重做\",\n          role: \"redo\",\n          accelerator: \"CmdOrCtrl+Shift+Z\",\n        },\n        { type: \"separator\" },\n        {\n          label: \"剪切\",\n          role: \"cut\",\n          accelerator: \"CmdOrCtrl+X\",\n        },\n        {\n          label: \"复制\",\n          role: \"copy\",\n          accelerator: \"CmdOrCtrl+C\",\n        },\n        {\n          label: \"粘贴\",\n          role: \"paste\",\n          accelerator: \"CmdOrCtrl+V\",\n        },\n        {\n          label: \"删除\",\n          role: \"delete\",\n          accelerator: \"Delete\",\n        },\n        {\n          label: \"全选\",\n          role: \"selectAll\",\n          accelerator: \"CmdOrCtrl+A\",\n        },\n      ],\n    },\n    {\n      label: \"显示\",\n      submenu: [\n        {\n          label: \"放大\",\n          role: \"zoomIn\",\n          accelerator: \"CmdOrCtrl+Plus\",\n        },\n        {\n          label: \"默认大小\",\n          role: \"resetZoom\",\n          accelerator: \"CmdOrCtrl+0\",\n        },\n        {\n          label: \"缩小\",\n          role: \"zoomOut\",\n          accelerator: \"CmdOrCtrl+-\",\n        },\n        { type: \"separator\" },\n        {\n          label: fullscreenLabel,\n          role: \"togglefullscreen\",\n          accelerator: \"F11\",\n        },\n      ],\n    },\n    // 将开发者工具菜单添加到这里（仅在开发模式下）\n    ...(isDev\n      ? [\n          {\n            label: \"开发\",\n            submenu: devMenuItems,\n          },\n        ]\n      : []),\n    // 关于菜单放在最后\n    {\n      label: \"关于\",\n      submenu: [\n        {\n          label: \"关于应用\",\n          role: \"about\" as const,\n          accelerator: \"F1\",\n        },\n      ],\n    },\n  ];\n\n  return template;\n};\n\n// New window example arg: new windows url\nipcMain.handle(\"open-win\", (_, arg) => {\n  const childWindow = new BrowserWindow({\n    webPreferences: {\n      preload,\n      nodeIntegration: false, // Then these should also be updated\n      contextIsolation: true,\n    },\n  });\n\n  if (process.env.VITE_DEV_SERVER_URL) {\n    childWindow.loadURL(`${url}#${arg}`);\n  } else {\n    childWindow.loadFile(indexHtml, { hash: arg });\n  }\n});\n\n// // 在文件末尾添加以下 IPC 事件监听器\n// ipcMain.handle('select-directory', async () => {\n//   const result = await dialog.showOpenDialog({\n//     properties: ['openDirectory']\n//   })\n//   return result.filePaths[0]\n// })\n\n// ipcMain.handle('create-directory', (_, path: string) => {\n//   if (!existsSync(path)) {\n//     mkdirSync(path, { recursive: true })\n//     return true\n//   }\n//   return false\n// })\n\n// ipcMain.handle('read-file', (_, filePath: string) => {\n//   return readFileSync(filePath, 'utf-8')\n// })\n\n// ipcMain.handle('write-file', (_, filePath: string, content: string) => {\n//   writeFileSync(filePath, content)\n//   return true\n// })\n\n// ipcMain.handle('read-directory', (_, dirPath: string) => {\n//   try {\n//     return readdirSync(dirPath).map(file => {\n//       const fullPath = join(dirPath, file)\n//       const stats: Stats = statSync(fullPath)\n//       return {\n//         name: file,\n//         path: fullPath,\n//         isDirectory: stats.isDirectory(),\n//         size: stats.size,\n//         modified: stats.mtime\n//       }\n//     })\n//   } catch (error) {\n//     console.error('Error reading directory:', error)\n//     return []\n//   }\n// })\n\n// 在文件顶部导入所需模块\nimport fs from \"fs\";\nimport path from \"path\";\nimport { createRequire } from \"node:module\";\n\n// Create require function for dynamic imports\nconst require = createRequire(import.meta.url);\n\n// 在文件末尾（ipcMain.handle(\"open-win\"...)之后）添加以下代码\n\n// 选择目录对话框\nipcMain.handle(\"dialog:openDirectory\", async () => {\n  const result = await dialog.showOpenDialog({\n    properties: [\"openDirectory\"],\n  });\n  return result.filePaths[0]; // 返回选择的第一个路径\n});\n\n// 选择文件对话框\nipcMain.handle(\"dialog:openFile\", async () => {\n  const result = await dialog.showOpenDialog({\n    properties: [\"openFile\"],\n    // You can add filters, e.g., for specific file types\n    // filters: [\n    //   { name: 'Text Files', extensions: ['txt', 'md'] },\n    //   { name: 'All Files', extensions: ['*'] }\n    // ]\n  });\n  if (result.canceled || result.filePaths.length === 0) {\n    return null; // Or handle as preferred\n  }\n  return result.filePaths[0]; // 返回选择的第一个路径\n});\n\n// 读取目录内容\nipcMain.handle(\"fs:readDirectory\", async (_, dirPath: string) => {\n  const files = await fs.promises.readdir(dirPath, { withFileTypes: true });\n  return files.map((dirent) => ({\n    name: dirent.name,\n    isDirectory: dirent.isDirectory(),\n    path: path.join(dirPath, dirent.name),\n  }));\n});\n\n// 创建新目录\nipcMain.handle(\"fs:createDirectory\", async (_, targetPath: string) => {\n  await fs.promises.mkdir(targetPath, { recursive: true });\n  return { success: true };\n});\n\n// 创建新文件\nipcMain.handle(\"fs:createFile\", async (_, filePath: string) => {\n  await fs.promises.writeFile(filePath, \"\");\n  return { success: true };\n});\n\n// 删除文件/目录\nipcMain.handle(\"fs:deletePath\", async (_, targetPath: string) => {\n  const stats = await fs.promises.stat(targetPath);\n  if (stats.isDirectory()) {\n    await fs.promises.rmdir(targetPath, { recursive: true });\n  } else {\n    await fs.promises.unlink(targetPath);\n  }\n  return { success: true };\n});\n\n// 检测文件类型\nconst getFileType = (\n  filePath: string,\n): { type: string; category: string; supported: boolean } => {\n  const ext = path.extname(filePath).toLowerCase();\n\n  // 文本文件\n  const textExtensions = [\n    \".txt\",\n    \".md\",\n    \".json\",\n    \".xml\",\n    \".html\",\n    \".css\",\n    \".js\",\n    \".ts\",\n    \".vue\",\n    \".py\",\n    \".java\",\n    \".cpp\",\n    \".c\",\n    \".h\",\n    \".sql\",\n    \".log\",\n    \".ini\",\n    \".cfg\",\n    \".conf\",\n    \".yaml\",\n    \".yml\",\n  ];\n\n  // Office 文档\n  const officeExtensions = [\".doc\", \".docx\", \".xls\", \".xlsx\", \".ppt\", \".pptx\"];\n\n  // PDF 文档\n  const pdfExtensions = [\".pdf\"];\n\n  // 图片文件\n  const imageExtensions = [\n    \".jpg\",\n    \".jpeg\",\n    \".png\",\n    \".gif\",\n    \".bmp\",\n    \".svg\",\n    \".ico\",\n  ];\n\n  // 压缩文件\n  const archiveExtensions = [\".zip\", \".rar\", \".7z\", \".tar\", \".gz\"];\n\n  // 可执行文件\n  const executableExtensions = [\".exe\", \".msi\", \".dmg\", \".app\", \".deb\", \".rpm\"];\n\n  // 音视频文件\n  const mediaExtensions = [\".mp3\", \".mp4\", \".avi\", \".mov\", \".wav\", \".flac\"];\n\n  if (textExtensions.includes(ext)) {\n    return { type: ext, category: \"text\", supported: true };\n  } else if (officeExtensions.includes(ext)) {\n    return { type: ext, category: \"office\", supported: true };\n  } else if (pdfExtensions.includes(ext)) {\n    return { type: ext, category: \"pdf\", supported: true };\n  } else if (imageExtensions.includes(ext)) {\n    return { type: ext, category: \"image\", supported: true };\n  } else if (archiveExtensions.includes(ext)) {\n    return { type: ext, category: \"archive\", supported: false };\n  } else if (executableExtensions.includes(ext)) {\n    return { type: ext, category: \"executable\", supported: false };\n  } else if (mediaExtensions.includes(ext)) {\n    return { type: ext, category: \"media\", supported: false };\n  } else {\n    return { type: ext, category: \"unknown\", supported: false };\n  }\n};\n\n// 读取文件内容\nipcMain.handle(\"fs:readFile\", async (_, filePath: string) => {\n  try {\n    const content = await fs.promises.readFile(filePath, \"utf-8\");\n    return content;\n  } catch (error) {\n    console.error(\"Error reading file:\", error);\n    throw error;\n  }\n});\n\n// 检测文件类型和读取内容\nipcMain.handle(\"fs:readFileWithType\", async (_, filePath: string) => {\n  try {\n    const fileInfo = getFileType(filePath);\n\n    if (!fileInfo.supported) {\n      return {\n        success: false,\n        fileInfo,\n        error: `不支持的文件类型: ${fileInfo.type}`,\n        message: getUnsupportedMessage(fileInfo),\n      };\n    }\n\n    let content = \"\";\n    let imageData = null;\n\n    if (fileInfo.category === \"text\") {\n      // 直接读取文本文件\n      content = await fs.promises.readFile(filePath, \"utf-8\");\n    } else if (fileInfo.category === \"office\") {\n      // Office 文档需要特殊处理\n      if (fileInfo.type === \".docx\") {\n        content = await extractDocxText(filePath);\n      } else if (fileInfo.type === \".doc\") {\n        content = \"暂不支持 .doc 格式，请转换为 .docx 格式\";\n      } else {\n        content = `不支持的Office 文档 (${fileInfo.type})，请使用Office工具进行编辑`;\n      }\n    } else if (fileInfo.category === \"pdf\") {\n      content = \"PDF 文档，暂不支持文本提取\";\n    } else if (fileInfo.category === \"image\") {\n      // 读取图像文件为 base64\n      const imageBuffer = await fs.promises.readFile(filePath);\n      const base64Data = imageBuffer.toString(\"base64\");\n      const mimeType = getMimeType(fileInfo.type);\n      imageData = `data:${mimeType};base64,${base64Data}`;\n      content = \"\"; // 图像文件不需要文本内容\n    }\n\n    return {\n      success: true,\n      fileInfo,\n      content,\n      imageData,\n    };\n  } catch (error: any) {\n    console.error(\"Error reading file with type:\", error);\n    return {\n      success: false,\n      fileInfo: getFileType(filePath),\n      error: error?.message || \"Unknown error\",\n      message: \"读取文件时发生错误\",\n    };\n  }\n});\n\n// 获取图像文件的 MIME 类型\nconst getMimeType = (extension: string): string => {\n  const mimeTypes: Record<string, string> = {\n    \".jpg\": \"image/jpeg\",\n    \".jpeg\": \"image/jpeg\",\n    \".png\": \"image/png\",\n    \".gif\": \"image/gif\",\n    \".bmp\": \"image/bmp\",\n    \".svg\": \"image/svg+xml\",\n    \".ico\": \"image/x-icon\",\n    \".webp\": \"image/webp\",\n  };\n  return mimeTypes[extension.toLowerCase()] || \"image/jpeg\";\n};\n\n// 获取不支持文件类型的提示信息\nconst getUnsupportedMessage = (fileInfo: {\n  type: string;\n  category: string;\n}) => {\n  switch (fileInfo.category) {\n    case \"image\":\n      return \"图片文件不支持文本编辑，请使用图片查看器打开\";\n    case \"archive\":\n      return \"压缩文件不支持直接编辑，请先解压缩\";\n    case \"executable\":\n      return \"可执行文件不支持编辑\";\n    case \"media\":\n      return \"音视频文件不支持文本编辑，请使用媒体播放器打开\";\n    default:\n      return \"不支持的文件类型，无法在文本编辑器中打开\";\n  }\n};\n\n// 提取 DOCX 文档的文本内容\nconst extractDocxText = async (filePath: string): Promise<string> => {\n  try {\n    // Try to use mammoth if available\n    try {\n      const mammoth = require(\"mammoth\");\n      const result = await mammoth.extractRawText({ path: filePath });\n      return result.value || \"无法提取文档内容\";\n    } catch (mammothError) {\n      console.log(\"Mammoth not available, using fallback method\");\n\n      // Fallback: Try to read as zip and extract document.xml\n      try {\n        const AdmZip = require(\"adm-zip\");\n        const zip = new AdmZip(filePath);\n        const documentXml = zip.readAsText(\"word/document.xml\");\n\n        if (documentXml) {\n          // Simple XML text extraction (removes tags)\n          const textContent = documentXml\n            .replace(/<[^>]*>/g, \" \") // Remove XML tags\n            .replace(/\\s+/g, \" \") // Normalize whitespace\n            .trim();\n\n          return textContent || \"文档内容为空\";\n        }\n      } catch (zipError: any) {\n        console.log(\n          \"ZIP extraction failed:\",\n          zipError?.message || \"Unknown error\",\n        );\n      }\n\n      return `Word 文档预览\\n\\n文件路径: ${filePath}\\n\\n注意：无法提取文档内容，请使用 Microsoft Word 或其他兼容软件打开此文件。\\n\\n要完整支持 Word 文档，请安装 mammoth 库：npm install mammoth`;\n    }\n  } catch (error: any) {\n    console.error(\"Error extracting DOCX text:\", error);\n    return `Word 文档预览\\n\\n文件路径: ${filePath}\\n\\n错误：无法读取文档内容 - ${error?.message || \"Unknown error\"}`;\n  }\n};\n\n// 读取文件内容为Buffer (用于Excel等二进制文件)\nipcMain.handle(\"fs:readFileBuffer\", async (_, filePath: string) => {\n  try {\n    const buffer = await fs.promises.readFile(filePath);\n    return buffer;\n  } catch (error) {\n    console.error(\"Error reading file buffer:\", error);\n    throw error;\n  }\n});\n\n// 写入文件内容\nipcMain.handle(\"fs:writeFile\", async (_, filePath: string, content: string) => {\n  try {\n    await fs.promises.writeFile(filePath, content, \"utf-8\");\n    return { success: true };\n  } catch (error) {\n    console.error(\"Error writing file:\", error);\n    throw error;\n  }\n});\n\n// 写入文件内容 (Buffer)\nipcMain.handle(\n  \"fs:writeFileBuffer\",\n  async (_, filePath: string, buffer: Buffer) => {\n    try {\n      await fs.promises.writeFile(filePath, buffer);\n      return { success: true };\n    } catch (error) {\n      console.error(\"Error writing file buffer:\", error);\n      throw error;\n    }\n  },\n);\n\n// Handle workspace file selection communication\nipcMain.on(\"workspace-file-selected\", (event, filePath: string) => {\n  // Forward the event to all renderer processes (in case there are multiple windows)\n  if (win && !win.isDestroyed()) {\n    win.webContents.send(\"workspace-file-selected\", filePath);\n  }\n});\n\n// Handle Excel file selection from workspace\nipcMain.on(\"excel-file-selected\", (event, filePath: string) => {\n  // Forward the event to all renderer processes\n  if (win && !win.isDestroyed()) {\n    win.webContents.send(\"excel-file-selected\", filePath);\n  }\n});\n\n// Handle app quit request\nipcMain.on(\"app-quit\", () => {\n  app.quit();\n});\n\n// Window control IPC handlers for main window\nipcMain.on(\"minimize-window\", () => {\n  if (win && !win.isDestroyed()) {\n    win.minimize();\n  }\n});\n\nipcMain.on(\"maximize-window\", () => {\n  if (win && !win.isDestroyed()) {\n    if (win.isMaximized()) {\n      win.unmaximize();\n    } else {\n      win.maximize();\n    }\n  }\n});\n\nipcMain.on(\"close-window\", () => {\n  if (win && !win.isDestroyed()) {\n    win.close();\n  }\n});\n\n// Window control IPC handlers for current window (child windows)\nipcMain.on(\"minimize-current-window\", (event) => {\n  const currentWindow = BrowserWindow.fromWebContents(event.sender);\n  if (currentWindow && !currentWindow.isDestroyed()) {\n    currentWindow.minimize();\n  }\n});\n\nipcMain.on(\"maximize-current-window\", (event) => {\n  const currentWindow = BrowserWindow.fromWebContents(event.sender);\n  if (currentWindow && !currentWindow.isDestroyed()) {\n    if (currentWindow.isMaximized()) {\n      currentWindow.unmaximize();\n    } else {\n      currentWindow.maximize();\n    }\n  }\n});\n\nipcMain.on(\"close-current-window\", (event) => {\n  const currentWindow = BrowserWindow.fromWebContents(event.sender);\n  if (currentWindow && !currentWindow.isDestroyed()) {\n    currentWindow.close();\n  }\n});\n\n// Application menu handling\nipcMain.on(\"show-file-menu\", (event, position) => {\n  if (!win || win.isDestroyed()) return;\n\n  const fileMenu = Menu.buildFromTemplate([\n    {\n      label: \"导入项目...\",\n      click: () => {\n        win?.webContents.send(\"menu-triggered-import-project\");\n      },\n    },\n    {\n      label: \"打开文件...\",\n      click: () => {\n        win?.webContents.send(\"menu-triggered-open-file\");\n      },\n    },\n    { type: \"separator\" },\n    {\n      label: \"退出\",\n      click: () => app.quit(),\n    },\n  ]);\n\n  // Fix: Remove position parameter and just use window\n  fileMenu.popup({ window: win });\n});\n\nipcMain.on(\"show-edit-menu\", (event, position) => {\n  if (!win || win.isDestroyed()) return;\n\n  const editMenu = Menu.buildFromTemplate([\n    { label: \"撤销\", role: \"undo\" },\n    { label: \"重做\", role: \"redo\" },\n    { type: \"separator\" },\n    { label: \"剪切\", role: \"cut\" },\n    { label: \"复制\", role: \"copy\" },\n    { label: \"粘贴\", role: \"paste\" },\n    { type: \"separator\" },\n    { label: \"全选\", role: \"selectAll\" },\n  ]);\n\n  // Fix: Remove position parameter and just use window\n  editMenu.popup({ window: win });\n});\n\nipcMain.on(\"show-view-menu\", (event, position) => {\n  if (!win || win.isDestroyed()) return;\n\n  const viewMenu = Menu.buildFromTemplate([\n    { label: \"放大\", role: \"zoomIn\" },\n    { label: \"默认大小\", role: \"resetZoom\" },\n    { label: \"缩小\", role: \"zoomOut\" },\n    { type: \"separator\" },\n    {\n      label: win?.isFullScreen() ? \"退出全屏\" : \"进入全屏\",\n      role: \"togglefullscreen\",\n    },\n  ]);\n\n  // Fix: Remove position parameter and just use window\n  viewMenu.popup({ window: win });\n});\n\nipcMain.on(\"show-dev-menu\", (event, position) => {\n  if (!win || win.isDestroyed() || !isDev) return;\n\n  const devMenu = Menu.buildFromTemplate([\n    { label: \"开发者工具\", role: \"toggleDevTools\" },\n    { label: \"强制刷新\", role: \"forceReload\" },\n  ]);\n\n  // Fix: Remove position parameter and just use window\n  devMenu.popup({ window: win });\n});\n\nipcMain.on(\"show-about-menu\", (event, position) => {\n  if (!win || win.isDestroyed()) return;\n\n  const aboutMenu = Menu.buildFromTemplate([\n    {\n      label: \"关于应用\",\n      click: () => {\n        dialog.showMessageBox(win!, {\n          title: \"关于应用\",\n          message: \"ML Desktop\",\n          detail: \"版本 1.0.0\\n一个机器学习数据处理和建模的桌面应用。\",\n          buttons: [\"确定\"],\n          type: \"info\",\n        });\n      },\n    },\n  ]);\n\n  // Fix: Remove position parameter and just use window\n  aboutMenu.popup({ window: win });\n});\n\n// Handle showing open dialog\nipcMain.handle(\"dialog:showOpenDialog\", async (event, options) => {\n  if (!win) {\n    return { canceled: true, filePaths: [] };\n  }\n  return await dialog.showOpenDialog(win, options);\n});\n"], "names": ["exec", "__filename", "fileURLToPath", "__dirname", "dirname", "join", "release", "app", "<PERSON><PERSON>", "BrowserWindow", "url", "ipcMain", "dialog", "require", "createRequire"], "mappings": ";;;;;;;;;;;AAGA,eAAsB,8BACpB,cAAsB,iBACH;AACnB,SAAO,IAAI,QAAQ,CAAC,YAAY;AAE9B,QAAI,QAAQ,aAAa,SAAS;AAChC,cAAQ,CAAA,CAAE;AACV;AAAA,IACF;AAIA,UAAM,MAAM,6BAA6B,WAAW;AAEpDA,uBAAAA,KAAK,KAAK,CAAC,OAAO,WAAW;AAC3B,UAAI,OAAO;AACT,gBAAQ;AAAA,UACN;AAAA,QAAA;AAEF,gBAAQ,CAAA,CAAE;AACV;AAAA,MACF;AAGA,YAAM,aAAa;AACnB,YAAM,OAAiB,CAAA;AACvB,UAAI;AAEJ,cAAQ,QAAQ,WAAW,KAAK,MAAM,OAAO,MAAM;AACjD,cAAM,MAAM,SAAS,MAAM,CAAC,GAAG,EAAE;AACjC,YAAI,CAAC,MAAM,GAAG,KAAK,QAAQ,QAAQ,KAAK;AACtC,eAAK,KAAK,GAAG;AAAA,QACf;AAAA,MACF;AAEA,cAAQ,IAAI,SAAS,KAAK,MAAM,gCAAgC,IAAI;AACpE,cAAQ,IAAI;AAAA,IACd,CAAC;AAAA,EACH,CAAC;AACH;AAIA,eAAsB,iCAAoD;AACxE,SAAO,IAAI,QAAQ,CAAC,YAAY;AAC9B,QAAI,QAAQ,aAAa,SAAS;AAChC,cAAQ,CAAA,CAAE;AACV;AAAA,IACF;AAEA,UAAM,MACJ;AAEFA,uBAAAA,KAAK,KAAK,CAAC,OAAO,WAAW;AAC3B,UAAI,OAAO;AACT,gBAAQ;AAAA,UACN;AAAA,QAAA;AAEF,gBAAQ,CAAA,CAAE;AACV;AAAA,MACF;AAGA,YAAM,eAAe,OAAO,KAAA,EAAO,MAAM,IAAI,EAAE,MAAM,CAAC;AACtD,YAAM,OAAiB,CAAA;AAEvB,iBAAW,QAAQ,cAAc;AAC/B,cAAM,QAAQ,KAAK,KAAA,EAAO,MAAM,cAAc;AAC9C,YAAI,OAAO;AACT,gBAAM,MAAM,SAAS,MAAM,CAAC,GAAG,EAAE;AACjC,gBAAM,OAAO,MAAM,CAAC,EAAE,KAAA;AACtB,cAAI,CAAC,MAAM,GAAG,KAAK,QAAQ,QAAQ,KAAK;AACtC,oBAAQ;AAAA,cACN,mCAAmC,IAAI,UAAU,GAAG;AAAA,YAAA;AAEtD,iBAAK,KAAK,GAAG;AAAA,UACf;AAAA,QACF;AAAA,MACF;AAEA,cAAQ,IAAI,SAAS,KAAK,MAAM,8BAA8B;AAC9D,cAAQ,IAAI;AAAA,IACd,CAAC;AAAA,EACH,CAAC;AACH;ACrDA,MAAMC,eAAaC,SAAAA,+PAA6B;AAChD,MAAMC,cAAYC,UAAAA,QAAQH,YAAU;AACpC,QAAA,IAAY,gBAAgBI,eAAKF,aAAW,IAAI;AAChD,QAAA,IAAY,OAAOE,UAAAA,KAAK,QAAA,IAAY,eAAe,SAAS;AAC5D,QAAA,IAAY,SAAS,YAAY,sBAC7BA,UAAAA,KAAK,YAAY,eAAe,WAAW,IAC3C,QAAA,IAAY;AAEhB,MAAM,QAAQ,QAAA,IAAY,UAAU,MAAM;AAG1C,IAAIC,QAAAA,UAAU,WAAW,KAAK,gBAAO,4BAAA;AAGrC,IAAI,QAAQ,aAAa,sBAAa,kBAAkBC,SAAAA,IAAI,SAAS;AAErE,IAAI,CAACA,SAAAA,IAAI,6BAA6B;AACpCA,WAAAA,IAAI,KAAA;AACJ,UAAQ,KAAK,CAAC;AAChB;AAOA,IAAI,MAA4B;AAGhC,MAAM,0BAAoC,CAAA;AAE1C,IAAI,iBAAiB;AAGrB,MAAM,iDAAmD,IAAA;AAGzD,MAAM,UAAUF,UAAAA,KAAKF,aAAW,qBAAqB;AACrD,MAAM,MAAM,QAAA,IAAY;AACxB,MAAM,YAAYE,UAAAA,KAAK,QAAA,IAAY,MAAM,YAAY;AAGrD,eAAe,YAAY,KAA+B;AACxD,SAAO,IAAI,QAAQ,CAAC,YAAY;AAC9B,QAAI,CAAC,OAAO,OAAO,GAAG;AACpB,cAAQ,KAAK;AACb;AAAA,IACF;AAEA,YAAQ,IAAI,wCAAwC,GAAG,EAAE;AAEzD,QAAI;AAEF,UAAI,QAAQ,aAAa,SAAS;AAChCL,2BAAAA,KAAK,iBAAiB,GAAG,UAAU,CAAC,QAAQ;AAC1C,cAAI,KAAK;AACP,oBAAQ,MAAM,0BAA0B,GAAG,KAAK,GAAG;AACnD,oBAAQ,KAAK;AAAA,UACf,OAAO;AACL,oBAAQ,IAAI,mCAAmC,GAAG,EAAE;AAEpD,kBAAM,QAAQ,wBAAwB,QAAQ,GAAG;AACjD,gBAAI,UAAU,IAAI;AAChB,sCAAwB,OAAO,OAAO,CAAC;AAAA,YACzC;AACA,oBAAQ,IAAI;AAAA,UACd;AAAA,QACF,CAAC;AAAA,MACH,OAAO;AAEL,YAAI;AACF,kBAAQ,KAAK,KAAK,SAAS;AAC3B,qBAAW,MAAM;AACf,gBAAI;AAEF,sBAAQ,KAAK,KAAK,CAAC;AAEnB,sBAAQ,KAAK,KAAK,SAAS;AAC3B,sBAAQ,IAAI,0BAA0B,GAAG,EAAE;AAAA,YAC7C,SAAS,GAAG;AAAA,YAEZ;AAGA,kBAAM,QAAQ,wBAAwB,QAAQ,GAAG;AACjD,gBAAI,UAAU,IAAI;AAChB,sCAAwB,OAAO,OAAO,CAAC;AAAA,YACzC;AAEA,oBAAQ,IAAI;AAAA,UACd,GAAG,GAAI;AAAA,QACT,SAAS,GAAG;AACV,kBAAQ,MAAM,0BAA0B,GAAG,KAAK,CAAC;AACjD,kBAAQ,KAAK;AAAA,QACf;AAAA,MACF;AAAA,IACF,SAAS,OAAO;AACd,cAAQ,MAAM,gCAAgC,GAAG,KAAK,KAAK;AAC3D,cAAQ,KAAK;AAAA,IACf;AAAA,EACF,CAAC;AACH;AAwOA,eAAe,+BAA+B;AAC5C,UAAQ,IAAI,sCAAsC;AAiBlD,QAAM,YAAY,CAAC,GAAG,uBAAuB;AAC7C,aAAW,OAAO,WAAW;AAC3B,UAAM,YAAY,GAAG;AAAA,EACvB;AAGA,QAAM,gBAAgB,MAAM,8BAAA;AAC5B,aAAW,OAAO,eAAe;AAC/B,UAAM,YAAY,GAAG;AAAA,EACvB;AACF;AAGA,eAAe,gCAAgC;AAC7C,UAAQ,IAAI,uCAAuC;AAgBnD,MAAI;AACF,UAAM,gBAAgB,MAAM,+BAAA;AAC5B,YAAQ;AAAA,MACN,SAAS,cAAc,MAAM;AAAA,IAAA;AAE/B,eAAW,OAAO,eAAe;AAC/B,YAAM,YAAY,GAAG;AAAA,IACvB;AAAA,EACF,SAAS,OAAO;AACd,YAAQ,MAAM,oDAAoD,KAAK;AAAA,EACzE;AACF;AAGA,SAAS,WAAW,QAAQ,SAAS;AACnC,QAAM,OAAOQ,SAAAA,KAAK;AAAA,IAChB,QAAQ,KAAK;AAAA,EAAA;AAEfA,WAAAA,KAAK,mBAAmB,IAAI;AAC9B;AAEA,eAAe,iBAAiB,cAAuB;AAErD,QAAM,IAAIC,SAAAA,cAAc;AAAA,IACtB,OAAO;AAAA,IACP,QAAQ;AAAA,IACR,UAAU;AAAA,IACV,WAAW;AAAA,IACX,OAAO;AAAA,IACP,MAAMJ,UAAAA,KAAK,QAAA,IAAY,QAAQ,aAAa;AAAA,IAC5C,OAAO;AAAA;AAAA,IACP,aAAa;AAAA;AAAA,IACb,WAAW;AAAA;AAAA,IACX,gBAAgB;AAAA,MACd;AAAA,MACA,iBAAiB;AAAA;AAAA,MACjB,kBAAkB;AAAA;AAAA,IAAA;AAAA,EACpB,CACD;AAGD,MAAI,SAAA;AAEJ,QAAM,YAAY,eAAe,GAAG,GAAG,IAAI,YAAY,KAAK;AAC5D,QAAM,kBAAkB,eACpB,EAAE,UAAU,WAAW,MAAM,iBAC7B;AAEJ,MAAI,YAAY,qBAAqB;AAEnC,QAAI,QAAQ,SAAS;AAErB,QAAI,YAAY,aAAa,EAAE,MAAM,UAAU;AAAA,EACjD,OAAO;AACL,QAAI;AAAA,MACF,OAAO,oBAAoB,WACvB,kBACA,gBAAgB;AAAA,MACpB,OAAO,oBAAoB,WAAW,CAAA,IAAK,EAAE,MAAM,gBAAgB,KAAA;AAAA,IAAK;AAAA,EAE5E;AAEA,aAAA;AAGA,MAAI,YAAY,GAAG,mBAAmB,MAAM;AAC1C,+BAAK,YAAY,KAAK,6CAA4B,KAAA,GAAO;EAC3D,CAAC;AAGD,MAAI,YAAY,qBAAqB,CAAC,EAAE,KAAAK,WAAU;AAMhD,UAAM,sBAAsBA,KAAI,SAAS,mBAAmB;AAC5D,UAAM,cAAc,IAAID,uBAAc;AAAA,MACpC,OAAO;AAAA,MACP,QAAQ;AAAA,MACR,UAAU;AAAA,MACV,WAAW;AAAA,MACX,iBAAiB;AAAA;AAAA,MACjB,OAAO,CAAC;AAAA;AAAA,MACR,aAAa;AAAA;AAAA,MACb,iBAAiB,sBAAsB,cAAc;AAAA;AAAA,MACrD,gBAAgB;AAAA,QACd;AAAA,QACA,iBAAiB;AAAA,QACjB,kBAAkB;AAAA,MAAA;AAAA,MAEpB,GAAI,sBACA;AAAA,QACE,MAAM;AAAA;AAAA,MAAA,IAER,CAAA;AAAA,IAAC,CACN;AAED,gBAAY,QAAQC,IAAG;AAGvB,QAAI,qBAAqB;AACvB,kBAAY,KAAK,iBAAiB,MAAM;AACtC,oBAAY,KAAA;AAAA,MACd,CAAC;AAAA,IACH;AAEA,WAAO,EAAE,QAAQ,OAAA;AAAA,EACnB,CAAC;AAID,MAAI,GAAG,qBAAqB,MAAM;AAChC,eAAW,OAAO;AAAA,EACpB,CAAC;AAGD,MAAI,GAAG,qBAAqB,MAAM;AAChC,eAAA;AAAA,EACF,CAAC;AACH;AAGA,MAAM,oCAAoB,IAAA;AAG1B,MAAM,iBAAiB,CAAC,WAAmB,SAAc;AACvD,UAAQ,IAAI,eAAe,SAAS,YAAY,IAAI;AACpD,UAAQ,IAAI,eAAe,SAAS,iBAAiB,OAAO,KAAK,OAAO;AACxE,UAAQ,IAAI,eAAe,SAAS,gBAAgB,KAAK,OAAO;AAChE,MAAI,KAAK,YAAY,QAAW;AAC9B,YAAQ,IAAI,eAAe,SAAS,oBAAoB;AAAA,EAC1D;AACF;AAkCAC,SAAAA,QAAQ,GAAG,yBAAyB,CAAC,OAAO,SAAS;AACnD,iBAAe,yBAAyB,IAAI;AAE5C,QAAM,UAAU,KAAK;AACrB,QAAM,eAAe,cAAc,IAAI,OAAO;AAE9C,UAAQ;AAAA,IACN,4BAA4B,OAAO,2BAA2B,CAAC,CAAC,YAAY;AAAA,EAAA;AAG9E,MAAI,gBAAgB,CAAC,aAAa,eAAe;AAC/C,iBAAa,YAAY,KAAK,yBAAyB,IAAI;AAAA,EAC7D,OAAO;AACL,YAAQ;AAAA,MACN;AAAA,MACA,MAAM,KAAK,cAAc,KAAA,CAAM;AAAA,IAAA;AAAA,EAEnC;AACF,CAAC;AAGDA,SAAAA,QAAQ,GAAG,kBAAkB,CAAC,OAAO,SAAS;AAC5C,UAAQ,IAAI,IAAI;AAChB,QAAM,UAAU,KAAK;AACrB,QAAM,eAAe,cAAc,IAAI,OAAO;AAE9C,UAAQ;AAAA,IACN,2BAA2B,OAAO,2BAA2B,CAAC,CAAC,YAAY;AAAA,EAAA;AAI7E,6BAA2B,IAAI,SAAS,IAAI;AAE5C,MAAI,gBAAgB,CAAC,aAAa,eAAe;AAC/C,iBAAa,YAAY,KAAK,kBAAkB,IAAI;AAAA,EACtD;AACF,CAAC;AAGDA,SAAAA,QAAQ,GAAG,4BAA4B,CAAC,OAAO,SAAS;AACtD,iBAAe,4BAA4B,IAAI;AAC/C,QAAM,UAAU,KAAK;AAErB,MAAI,CAAC,SAAS;AACZ,YAAQ,MAAM,eAAe,KAAK,OAAO;AACzC;AAAA,EACF;AAEA,UAAQ;AAAA,IACN,+BAA+B,OAAO;AAAA,EAAA;AAIxC,QAAM,eAAeF,SAAAA,cAAc,gBAAgB,MAAM,MAAM;AAC/D,MAAI,cAAc;AAChB,kBAAc,IAAI,SAAS,YAAY;AAAA,EACzC;AACF,CAAC;AAGDE,SAAAA,QAAQ,GAAG,6BAA6B,CAAC,OAAO,SAAS;AACvD,iBAAe,6BAA6B,IAAI;AAEhD,QAAM,UAAU,KAAK;AAErB,MAAI,CAAC,SAAS;AACZ,YAAQ,MAAM,eAAe,KAAK,OAAO;AACzC;AAAA,EACF;AAEA,UAAQ,IAAI,sCAAsC,OAAO,EAAE;AAG3D,QAAM,eAAeF,SAAAA,cAAc,gBAAgB,MAAM,MAAM;AAC/D,MAAI,cAAc;AAChB,kBAAc,IAAI,SAAS,YAAY;AACvC,YAAQ,IAAI,+CAA+C,OAAO,EAAE;AAGpE,UAAM,cAAc,2BAA2B,IAAI,OAAO;AAC1D,QAAI,aAAa;AACf,mBAAa,YAAY,KAAK,kBAAkB,WAAW;AAC3D,iCAA2B,OAAO,OAAO;AAAA,IAC3C;AAAA,EACF;AACF,CAAC;AAGDE,SAAAA,QAAQ,GAAG,sBAAsB,OAAO,OAAO,SAAS;AACtD,iBAAe,sBAAsB,IAAI;AAEzC,QAAM,UAAU,KAAK;AAErB,MAAI,CAAC,SAAS;AACZ,YAAQ,MAAM,eAAe,KAAK,OAAO;AACzC,UAAM,OAAO,KAAK,uBAAuB;AAAA,MACvC,MAAM;AAAA,MACN,KAAK;AAAA,IAAA,CACN;AACD;AAAA,EACF;AAEA,UAAQ,IAAI,qBAAqB,OAAO,EAAE;AAG1C,QAAM,eAAeF,SAAAA,cAAc,gBAAgB,MAAM,MAAM;AAC/D,MAAI,cAAc;AAChB,kBAAc,IAAI,SAAS,YAAY;AAAA,EACzC;AAGA,MAAI,CAAC,OAAO,IAAI,eAAe;AAC7B,YAAQ,MAAM,QAAQ;AACtB,UAAM,OAAO,KAAK,uBAAuB;AAAA,MACvC,MAAM;AAAA,MACN,KAAK;AAAA,IAAA,CACN;AACD;AAAA,EACF;AAEA,MAAI;AAEF,UAAM,SAAS,MAAM,IAAI,YAAY,kBAAkB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,gEASK,OAAO;AAAA,sDACjB,OAAO;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,KAWxD;AAED,YAAQ,IAAI,kBAAkB,MAAM;AAGpC,UAAM,OAAO,KAAK,uBAAuB,MAAM;AAAA,EACjD,SAAS,OAAY;AACnB,YAAQ,MAAM,oBAAoB,KAAK;AACvC,UAAM,OAAO,KAAK,uBAAuB;AAAA,MACvC,MAAM;AAAA,MACN,KAAK,cAAc,MAAM,WAAW;AAAA,IAAA,CACrC;AAAA,EACH;AACF,CAAC;AAsLDF,SAAAA,IAAI,UAAA,EAAY,KAAK,YAAY;AAC/B,UAAQ,IAAI,iCAAiC;AAwE7C,UAAQ,IAAI,mDAAmD;AAC/D,mBAAiB,UAAU;AAC7B,CAAC;AAEDA,SAAAA,IAAI,GAAG,qBAAqB,MAAM;AAEhC,MAAIE,uBAAc,gBAAgB,WAAW,GAAG;AAE9CF,aAAAA,IAAI,KAAA;AAAA,EACN;AACF,CAAC;AAGDA,SAAAA,IAAI,GAAG,eAAe,OAAO,UAAU;AACrC,QAAM,iBAAiB;AACvB,MAAI,CAAC,kBAAkB,mBAAmB,QAAQ;AAChD,UAAM,eAAA;AACN,qBAAiB;AAEjB,YAAQ,IAAI,sDAAsD;AAGlE,UAAM,uBAAuB,OAC3B,eACA,MACA,YACG;AACH,aAAO,QAAQ,KAAK;AAAA,QAClB,cAAA;AAAA,QACA,IAAI,QAAc,CAAC,YAAY;AAC7B,qBAAW,MAAM;AACf,oBAAQ,KAAK,GAAG,IAAI,8BAA8B,OAAO,IAAI;AAC7D,oBAAA;AAAA,UACF,GAAG,OAAO;AAAA,QACZ,CAAC;AAAA,MAAA,CACF;AAAA,IACH;AAEA,QAAI;AACF,YAAM,QAAQ,IAAI;AAAA,QAChB;AAAA,UACE,YAAY,MAAM,8BAAA;AAAA,UAClB;AAAA,UACA;AAAA,QAAA;AAAA,QAEF;AAAA,UACE,YAAY,MAAM,6BAAA;AAAA,UAClB;AAAA,UACA;AAAA,QAAA;AAAA,MACF,CACD;AACD,cAAQ,IAAI,uCAAuC;AAAA,IACrD,SAAS,OAAO;AACd,cAAQ,MAAM,qCAAqC,KAAK;AAAA,IAC1D;AAGA,QAAI;AACF,YAAM,uBAAuB,MAAM,8BAAA;AACnC,YAAM,wBAAwB,MAAM,+BAAA;AAEpC,YAAM,mBAAmB;AAAA,QACvB,GAAG;AAAA,QACH,GAAG;AAAA,MAAA;AAEL,UAAI,iBAAiB,SAAS,GAAG;AAC/B,gBAAQ;AAAA,UACN,SAAS,iBAAiB,MAAM;AAAA,QAAA;AAElC,cAAM,QAAQ,IAAI,iBAAiB,IAAI,CAAC,QAAQ,YAAY,GAAG,CAAC,CAAC;AAAA,MACnE;AAAA,IACF,SAAS,OAAO;AACd,cAAQ,MAAM,uCAAuC,KAAK;AAAA,IAC5D;AAGA,eAAW,MAAM;AACf,cAAQ,IAAI,4CAA4C;AACxDA,eAAAA,IAAI,KAAK,CAAC;AAAA,IACZ,GAAG,GAAI;AAAA,EACT;AACF,CAAC;AAGD,QAAQ,GAAG,QAAQ,MAAM;AAoEzB,CAAC;AAGD,QAAQ,GAAG,qBAAqB,OAAO,UAAU;AAC/C,UAAQ,MAAM,uBAAuB,KAAK;AAC1C,mBAAiB;AAGjB,QAAM,UAAU,YAAY;AAC1B,YAAQ,IAAI,wDAAwD;AACpE,UAAM,WAAW,CAAA;AAEjB,QAAI;AAEF,YAAM,cAAc,MAAM,8BAAA;AAC1B,iBAAW,OAAO,aAAa;AAC7B,iBAAS,KAAK,YAAY,GAAG,CAAC;AAAA,MAChC;AAGA,YAAM,eAAe,MAAM,+BAAA;AAC3B,iBAAW,OAAO,cAAc;AAC9B,iBAAS,KAAK,YAAY,GAAG,CAAC;AAAA,MAChC;AAEA,YAAM,QAAQ,KAAK;AAAA,QACjB,QAAQ,IAAI,QAAQ;AAAA,QACpB,IAAI,QAAQ,CAAC,YAAY,WAAW,SAAS,GAAI,CAAC;AAAA;AAAA,MAAA,CACnD;AACD,cAAQ,IAAI,6BAA6B;AAAA,IAC3C,SAAS,GAAG;AACV,cAAQ,MAAM,4BAA4B,CAAC;AAAA,IAC7C;AAAA,EACF;AAEA,QAAM,QAAA;AACNA,WAAAA,IAAI,KAAK,CAAC;AACZ,CAAC;AAGD,QAAQ,GAAG,sBAAsB,OAAO,QAAQ,YAAY;AAC1D,UAAQ,MAAM,gCAAgC,MAAM;AAGtD,CAAC;AAEDA,SAAAA,IAAI,GAAG,mBAAmB,MAAM;AAE9B,MAAI,OAAO,CAAC,IAAI,eAAe;AAC7B,QAAI,IAAI,cAAe,KAAI,QAAA;AAC3B,QAAI,MAAA;AAAA,EACN;AACF,CAAC;AAEDA,SAAAA,IAAI,GAAG,YAAY,MAAM;AAGvB,QAAM,aAAaE,SAAAA,cAAc,cAAA;AACjC,MAAI,WAAW,WAAW,GAAG;AAC3B,qBAAiB,UAAU;AAAA,EAC7B,OAAO;AAEL,QAAI,OAAO,CAAC,IAAI,eAAe;AAC7B,UAAI,IAAI,cAAe,KAAI,QAAA;AAC3B,UAAI,MAAA;AAAA,IACN,OAAO;AAEL,iBAAW,CAAC,EAAE,MAAA;AAAA,IAChB;AAAA,EACF;AACF,CAAC;AAGDE,SAAAA,QAAQ;AAAA,EACN;AAAA,EACA,CACE,OACA,OAKI,OACD;AACH,qBAAiB,KAAK,WAAW;AAGjC,QAAI,KAAK,gBAAgB;AACvB,cAAQ,IAAI,8CAA8C;AAAA,QACxD,UAAU,KAAK;AAAA,QACf,aAAa,KAAK;AAAA,QAClB,gBAAgB,KAAK;AAAA,MAAA,CACtB;AAED,YAAM,eAAe,MAAM;;AACzB,gBAAQ,IAAI,wCAAwC;AAEpD,aAAI,UAAK,gBAAL,mBAAkB,SAAS,4BAA4B;AAEzD,kBAAQ,IAAI,iDAAiD;AAC7D,qCAAK,YAAY,KAAK,uBAAuB,KAAK;AAAA,QACpD,OAAO;AAEL,kBAAQ,IAAI,qDAAqD;AACjE,qCAAK,YAAY,KAAK,2BAA2B,KAAK;AAAA,QACxD;AAEA,YAAI,KAAK,gBAAgB;AACvB,kBAAQ,IAAI,kDAAkD;AAC9D,qCAAK,YAAY,KAAK,wBAAwB,KAAK;AAAA,QACrD;AAAA,MACF;AAEA,iCAAK,YAAY,KAAK,mBAAmB;AACzC,iCAAK,YAAY,KAAK,aAAa;AACnC,iBAAW,cAAc,GAAI;AAAA,IAC/B;AAAA,EACF;AACF;AAGA,MAAM,UAAU,CAAC,oBAA4B;AAE3C,QAAM,eAA6C,CAAA;AACnD,MAAI,OAAO;AACT,iBAAa;AAAA,MACX,EAAE,OAAO,SAAS,MAAM,iBAAA;AAAA,MACxB,EAAE,OAAO,QAAQ,MAAM,cAAA;AAAA,IAAc;AAAA,EAEzC;AAEA,QAAM,WAAyD;AAAA,IAC7D;AAAA,MACE,OAAO;AAAA,MACP,SAAS;AAAA,QACP;AAAA,UACE,OAAO;AAAA,UACP,aAAa;AAAA;AAAA,UAEb,SAAS;AAAA,UACT,OAAO,YAAY;AACjB,gBAAI,OAAO,CAAC,IAAI,eAAe;AAC7B,kBAAI,MAAA;AACJ,kBAAI,YAAY,KAAK,+BAA+B;AAAA,YACtD,OAAO;AACL,oBAAM,gBAA4C;AAAA,gBAChD,YAAY,CAAC,eAAe;AAAA,cAAA;AAE9B,oBAAM,sBACJ,MAAMC,gBAAO,eAAe,aAAa;AAE3C,kBACE,CAAC,oBAAoB,YACrB,oBAAoB,UAAU,SAAS,GACvC;AACA,sBAAM,cAAc,oBAAoB,UAAU,CAAC;AACnD;AAAA,kBACE,cAAc,mBAAmB,WAAW,CAAC;AAAA,gBAAA;AAAA,cAEjD;AAAA,YACF;AAAA,UACF;AAAA,QAAA;AAAA,QAEF;AAAA,UACE,OAAO;AAAA,UACP,aAAa;AAAA,UACb,SAAS;AAAA,UACT,OAAO,YAAY;AACjB,gBAAI,OAAO,CAAC,IAAI,eAAe;AAC7B,kBAAI,MAAA;AAGJ,oBAAM,aAAa,IAAI,YAAY,OAAA;AACnC,sBAAQ,IAAI,uBAAuB,UAAU;AAG7C,oBAAM,mBAAmB,WAAW;AAAA,gBAClC;AAAA,cAAA;AAIF,kBAAI,kBAAkB;AACpB,wBAAQ;AAAA,kBACN;AAAA,gBAAA;AAEF,oBAAI,YAAY,KAAK,0BAA0B;AAAA,cACjD,OAAO;AAEL,sBAAM,gBAA4C;AAAA,kBAChD,YAAY,CAAC,UAAU;AAAA,gBAAA;AAEzB,sBAAM,iBACJ,MAAMA,gBAAO,eAAe,aAAa;AAE3C,oBACE,CAAC,eAAe,YAChB,eAAe,UAAU,SAAS,GAClC;AACA,wBAAM,WAAW,eAAe,UAAU,CAAC;AAG3C,wBAAM,cAAc,qBAAqB,KAAK,QAAQ;AAEtD,sBAAI,aAAa;AAEf,qCAAiB,yBAAyB;AAE1C,0BAAM,gBAAgB,MAAM;AAC1B,iDAAK,YAAY,KAAK,uBAAuB;AAC7C,iDAAK,YAAY,KAAK,wBAAwB;AAAA,oBAChD;AACA,+CAAK,YAAY,KAAK,mBAAmB;AACzC,+CAAK,YAAY,KAAK,aAAa;AACnC,+BAAW,eAAe,GAAI;AAAA,kBAChC,OAAO;AAEL,0BAAM,UAAU,SAAS;AAAA,sBACvB;AAAA,sBACA,SAAS,YAAY,GAAG,KAAK,SAAS,YAAY,IAAI;AAAA,oBAAA;AAExD;AAAA,sBACE,cAAc,mBAAmB,OAAO,CAAC;AAAA,oBAAA;AAG3C,0BAAM,oBAAoB,MAAM;AAC9B,iDAAK,YAAY;AAAA,wBACf;AAAA,wBACA;AAAA;AAEF,iDAAK,YAAY,KAAK,wBAAwB;AAAA,oBAChD;AACA,+CAAK,YAAY,KAAK,mBAAmB;AACzC,+CAAK,YAAY,KAAK,aAAa;AAEnC,+BAAW,mBAAmB,GAAI;AAAA,kBACpC;AAAA,gBACF;AAAA,cACF;AAAA,YACF,OAAO;AACL,oBAAM,gBAA4C;AAAA,gBAChD,YAAY,CAAC,UAAU;AAAA,cAAA;AAEzB,oBAAM,iBAAiB,MAAMA,gBAAO,eAAe,aAAa;AAEhE,kBACE,CAAC,eAAe,YAChB,eAAe,UAAU,SAAS,GAClC;AACA,sBAAM,WAAW,eAAe,UAAU,CAAC;AAG3C,sBAAM,cAAc,qBAAqB,KAAK,QAAQ;AAEtD,oBAAI,aAAa;AAEf,mCAAiB,yBAAyB;AAE1C,wBAAM,gBAAgB,MAAM;AAC1B,+CAAK,YAAY,KAAK,uBAAuB;AAC7C,+CAAK,YAAY,KAAK,wBAAwB;AAAA,kBAChD;AACA,6CAAK,YAAY,KAAK,mBAAmB;AACzC,6CAAK,YAAY,KAAK,aAAa;AACnC,6BAAW,eAAe,GAAI;AAAA,gBAChC,OAAO;AAEL,wBAAM,UAAU,SAAS;AAAA,oBACvB;AAAA,oBACA,SAAS,YAAY,GAAG,KAAK,SAAS,YAAY,IAAI;AAAA,kBAAA;AAExD,mCAAiB,cAAc,mBAAmB,OAAO,CAAC,EAAE;AAE5D,wBAAM,oBAAoB,MAAM;AAC9B,+CAAK,YAAY,KAAK,2BAA2B;AACjD,+CAAK,YAAY,KAAK,wBAAwB;AAAA,kBAChD;AACA,6CAAK,YAAY,KAAK,mBAAmB;AACzC,6CAAK,YAAY,KAAK,aAAa;AAEnC,6BAAW,mBAAmB,GAAI;AAAA,gBACpC;AAAA,cACF;AAAA,YACF;AAAA,UACF;AAAA,QAAA;AAAA,QAEF,EAAE,MAAM,YAAA;AAAA,QACR;AAAA,UACE,OAAO;AAAA,UACP,MAAM;AAAA,UACN,aAAa;AAAA,QAAA;AAAA,MACf;AAAA,IACF;AAAA,IAEF;AAAA,MACE,OAAO;AAAA,MACP,SAAS;AAAA,QACP;AAAA,UACE,OAAO;AAAA,UACP,MAAM;AAAA,UACN,aAAa;AAAA,QAAA;AAAA,QAEf;AAAA,UACE,OAAO;AAAA,UACP,MAAM;AAAA,UACN,aAAa;AAAA,QAAA;AAAA,QAEf,EAAE,MAAM,YAAA;AAAA,QACR;AAAA,UACE,OAAO;AAAA,UACP,MAAM;AAAA,UACN,aAAa;AAAA,QAAA;AAAA,QAEf;AAAA,UACE,OAAO;AAAA,UACP,MAAM;AAAA,UACN,aAAa;AAAA,QAAA;AAAA,QAEf;AAAA,UACE,OAAO;AAAA,UACP,MAAM;AAAA,UACN,aAAa;AAAA,QAAA;AAAA,QAEf;AAAA,UACE,OAAO;AAAA,UACP,MAAM;AAAA,UACN,aAAa;AAAA,QAAA;AAAA,QAEf;AAAA,UACE,OAAO;AAAA,UACP,MAAM;AAAA,UACN,aAAa;AAAA,QAAA;AAAA,MACf;AAAA,IACF;AAAA,IAEF;AAAA,MACE,OAAO;AAAA,MACP,SAAS;AAAA,QACP;AAAA,UACE,OAAO;AAAA,UACP,MAAM;AAAA,UACN,aAAa;AAAA,QAAA;AAAA,QAEf;AAAA,UACE,OAAO;AAAA,UACP,MAAM;AAAA,UACN,aAAa;AAAA,QAAA;AAAA,QAEf;AAAA,UACE,OAAO;AAAA,UACP,MAAM;AAAA,UACN,aAAa;AAAA,QAAA;AAAA,QAEf,EAAE,MAAM,YAAA;AAAA,QACR;AAAA,UACE,OAAO;AAAA,UACP,MAAM;AAAA,UACN,aAAa;AAAA,QAAA;AAAA,MACf;AAAA,IACF;AAAA;AAAA,IAGF,GAAI,QACA;AAAA,MACE;AAAA,QACE,OAAO;AAAA,QACP,SAAS;AAAA,MAAA;AAAA,IACX,IAEF,CAAA;AAAA;AAAA,IAEJ;AAAA,MACE,OAAO;AAAA,MACP,SAAS;AAAA,QACP;AAAA,UACE,OAAO;AAAA,UACP,MAAM;AAAA,UACN,aAAa;AAAA,QAAA;AAAA,MACf;AAAA,IACF;AAAA,EACF;AAGF,SAAO;AACT;AAGAD,SAAAA,QAAQ,OAAO,YAAY,CAAC,GAAG,QAAQ;AACrC,QAAM,cAAc,IAAIF,uBAAc;AAAA,IACpC,gBAAgB;AAAA,MACd;AAAA,MACA,iBAAiB;AAAA;AAAA,MACjB,kBAAkB;AAAA,IAAA;AAAA,EACpB,CACD;AAED,MAAI,YAAY,qBAAqB;AACnC,gBAAY,QAAQ,GAAG,GAAG,IAAI,GAAG,EAAE;AAAA,EACrC,OAAO;AACL,gBAAY,SAAS,WAAW,EAAE,MAAM,KAAK;AAAA,EAC/C;AACF,CAAC;AAoDD,MAAMI,YAAUC,YAAAA,+PAA6B;AAK7CH,SAAAA,QAAQ,OAAO,wBAAwB,YAAY;AACjD,QAAM,SAAS,MAAMC,SAAAA,OAAO,eAAe;AAAA,IACzC,YAAY,CAAC,eAAe;AAAA,EAAA,CAC7B;AACD,SAAO,OAAO,UAAU,CAAC;AAC3B,CAAC;AAGDD,SAAAA,QAAQ,OAAO,mBAAmB,YAAY;AAC5C,QAAM,SAAS,MAAMC,SAAAA,OAAO,eAAe;AAAA,IACzC,YAAY,CAAC,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAAA,CAMxB;AACD,MAAI,OAAO,YAAY,OAAO,UAAU,WAAW,GAAG;AACpD,WAAO;AAAA,EACT;AACA,SAAO,OAAO,UAAU,CAAC;AAC3B,CAAC;AAGDD,SAAAA,QAAQ,OAAO,oBAAoB,OAAO,GAAG,YAAoB;AAC/D,QAAM,QAAQ,MAAM,GAAG,SAAS,QAAQ,SAAS,EAAE,eAAe,MAAM;AACxE,SAAO,MAAM,IAAI,CAAC,YAAY;AAAA,IAC5B,MAAM,OAAO;AAAA,IACb,aAAa,OAAO,YAAA;AAAA,IACpB,MAAM,KAAK,KAAK,SAAS,OAAO,IAAI;AAAA,EAAA,EACpC;AACJ,CAAC;AAGDA,SAAAA,QAAQ,OAAO,sBAAsB,OAAO,GAAG,eAAuB;AACpE,QAAM,GAAG,SAAS,MAAM,YAAY,EAAE,WAAW,MAAM;AACvD,SAAO,EAAE,SAAS,KAAA;AACpB,CAAC;AAGDA,SAAAA,QAAQ,OAAO,iBAAiB,OAAO,GAAG,aAAqB;AAC7D,QAAM,GAAG,SAAS,UAAU,UAAU,EAAE;AACxC,SAAO,EAAE,SAAS,KAAA;AACpB,CAAC;AAGDA,SAAAA,QAAQ,OAAO,iBAAiB,OAAO,GAAG,eAAuB;AAC/D,QAAM,QAAQ,MAAM,GAAG,SAAS,KAAK,UAAU;AAC/C,MAAI,MAAM,eAAe;AACvB,UAAM,GAAG,SAAS,MAAM,YAAY,EAAE,WAAW,MAAM;AAAA,EACzD,OAAO;AACL,UAAM,GAAG,SAAS,OAAO,UAAU;AAAA,EACrC;AACA,SAAO,EAAE,SAAS,KAAA;AACpB,CAAC;AAGD,MAAM,cAAc,CAClB,aAC2D;AAC3D,QAAM,MAAM,KAAK,QAAQ,QAAQ,EAAE,YAAA;AAGnC,QAAM,iBAAiB;AAAA,IACrB;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EAAA;AAIF,QAAM,mBAAmB,CAAC,QAAQ,SAAS,QAAQ,SAAS,QAAQ,OAAO;AAG3E,QAAM,gBAAgB,CAAC,MAAM;AAG7B,QAAM,kBAAkB;AAAA,IACtB;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EAAA;AAIF,QAAM,oBAAoB,CAAC,QAAQ,QAAQ,OAAO,QAAQ,KAAK;AAG/D,QAAM,uBAAuB,CAAC,QAAQ,QAAQ,QAAQ,QAAQ,QAAQ,MAAM;AAG5E,QAAM,kBAAkB,CAAC,QAAQ,QAAQ,QAAQ,QAAQ,QAAQ,OAAO;AAExE,MAAI,eAAe,SAAS,GAAG,GAAG;AAChC,WAAO,EAAE,MAAM,KAAK,UAAU,QAAQ,WAAW,KAAA;AAAA,EACnD,WAAW,iBAAiB,SAAS,GAAG,GAAG;AACzC,WAAO,EAAE,MAAM,KAAK,UAAU,UAAU,WAAW,KAAA;AAAA,EACrD,WAAW,cAAc,SAAS,GAAG,GAAG;AACtC,WAAO,EAAE,MAAM,KAAK,UAAU,OAAO,WAAW,KAAA;AAAA,EAClD,WAAW,gBAAgB,SAAS,GAAG,GAAG;AACxC,WAAO,EAAE,MAAM,KAAK,UAAU,SAAS,WAAW,KAAA;AAAA,EACpD,WAAW,kBAAkB,SAAS,GAAG,GAAG;AAC1C,WAAO,EAAE,MAAM,KAAK,UAAU,WAAW,WAAW,MAAA;AAAA,EACtD,WAAW,qBAAqB,SAAS,GAAG,GAAG;AAC7C,WAAO,EAAE,MAAM,KAAK,UAAU,cAAc,WAAW,MAAA;AAAA,EACzD,WAAW,gBAAgB,SAAS,GAAG,GAAG;AACxC,WAAO,EAAE,MAAM,KAAK,UAAU,SAAS,WAAW,MAAA;AAAA,EACpD,OAAO;AACL,WAAO,EAAE,MAAM,KAAK,UAAU,WAAW,WAAW,MAAA;AAAA,EACtD;AACF;AAGAA,SAAAA,QAAQ,OAAO,eAAe,OAAO,GAAG,aAAqB;AAC3D,MAAI;AACF,UAAM,UAAU,MAAM,GAAG,SAAS,SAAS,UAAU,OAAO;AAC5D,WAAO;AAAA,EACT,SAAS,OAAO;AACd,YAAQ,MAAM,uBAAuB,KAAK;AAC1C,UAAM;AAAA,EACR;AACF,CAAC;AAGDA,SAAAA,QAAQ,OAAO,uBAAuB,OAAO,GAAG,aAAqB;AACnE,MAAI;AACF,UAAM,WAAW,YAAY,QAAQ;AAErC,QAAI,CAAC,SAAS,WAAW;AACvB,aAAO;AAAA,QACL,SAAS;AAAA,QACT;AAAA,QACA,OAAO,aAAa,SAAS,IAAI;AAAA,QACjC,SAAS,sBAAsB,QAAQ;AAAA,MAAA;AAAA,IAE3C;AAEA,QAAI,UAAU;AACd,QAAI,YAAY;AAEhB,QAAI,SAAS,aAAa,QAAQ;AAEhC,gBAAU,MAAM,GAAG,SAAS,SAAS,UAAU,OAAO;AAAA,IACxD,WAAW,SAAS,aAAa,UAAU;AAEzC,UAAI,SAAS,SAAS,SAAS;AAC7B,kBAAU,MAAM,gBAAgB,QAAQ;AAAA,MAC1C,WAAW,SAAS,SAAS,QAAQ;AACnC,kBAAU;AAAA,MACZ,OAAO;AACL,kBAAU,kBAAkB,SAAS,IAAI;AAAA,MAC3C;AAAA,IACF,WAAW,SAAS,aAAa,OAAO;AACtC,gBAAU;AAAA,IACZ,WAAW,SAAS,aAAa,SAAS;AAExC,YAAM,cAAc,MAAM,GAAG,SAAS,SAAS,QAAQ;AACvD,YAAM,aAAa,YAAY,SAAS,QAAQ;AAChD,YAAM,WAAW,YAAY,SAAS,IAAI;AAC1C,kBAAY,QAAQ,QAAQ,WAAW,UAAU;AACjD,gBAAU;AAAA,IACZ;AAEA,WAAO;AAAA,MACL,SAAS;AAAA,MACT;AAAA,MACA;AAAA,MACA;AAAA,IAAA;AAAA,EAEJ,SAAS,OAAY;AACnB,YAAQ,MAAM,iCAAiC,KAAK;AACpD,WAAO;AAAA,MACL,SAAS;AAAA,MACT,UAAU,YAAY,QAAQ;AAAA,MAC9B,QAAO,+BAAO,YAAW;AAAA,MACzB,SAAS;AAAA,IAAA;AAAA,EAEb;AACF,CAAC;AAGD,MAAM,cAAc,CAAC,cAA8B;AACjD,QAAM,YAAoC;AAAA,IACxC,QAAQ;AAAA,IACR,SAAS;AAAA,IACT,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,SAAS;AAAA,EAAA;AAEX,SAAO,UAAU,UAAU,YAAA,CAAa,KAAK;AAC/C;AAGA,MAAM,wBAAwB,CAAC,aAGzB;AACJ,UAAQ,SAAS,UAAA;AAAA,IACf,KAAK;AACH,aAAO;AAAA,IACT,KAAK;AACH,aAAO;AAAA,IACT,KAAK;AACH,aAAO;AAAA,IACT,KAAK;AACH,aAAO;AAAA,IACT;AACE,aAAO;AAAA,EAAA;AAEb;AAGA,MAAM,kBAAkB,OAAO,aAAsC;AACnE,MAAI;AAEF,QAAI;AACF,YAAM,UAAUE,UAAQ,SAAS;AACjC,YAAM,SAAS,MAAM,QAAQ,eAAe,EAAE,MAAM,UAAU;AAC9D,aAAO,OAAO,SAAS;AAAA,IACzB,SAAS,cAAc;AACrB,cAAQ,IAAI,8CAA8C;AAG1D,UAAI;AACF,cAAM,SAASA,UAAQ,SAAS;AAChC,cAAM,MAAM,IAAI,OAAO,QAAQ;AAC/B,cAAM,cAAc,IAAI,WAAW,mBAAmB;AAEtD,YAAI,aAAa;AAEf,gBAAM,cAAc,YACjB,QAAQ,YAAY,GAAG,EACvB,QAAQ,QAAQ,GAAG,EACnB,KAAA;AAEH,iBAAO,eAAe;AAAA,QACxB;AAAA,MACF,SAAS,UAAe;AACtB,gBAAQ;AAAA,UACN;AAAA,WACA,qCAAU,YAAW;AAAA,QAAA;AAAA,MAEzB;AAEA,aAAO;AAAA;AAAA,QAAsB,QAAQ;AAAA;AAAA;AAAA;AAAA;AAAA,IACvC;AAAA,EACF,SAAS,OAAY;AACnB,YAAQ,MAAM,+BAA+B,KAAK;AAClD,WAAO;AAAA;AAAA,QAAsB,QAAQ;AAAA;AAAA,iBAAqB,+BAAO,YAAW,eAAe;AAAA,EAC7F;AACF;AAGAF,SAAAA,QAAQ,OAAO,qBAAqB,OAAO,GAAG,aAAqB;AACjE,MAAI;AACF,UAAM,SAAS,MAAM,GAAG,SAAS,SAAS,QAAQ;AAClD,WAAO;AAAA,EACT,SAAS,OAAO;AACd,YAAQ,MAAM,8BAA8B,KAAK;AACjD,UAAM;AAAA,EACR;AACF,CAAC;AAGDA,SAAAA,QAAQ,OAAO,gBAAgB,OAAO,GAAG,UAAkB,YAAoB;AAC7E,MAAI;AACF,UAAM,GAAG,SAAS,UAAU,UAAU,SAAS,OAAO;AACtD,WAAO,EAAE,SAAS,KAAA;AAAA,EACpB,SAAS,OAAO;AACd,YAAQ,MAAM,uBAAuB,KAAK;AAC1C,UAAM;AAAA,EACR;AACF,CAAC;AAGDA,SAAAA,QAAQ;AAAA,EACN;AAAA,EACA,OAAO,GAAG,UAAkB,WAAmB;AAC7C,QAAI;AACF,YAAM,GAAG,SAAS,UAAU,UAAU,MAAM;AAC5C,aAAO,EAAE,SAAS,KAAA;AAAA,IACpB,SAAS,OAAO;AACd,cAAQ,MAAM,8BAA8B,KAAK;AACjD,YAAM;AAAA,IACR;AAAA,EACF;AACF;AAGAA,SAAAA,QAAQ,GAAG,2BAA2B,CAAC,OAAO,aAAqB;AAEjE,MAAI,OAAO,CAAC,IAAI,eAAe;AAC7B,QAAI,YAAY,KAAK,2BAA2B,QAAQ;AAAA,EAC1D;AACF,CAAC;AAGDA,SAAAA,QAAQ,GAAG,uBAAuB,CAAC,OAAO,aAAqB;AAE7D,MAAI,OAAO,CAAC,IAAI,eAAe;AAC7B,QAAI,YAAY,KAAK,uBAAuB,QAAQ;AAAA,EACtD;AACF,CAAC;AAGDA,SAAAA,QAAQ,GAAG,YAAY,MAAM;AAC3BJ,WAAAA,IAAI,KAAA;AACN,CAAC;AAGDI,SAAAA,QAAQ,GAAG,mBAAmB,MAAM;AAClC,MAAI,OAAO,CAAC,IAAI,eAAe;AAC7B,QAAI,SAAA;AAAA,EACN;AACF,CAAC;AAEDA,SAAAA,QAAQ,GAAG,mBAAmB,MAAM;AAClC,MAAI,OAAO,CAAC,IAAI,eAAe;AAC7B,QAAI,IAAI,eAAe;AACrB,UAAI,WAAA;AAAA,IACN,OAAO;AACL,UAAI,SAAA;AAAA,IACN;AAAA,EACF;AACF,CAAC;AAEDA,SAAAA,QAAQ,GAAG,gBAAgB,MAAM;AAC/B,MAAI,OAAO,CAAC,IAAI,eAAe;AAC7B,QAAI,MAAA;AAAA,EACN;AACF,CAAC;AAGDA,SAAAA,QAAQ,GAAG,2BAA2B,CAAC,UAAU;AAC/C,QAAM,gBAAgBF,SAAAA,cAAc,gBAAgB,MAAM,MAAM;AAChE,MAAI,iBAAiB,CAAC,cAAc,eAAe;AACjD,kBAAc,SAAA;AAAA,EAChB;AACF,CAAC;AAEDE,SAAAA,QAAQ,GAAG,2BAA2B,CAAC,UAAU;AAC/C,QAAM,gBAAgBF,SAAAA,cAAc,gBAAgB,MAAM,MAAM;AAChE,MAAI,iBAAiB,CAAC,cAAc,eAAe;AACjD,QAAI,cAAc,eAAe;AAC/B,oBAAc,WAAA;AAAA,IAChB,OAAO;AACL,oBAAc,SAAA;AAAA,IAChB;AAAA,EACF;AACF,CAAC;AAEDE,SAAAA,QAAQ,GAAG,wBAAwB,CAAC,UAAU;AAC5C,QAAM,gBAAgBF,SAAAA,cAAc,gBAAgB,MAAM,MAAM;AAChE,MAAI,iBAAiB,CAAC,cAAc,eAAe;AACjD,kBAAc,MAAA;AAAA,EAChB;AACF,CAAC;AAGDE,SAAAA,QAAQ,GAAG,kBAAkB,CAAC,OAAO,aAAa;AAChD,MAAI,CAAC,OAAO,IAAI,cAAe;AAE/B,QAAM,WAAWH,SAAAA,KAAK,kBAAkB;AAAA,IACtC;AAAA,MACE,OAAO;AAAA,MACP,OAAO,MAAM;AACX,mCAAK,YAAY,KAAK;AAAA,MACxB;AAAA,IAAA;AAAA,IAEF;AAAA,MACE,OAAO;AAAA,MACP,OAAO,MAAM;AACX,mCAAK,YAAY,KAAK;AAAA,MACxB;AAAA,IAAA;AAAA,IAEF,EAAE,MAAM,YAAA;AAAA,IACR;AAAA,MACE,OAAO;AAAA,MACP,OAAO,MAAMD,SAAAA,IAAI,KAAA;AAAA,IAAK;AAAA,EACxB,CACD;AAGD,WAAS,MAAM,EAAE,QAAQ,IAAA,CAAK;AAChC,CAAC;AAEDI,SAAAA,QAAQ,GAAG,kBAAkB,CAAC,OAAO,aAAa;AAChD,MAAI,CAAC,OAAO,IAAI,cAAe;AAE/B,QAAM,WAAWH,SAAAA,KAAK,kBAAkB;AAAA,IACtC,EAAE,OAAO,MAAM,MAAM,OAAA;AAAA,IACrB,EAAE,OAAO,MAAM,MAAM,OAAA;AAAA,IACrB,EAAE,MAAM,YAAA;AAAA,IACR,EAAE,OAAO,MAAM,MAAM,MAAA;AAAA,IACrB,EAAE,OAAO,MAAM,MAAM,OAAA;AAAA,IACrB,EAAE,OAAO,MAAM,MAAM,QAAA;AAAA,IACrB,EAAE,MAAM,YAAA;AAAA,IACR,EAAE,OAAO,MAAM,MAAM,YAAA;AAAA,EAAY,CAClC;AAGD,WAAS,MAAM,EAAE,QAAQ,IAAA,CAAK;AAChC,CAAC;AAEDG,SAAAA,QAAQ,GAAG,kBAAkB,CAAC,OAAO,aAAa;AAChD,MAAI,CAAC,OAAO,IAAI,cAAe;AAE/B,QAAM,WAAWH,SAAAA,KAAK,kBAAkB;AAAA,IACtC,EAAE,OAAO,MAAM,MAAM,SAAA;AAAA,IACrB,EAAE,OAAO,QAAQ,MAAM,YAAA;AAAA,IACvB,EAAE,OAAO,MAAM,MAAM,UAAA;AAAA,IACrB,EAAE,MAAM,YAAA;AAAA,IACR;AAAA,MACE,QAAO,2BAAK,kBAAiB,SAAS;AAAA,MACtC,MAAM;AAAA,IAAA;AAAA,EACR,CACD;AAGD,WAAS,MAAM,EAAE,QAAQ,IAAA,CAAK;AAChC,CAAC;AAEDG,SAAAA,QAAQ,GAAG,iBAAiB,CAAC,OAAO,aAAa;AAC/C,MAAI,CAAC,OAAO,IAAI,YAAA,KAAiB,CAAC,MAAO;AAEzC,QAAM,UAAUH,SAAAA,KAAK,kBAAkB;AAAA,IACrC,EAAE,OAAO,SAAS,MAAM,iBAAA;AAAA,IACxB,EAAE,OAAO,QAAQ,MAAM,cAAA;AAAA,EAAc,CACtC;AAGD,UAAQ,MAAM,EAAE,QAAQ,IAAA,CAAK;AAC/B,CAAC;AAEDG,SAAAA,QAAQ,GAAG,mBAAmB,CAAC,OAAO,aAAa;AACjD,MAAI,CAAC,OAAO,IAAI,cAAe;AAE/B,QAAM,YAAYH,SAAAA,KAAK,kBAAkB;AAAA,IACvC;AAAA,MACE,OAAO;AAAA,MACP,OAAO,MAAM;AACXI,iBAAAA,OAAO,eAAe,KAAM;AAAA,UAC1B,OAAO;AAAA,UACP,SAAS;AAAA,UACT,QAAQ;AAAA,UACR,SAAS,CAAC,IAAI;AAAA,UACd,MAAM;AAAA,QAAA,CACP;AAAA,MACH;AAAA,IAAA;AAAA,EACF,CACD;AAGD,YAAU,MAAM,EAAE,QAAQ,IAAA,CAAK;AACjC,CAAC;AAGDD,SAAAA,QAAQ,OAAO,yBAAyB,OAAO,OAAO,YAAY;AAChE,MAAI,CAAC,KAAK;AACR,WAAO,EAAE,UAAU,MAAM,WAAW,CAAA,EAAC;AAAA,EACvC;AACA,SAAO,MAAMC,SAAAA,OAAO,eAAe,KAAK,OAAO;AACjD,CAAC;"}