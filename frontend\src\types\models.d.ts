import { Dataset } from "@/src/types/dataset";
interface ModelMetaEvaluation {
  cv?: {
    k: number;
    randomState: number;
  };
  loocv?: boolean;
  test?: {
    size: number;
    randomState: number;
  };
}
interface ModelMeta {
  headers: {
    index: string[];
    target: string[];
    features: string[];
    all: string[];
  };
  evaluation: ModelMetaEvaluation;
}
interface ModelAlgorithm {
  name: string;
  params: Record<string, any>;
}
interface ModelConfig {
  dataset: Dataset;
  model: {
    algorithm: ModelAlgorithm;
    meta: ModelMeta;
  };
}

interface RegPredictions {
  obs: number[];
  pred: number[];
  err: number[];
  errPer: number[];
}
interface RegMetrics {
  mae: number;
  mre: number;
  mse: number;
  rmse: number;
  r2: number;
  corr: number;
  maxErr: number;
}
interface RegModelResult {
  train: {
    predictions: RegPredictions;
    metrics: RegMetrics;
  };
  test?: {
    predictions: RegPredictions;
    metrics: RegMetrics;
  };
  cv?: {
    predictions: RegPredictions;
    metrics: RegMetrics;
  };
  loocv?: {
    predictions: RegPredictions;
    metrics: RegMetrics;
  };
}

export type {
  ModelConfig,
  RegModelResult,
  ModelMeta,
  ModelMetaEvaluation,
  ModelAlgorithm,
};
