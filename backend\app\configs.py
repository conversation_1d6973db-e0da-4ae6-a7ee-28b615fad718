from pathlib import Path
from app.types.services import RabbitmqHost, RabbitmqPort

class BasicConfig(object):
    
    # 项目路径配置
    BASE_FOLDER = Path(__file__).parent.parent
    STATIC_FOLDER = BASE_FOLDER.joinpath("static")
    TEMP_FOLDER = BASE_FOLDER.joinpath("temp")
    PROJECTS_FOLDER = BASE_FOLDER.joinpath("projects")
    UPLOAD_FOLDER = TEMP_FOLDER.joinpath("uploads")
    LOG_DIR = TEMP_FOLDER / "logs"
    DB_PATH = TEMP_FOLDER / "db.db"
    MODEL_PATH = TEMP_FOLDER / "models"
    RABBITMQ_HOST: RabbitmqHost = "localhost"
    RABBITMQ_PORT: RabbitmqPort = 5672

    SECRET_KEY = "42mldesktop@szw"

    SERVICE_PREFIX = "mldesktop_backend" # 服务器路径统一后缀

    JSON_AS_ASCII = False

    # 文件上传配置
    ALLOWED_EXTENSIONS = {'txt', 'pdf', 'png', 'jpg', 'jpeg', 'gif', 'csv', 'xlsx', 'zip'}
    MAX_CONTENT_LENGTH = 1024 * 1024 * 1024  # limit file size to 1GB
    
dev = BasicConfig()